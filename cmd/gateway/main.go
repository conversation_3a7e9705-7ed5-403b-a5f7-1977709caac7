package main

import (
	"context"
	"errors"
	"log"

	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"golang.org/x/sync/errgroup"
)

func main() {
	// ctx := context.Background()

	// loggerProvider := opentel.NewLogProvider(ctx)

	// // Handle shutdown properly so nothing leaks.
	// defer func() {
	// 	if err := loggerProvider.Shutdown(ctx); err != nil {
	// 		fmt.Println(err)
	// 	}
	// }()

	// global.SetLoggerProvider(loggerProvider)

	// Initialize the config
	initConfig()

	logger.NewLogger()

	// Setup Telemetry
	otelShutdown, err := initOtel(context.Background())
	if err != nil {
		// Add a log line here
		panic(err)
	}
	// Handle telemetry shutdown properly so nothing leaks.
	defer func() {
		err = errors.Join(err, otelShutdown(context.Background()))
	}()

	var g errgroup.Group

	// Initialise the GRPC clients
	grpcClients, err := initGrpcClients()
	if err != nil {
		log.Fatal(err)
	}

	// Initialise the HTTP routers
	httpRouters := initRouters()

	// Initiliase the Modules of the service
	initModules(httpRouters, grpcClients)

	// Run the servers
	runServers(&g)

	if err := g.Wait(); err != nil {
		// Add a log line here
		log.Fatal(err)
	}
}
