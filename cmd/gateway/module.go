package main

import (
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/http_router"
	module "github.com/oneassure-tech/oa-gateway-svc/internal/modules"
)

func initModules(httpRouters map[string]http_router.RouterIface, grpcClients *grpc.GrpcClientManager) {

	// Register subapps created inside the module
	m := module.New()

	m.RegisterApps()
	m.CreateContext(httpRouters, grpcClients)
	m.Initalize()

}
