package main

import (
	"github.com/gin-contrib/cors"
	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/http_router"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/server"
	"golang.org/x/sync/errgroup"
)

var httpServers []*server.HttpServer

// var grpcServers []*server.GrpcServer

func initRouters() map[string]http_router.RouterIface {
	// Setup the public HTTP router
	// publicRouter := http_router.NewHttpRouter(&http_router.RouterInputParams{
	// 	MaxVersion: config.GetConfig().MaxVersion,
	// 	MinVersion: config.GetConfig().MinVersion,
	// })
	// publicCorsCfg := cors.DefaultConfig()
	// publicCorsCfg.AllowOrigins = config.GetConfig().CorsOrigins
	// publicCorsCfg.AllowCredentials = true
	// publicCorsCfg.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With", "Content-Length"}
	// publicCorsCfg.ExposeHeaders = []string{"Content-Length"}
	// publicCorsCfg.AllowWildcard = true
	// publicRouter.GetEngine().Use(cors.New(publicCorsCfg))

	// Setup the global middlewares
	// middleware.InitMiddlewares(publicRouter.GetGlobalRouteGroup(), constant.HTTP_PUBLIC)

	// Configure the HTTPServer
	// publicSrv := server.NewServer(&server.ServerInputParams{
	// 	Port:   config.GetConfig().HttpPublicPort,
	// 	Router: publicRouter,
	// })

	// httpServers = append(httpServers, publicSrv)

	adminRouter := http_router.NewHttpRouter(&http_router.RouterInputParams{
		MaxVersion: config.GetConfig().MaxVersion,
		MinVersion: config.GetConfig().MinVersion,
	})

	middleware.InitMiddlewares(adminRouter.GetGlobalRouteGroup(), constant.HTTP_ADMIN)

	// Configure the HTTPServer
	adminSrv := server.NewServer(&server.ServerInputParams{
		Port:   config.GetConfig().HttpAdminPort,
		Router: adminRouter,
	})

	httpServers = append(httpServers, adminSrv)

	apiCorsCfg := cors.DefaultConfig()
	corsOrigins := config.GetConfig().CorsOrigins
	if len(corsOrigins) == 0 {
		corsOrigins = []string{"*"}
	}
	apiCorsCfg.AllowOrigins = corsOrigins
	apiCorsCfg.AllowCredentials = true
	apiCorsCfg.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With", "Content-Length"}
	apiCorsCfg.ExposeHeaders = []string{"Content-Length"}
	// Remove AllowWildcard when using wildcard origins to avoid conflict
	// apiCorsCfg.AllowWildcard = true
	// Setup the embedded HTTP router
	apiRouter := http_router.NewHttpRouter(&http_router.RouterInputParams{
		MaxVersion: config.GetConfig().MaxVersion,
		MinVersion: config.GetConfig().MinVersion,
		CorsConfig: &apiCorsCfg,
	})

	// Setup the global middlewares
	middleware.InitMiddlewares(apiRouter.GetGlobalRouteGroup(), constant.HTTP_API)

	// Configure the HTTPServer
	apiSrv := server.NewServer(&server.ServerInputParams{
		Port:   config.GetConfig().HttpApiPort,
		Router: apiRouter,
	})

	httpServers = append(httpServers, apiSrv)

	callbackRouter := http_router.NewHttpRouter(&http_router.RouterInputParams{
		MaxVersion: config.GetConfig().MaxVersion,
		MinVersion: config.GetConfig().MinVersion,
	})
	// callbackCorsCfg := cors.DefaultConfig()
	// callbackCorsCfg.AllowOrigins = config.GetConfig().CorsOrigins
	// callbackCorsCfg.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With", "Content-Length"}
	// callbackCorsCfg.ExposeHeaders = []string{"Content-Length"}
	// callbackCorsCfg.AllowWildcard = true
	// callbackRouter.GetEngine().Use(cors.New(callbackCorsCfg))

	// Setup the global middlewares
	middleware.InitMiddlewares(callbackRouter.GetGlobalRouteGroup(), constant.HTTP_CALLBACK)

	callbackSrv := server.NewServer(&server.ServerInputParams{
		Port:   config.GetConfig().HttpCallbackPort,
		Router: callbackRouter,
	})

	httpServers = append(httpServers, callbackSrv)

	rapidshortRouter := http_router.NewHttpRouter(&http_router.RouterInputParams{
		MaxVersion: config.GetConfig().MaxVersion,
		MinVersion: config.GetConfig().MinVersion,
		CorsConfig: &apiCorsCfg,
	})

	rapidshortSrv := server.NewServer(&server.ServerInputParams{
		Port:   config.GetConfig().HttpRapidshortPort,
		Router: rapidshortRouter,
	})

	httpServers = append(httpServers, rapidshortSrv)

	rapidshortAdminRouter := http_router.NewHttpRouter(&http_router.RouterInputParams{
		MaxVersion: config.GetConfig().MaxVersion,
		MinVersion: config.GetConfig().MinVersion,
		CorsConfig: &apiCorsCfg,
	})

	rapidshortAdminSrv := server.NewServer(&server.ServerInputParams{
		Port:   config.GetConfig().HttpRapidshortAdminPort,
		Router: rapidshortAdminRouter,
	})

	httpServers = append(httpServers, rapidshortAdminSrv)

	// Setup the GRPC Router
	// grpcRouter := grpc.NewRouter()
	// grpcRouter.EnableReflection()

	// // Configure the GRPCRouter
	// grpcSrv := server.NewGrpcServer(&server.GrpcServerInputParams{
	// 	Port:   config.GetConfig().GrpcPort,
	// 	Router: grpcRouter,
	// })

	// grpcServers = append(grpcServers, grpcSrv)

	return map[string]http_router.RouterIface{
		constant.HTTP_ADMIN:            adminRouter,
		constant.HTTP_API:              apiRouter,
		constant.HTTP_CALLBACK:         callbackRouter,
		constant.HTTP_RAPIDSHORT:       rapidshortRouter,
		constant.HTTP_RAPIDSHORT_ADMIN: rapidshortAdminRouter,
	}
}

func runServers(g *errgroup.Group) {

	// // Start the server
	// g.Go(func() error {
	// 	// Add a log line here
	// 	return publicSrv.Run()
	// })

	for _, srv := range httpServers {
		g.Go(func() error {
			return srv.Run()
		})
	}

	// for _, srv := range grpcServers {
	// 	g.Go(func() error {
	// 		return srv.Run()
	// 	})
	// }
}
