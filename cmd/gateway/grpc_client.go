package main

import (
	"fmt"

	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	client "github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/health"
	catalog_metadata "github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/metadata"
	"github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/term"

	// "github.com/oneassure-tech/oa-protos/go/oa-affinity-recommendation/v0/credit"
	affinityProposal "github.com/oneassure-tech/oa-protos/go/oa-affinity/v0/proposal"
	affinityRecommendation "github.com/oneassure-tech/oa-protos/go/oa-affinity/v0/recommendation"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"github.com/oneassure-tech/oa-protos/go/oa-enterprise/v0/enterprise"
	"github.com/oneassure-tech/oa-protos/go/oa-forms/v0/forms"
	"github.com/oneassure-tech/oa-protos/go/oa-marketing/v0/creatives"
	rapidshort "github.com/oneassure-tech/oa-protos/go/oa-rapidshort/v0/rapidshort"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
	"github.com/oneassure-tech/oa-protos/go/oa-renewal/v0/renewal"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/leads"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/payouts"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/term_proposal"
)

func initGrpcClients() (*client.GrpcClientManager, error) {
	manager := client.NewGrpcClientManager()

	// Register the recommendation service
	// manager.AddClient(credit.CreditRecommendation_ServiceDesc.ServiceName, &client.ClientConfig{
	// 	ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().AffinityRecommendation.Url, config.GetConfig().AffinityRecommendation.Port),
	// })

	// Register the auth service
	err := manager.AddClient(auth.Auth_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Auth.Url, config.GetConfig().Auth.Port),
	})
	if err != nil {
		return nil, err
	}

	// Register the forms service
	err = manager.AddClient(forms.FormsService_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Forms.Url, config.GetConfig().Forms.Port),
	})
	if err != nil {
		return nil, err
	}

	// Register the recommendation service
	err = manager.AddClient(recommendation.Recommendation_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Recommendation.Url, config.GetConfig().Recommendation.Port),
	})
	if err != nil {
		return nil, err
	}

	// Register the credit sales service
	// manager.AddClient(affinitySales.CreditSales_ServiceDesc.ServiceName, &client.ClientConfig{
	// 	ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().AffinitySales.Url, config.GetConfig().AffinitySales.Port),
	// })

	// Register The Sales Module
	err = manager.AddClient(proposal.Proposal_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Sales.Url, config.GetConfig().Sales.Port),
	})
	if err != nil {
		return nil, err
	}
	err = manager.AddClient(term_proposal.TermProposal_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Sales.Url, config.GetConfig().Sales.Port),
	})
	if err != nil {
		return nil, err
	}
	err = manager.AddClient(leads.Leads_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Sales.Url, config.GetConfig().Sales.Port),
	})
	if err != nil {
		return nil, err
	}

	// Register the Policies service
	err = manager.AddClient(policies.Policies_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Sales.Url, config.GetConfig().Sales.Port),
	})
	if err != nil {
		return nil, err
	}

	err = manager.AddClient(catalog_metadata.MetadataService_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().CATALOGUE.Url, config.GetConfig().CATALOGUE.Port),
	})
	if err != nil {
		return nil, err
	}

	// Register the affinity service
	err = manager.AddClient(affinityRecommendation.Recommendation_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().AffinityService.Url, config.GetConfig().AffinityService.Port),
	})
	if err != nil {
		return nil, err
	}
	err = manager.AddClient(affinityProposal.Proposal_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().AffinityService.Url, config.GetConfig().AffinityService.Port),
	})
	if err != nil {
		return nil, err
	}

	err = manager.AddClient(term_recommendation.TermRecommendation_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Recommendation.Url, config.GetConfig().Recommendation.Port),
	})
	if err != nil {
		return nil, err
	}

	err = manager.AddClient(health.HealthService_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().CATALOGUE.Url, config.GetConfig().CATALOGUE.Port),
	})
	if err != nil {
		return nil, err
	}

	err = manager.AddClient(term.TermService_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().CATALOGUE.Url, config.GetConfig().CATALOGUE.Port),
	})
	if err != nil {
		return nil, err
	}

	err = manager.AddClient(rapidshort.RapidShortService_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().RAPIDSHORT.Url, config.GetConfig().RAPIDSHORT.Port),
	})

	err = manager.AddClient(creatives.CreativesService_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().MARKETING.Url, config.GetConfig().MARKETING.Port),
	})

	if err != nil {
		return nil, err
	}

	if err != nil {
		return nil, err
	}

	err = manager.AddClient(enterprise.Enterprise_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Enterprise.Url, config.GetConfig().Enterprise.Port),
	})
	if err != nil {
		return nil, err
	}

	err = manager.AddClient(payouts.Payouts_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().Sales.Url, config.GetConfig().Sales.Port),
	})
	if err != nil {
		return nil, err
	}

	// Register renewal service
	err = manager.AddClient(renewal.Renewal_ServiceDesc.ServiceName, &client.ClientConfig{
		ServiceURL: fmt.Sprintf("%s:%s", config.GetConfig().RENEWAL.Url, config.GetConfig().RENEWAL.Port),
	})
	if err != nil {
		return nil, err
	}

	return manager, nil
}
