package main

import (
	"log"
	"os"
	"strings"

	"github.com/joho/godotenv"
	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/spf13/viper"
)

func initConfig() {
	// the environment file to read from
	// viper.SetConfigName(".env")
	// viper.SetConfigType("env")
	// viper.AddConfigPath(".")

	if os.Getenv("SKIP_ENV_LOAD") != "true" {
		// Load the environment variables from the .env file
		err := godotenv.Load(".env")
		if err != nil {
			log.Fatalf("Error loading .env file : %v", err)
			panic(err)
		}
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Enable automatic environment variables override
	viper.AutomaticEnv()

	// if err := viper.ReadInConfig(); err != nil {
	// 	// Add a log line here
	// 	log.Fatalf("Error reading config file: %v", err)
	// }

	// Attempt to bind the environment variables to ViperConfig
	if err := viper.Unmarshal(config.New()); err != nil {
		log.Fatalf("Unable to decode into config struct: %v", err)
		panic(err)
	}
}
