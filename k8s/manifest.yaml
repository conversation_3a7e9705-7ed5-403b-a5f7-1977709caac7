apiVersion: apps/v1
kind: Deployment
metadata:
  name: oa-{{app_name}}-deployment-{{env_name}}
  namespace: oa-{{env_name}}
  labels:
    k8s-app: oa-{{app_name}}-{{env_name}}
spec:
  replicas: 2
  revisionHistoryLimit: 1
  progressDeadlineSeconds: 420
  selector:
    matchLabels:
      k8s-app: oa-{{app_name}}-{{env_name}}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        k8s-app: oa-{{app_name}}-{{env_name}}
    spec:
      containers:
      - name: oa-{{app_name}}-{{env_name}}
        image: {{gcr_uri}}/oa-{{app_name}}-{{env_name}}:{{image_id}}
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9000
          name: admin
        - containerPort: 9001
          name: http-public
        - containerPort: 9002
          name: http-callback
        - containerPort: 10000
          name: rapidshort
        - containerPort: 6000
          name: rs-admin
        readinessProbe:
          httpGet:
            path: /v0/healthz
            port: 9001
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /v0/healthz
            port: 9001
          initialDelaySeconds: 15
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          limits:
            cpu: "0.1"
            memory: "256Mi"
          requests:
            cpu: "0.05"
            memory: "128Mi"
        env:
        - name: SERVICE_NAME
          value: "oa-{{app_name}}"
        - name: OTLP_COLLECTOR_GRPC
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: OTLP_COLLECTOR_GRPC
        - name: OTLP_COLLECTOR_HTTP
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: OTLP_COLLECTOR_HTTP
        - name: ENVIRONMENT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: ENVIRONMENT
        - name: HTTP_PUBLIC_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: HTTP_PUBLIC_PORT
        - name: HTTP_ADMIN_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: HTTP_ADMIN_PORT
        - name: HTTP_API_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: HTTP_API_PORT
        - name: JWT_PUBLIC_KEY_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: JWT_PUBLIC_KEY_URL
        - name: MIN_VERSION
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: MIN_VERSION
        - name: MAX_VERSION
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: MAX_VERSION
        - name: CORS_ORIGINS
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: CORS_ORIGINS
        - name: ADMIN_CORS_ORIGINS
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: ADMIN_CORS_ORIGINS
        - name: AUTH_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: AUTH_URL
        - name: AUTH_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: AUTH_PORT
        - name: FORMS_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: FORMS_URL
        - name: FORMS_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: FORMS_PORT
        - name: SALES_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: SALES_URL
        - name: SALES_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: SALES_PORT
        - name: RECOMMENDATION_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: RECOMMENDATION_URL
        - name: RECOMMENDATION_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: RECOMMENDATION_PORT
        - name: CATALOGUE_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: CATALOGUE_URL
        - name: CATALOGUE_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: CATALOGUE_PORT
        - name: HTTP_CALLBACK_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: HTTP_CALLBACK_PORT
        - name: PUBLIC_BASE_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: PUBLIC_BASE_URL
        - name: HTTP_RAPIDSHORT_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: HTTP_RAPIDSHORT_PORT
        - name: RAPIDSHORT_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: RAPIDSHORT_URL
        - name: RAPIDSHORT_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: RAPIDSHORT_PORT
        - name: RAG_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: RAG_URL
        - name: RAG_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: RAG_PORT
        - name: ENTERPRISE_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: ENTERPRISE_URL
        - name: ENTERPRISE_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: ENTERPRISE_PORT
        - name: AFFINITY_SERVICE_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: AFFINITY_SERVICE_URL
        - name: AFFINITY_SERVICE_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: AFFINITY_SERVICE_PORT
        - name: SKIP_ENV_LOAD
          value: "true"
        - name: MARKETING_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: MARKETING_URL
        - name: MARKETING_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: MARKETING_PORT
        - name: HASURA_URL
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: HASURA_URL
        - name: HASURA_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: HASURA_PORT
        - name: HASURA_ADMIN_SECRET
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: HASURA_ADMIN_SECRET
        - name: STORAGE_ROOT_PREFIX
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: STORAGE_ROOT_PREFIX
        - name: STORAGE_DEFAULT_BUCKET
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: STORAGE_DEFAULT_BUCKET
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: AWS_ACCESS_KEY_ID
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: AWS_SECRET_ACCESS_KEY
        - name: AWS_REGION
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: AWS_REGION
        - name: HTTP_RAPIDSHORT_ADMIN_PORT
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: HTTP_RAPIDSHORT_ADMIN_PORT
        - name: RAPIDSHORT_ADMIN_TOKEN
          valueFrom:
            secretKeyRef:
              name: oa-{{app_name}}-secret-{{env_name}}
              key: RAPIDSHORT_ADMIN_TOKEN
---
apiVersion: v1
kind: Service
metadata:
  name: oa-{{app_name}}-service-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  ports:
  - port: 8080
    targetPort: http
    protocol: TCP
    name: http
  - port: 9001
    targetPort: http-public
    protocol: TCP
    name: http-public
  - port: 9000
    targetPort: admin
    protocol: TCP
    name: admin
  - port: 9002
    targetPort: http-callback
    protocol: TCP
    name: http-callback
  - port: 10000
    targetPort: rapidshort
    protocol: TCP
    name: rapidshort
  - port: 6000
    targetPort: rs-admin
    protocol: TCP
    name: rs-admin
  selector:
    k8s-app: oa-{{app_name}}-{{env_name}}
  type: ClusterIP
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: oa-{{app_name}}-httproute-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  parentRefs:
  - kind: Gateway
    name: gke-gateway-pub-{{overarching_env}}
    namespace: entrypoint-pub
  hostnames:
  - "{{public_url}}"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: oa-{{app_name}}-service-{{env_name}}
      port: 9001
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: oa-{{app_name}}-httproute-pvt-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  parentRefs:
  - kind: Gateway
    name: gke-gateway-pvt-{{overarching_env}}
    namespace: entrypoint-pvt
  hostnames:
  - "{{private_url}}"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: oa-{{app_name}}-service-{{env_name}}
      port: 9000
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: oa-{{app_name}}-portal-httproute-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  parentRefs:
  - kind: Gateway
    name: gke-gateway-pub-{{overarching_env}}
    namespace: entrypoint-pub
  hostnames:
  - "portal-new.uat.non-prod.oneassure.in"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /api
    filters:
    - type: URLRewrite
      urlRewrite:
        path:
          type: ReplacePrefixMatch
          replacePrefixMatch: ""
    backendRefs:
    - name: oa-{{app_name}}-service-{{env_name}}
      port: 9001
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: oa-{{app_name}}-callback-httproute-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  parentRefs:
  - kind: Gateway
    name: gke-gateway-pub-{{overarching_env}}
    namespace: entrypoint-pub
  hostnames:
  - "{{callback_url}}"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    filters:
    - type: URLRewrite
      urlRewrite:
        path:
          type: ReplacePrefixMatch
          replacePrefixMatch: "/v1/callback"
    backendRefs:
    - name: oa-{{app_name}}-service-{{env_name}}
      port: 9002
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: oa-{{app_name}}-rapidshort-httproute-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  parentRefs:
  - kind: Gateway
    name: gke-gateway-pub-{{overarching_env}}
    namespace: entrypoint-pub
  hostnames:
  - "{{rapidshort_url}}"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    filters:
    - type: URLRewrite
      urlRewrite:
        path:
          type: ReplacePrefixMatch
          replacePrefixMatch: "/v1/rapidshort"
    backendRefs:
    - name: oa-{{app_name}}-service-{{env_name}}
      port: 10000
  - matches:
    - path:
        type: PathPrefix
        value: /ONEASR
    filters:
    - type: URLRewrite
      urlRewrite:
        path:
          type: ReplacePrefixMatch
          replacePrefixMatch: "/v1/rapidshort"
    backendRefs:
    - name: oa-{{app_name}}-service-{{env_name}}
      port: 10000
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: oa-{{app_name}}-rapidshort-admin-httproute-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  parentRefs:
  - kind: Gateway
    name: gke-gateway-pub-{{overarching_env}}
    namespace: entrypoint-pub
  hostnames:
  - "{{rapidshort_admin_url}}"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: oa-{{app_name}}-service-{{env_name}}
      port: 6000
---
apiVersion: networking.gke.io/v1
kind: HealthCheckPolicy
metadata:
  name: oa-{{app_name}}-healthcheck-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  default:
    checkIntervalSec: 15
    timeoutSec: 10
    healthyThreshold: 2
    unhealthyThreshold: 3
    logConfig:
      enabled: true
    config:
      type: HTTP
      httpHealthCheck:
        portSpecification: USE_FIXED_PORT
        port: 9001
        requestPath: /v0/healthz
  targetRef:
    group: ""
    kind: Service
    name: oa-{{app_name}}-service-{{env_name}}
---
apiVersion: networking.gke.io/v1
kind: GCPBackendPolicy
metadata:
  name: oa-{{app_name}}-backend-policy-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  default:
    timeoutSec: 180
    logging:
      enabled: True
      sampleRate: 1000000
  targetRef:
    group: ""
    kind: Service
    name: oa-{{app_name}}-service-{{env_name}}
