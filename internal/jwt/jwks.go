package jwt

import (
	"crypto/rsa"
	"encoding/base64"
	"fmt"
	"math/big"
)

// J<PERSON><PERSON> represents a JSON Web Key Set
type JWKS struct {
	Keys []JWK `json:"keys"`
}

// J<PERSON><PERSON> represents a JSON Web Key
type JWK struct {
	Kid string   `json:"kid"`
	Kty string   `json:"kty"`           // Key type (RSA, EC, etc.)
	Alg string   `json:"alg"`           // Algorithm (RS256, ES256, etc.)
	Use string   `json:"use"`           // Use (sig for signature, enc for encryption)
	N   string   `json:"n"`             // RSA modulus
	E   string   `json:"e"`             // RSA exponent
	X5c []string `json:"x5c,omitempty"` // X.509 certificate chain
}

// ParseJWKS parses a JWKS JSON string into a JWKS struct
// func ParseJWKS(jwksJSON []byte) (*JWKS, error) {
// 	var jwks JWKS
// 	if err := json.Unmarshal(jwksJSON, &jwks); err != nil {
// 		return nil, fmt.Errorf("failed to parse JWKS: %w", err)
// 	}
// 	return &jwks, nil
// }

// FindKeyByKID finds a JWK in the JWKS by its Key ID
func (jwks *JWKS) FindKeyByKID(kid string) (*JWK, error) {
	for _, key := range jwks.Keys {
		if key.Kid == kid {
			return &key, nil
		}
	}
	return nil, fmt.Errorf("key with ID %s not found in JWKS", kid)
}

// GetRSAPublicKey converts a JWK to an RSA public key
func (jwk *JWK) GetRSAPublicKey() (*rsa.PublicKey, error) {
	if jwk.Kty != "RSA" {
		return nil, fmt.Errorf("key type %s is not supported, only RSA is supported", jwk.Kty)
	}

	// Decode the base64url-encoded modulus
	nBytes, err := base64.RawURLEncoding.DecodeString(jwk.N)
	if err != nil {
		return nil, fmt.Errorf("failed to decode modulus: %w", err)
	}

	// Decode the base64url-encoded exponent
	eBytes, err := base64.RawURLEncoding.DecodeString(jwk.E)
	if err != nil {
		return nil, fmt.Errorf("failed to decode exponent: %w", err)
	}

	// Convert the exponent bytes to an integer
	var eInt int
	for i, b := range eBytes {
		eInt += int(b) << (8 * (len(eBytes) - i - 1))
	}

	// Create an RSA public key
	return &rsa.PublicKey{
		N: new(big.Int).SetBytes(nBytes),
		E: eInt,
	}, nil
}

// JWKSToPublicKeys converts a JWKS to a map of key IDs to RSA public keys
func JWKSToPublicKeys(jwks *JWKS) (map[string]*rsa.PublicKey, error) {
	publicKeys := make(map[string]*rsa.PublicKey)

	for _, jwk := range jwks.Keys {
		if jwk.Kty == "RSA" && (jwk.Use == "sig" || jwk.Use == "") {
			publicKey, err := jwk.GetRSAPublicKey()
			if err != nil {
				return nil, err
			}
			publicKeys[jwk.Kid] = publicKey
		}
	}

	if len(publicKeys) == 0 {
		return nil, fmt.Errorf("no valid RSA signature keys found in JWKS")
	}

	return publicKeys, nil
}

// // GetPublicKeyFromJWKSResponse converts a JWKS response to a public key for JWT verification
// func GetPublicKeyFromJWKSResponse(jwksResponse []byte, kid string) (*rsa.PublicKey, error) {
// 	// Parse the JWKS
// 	jwks, err := ParseJWKS(jwksResponse)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// If kid is provided, find the key with that ID
// 	if kid != "" {
// 		jwk, err := jwks.FindKeyByKID(kid)
// 		if err != nil {
// 			return nil, err
// 		}
// 		return jwk.GetRSAPublicKey()
// 	}

// 	// If no kid is provided, use the first suitable key
// 	for _, jwk := range jwks.Keys {
// 		if jwk.Kty == "RSA" && (jwk.Use == "sig" || jwk.Use == "") {
// 			return jwk.GetRSAPublicKey()
// 		}
// 	}

// 	return nil, fmt.Errorf("no suitable key found in JWKS")
// }
