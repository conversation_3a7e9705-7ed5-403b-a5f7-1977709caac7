package jwt

import (
	"crypto/rsa"
	"fmt"

	"github.com/golang-jwt/jwt/v5"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
)

// Custom claims struct that will be embedded in JWT
// type Claims struct {
// 	jwt.RegisteredClaims
// 	EnterpriseID string   `json:"enterprise_id"`
// 	Scopes       []string `json:"scopes"`
// }

// type JWTConfig struct {
// 	TokenString  string
// 	PublicKeySet map[string]*rsa.PublicKey
// }

// ParseToken parses and validates a JWT token using public keys from the auth service JWKS
// func ParseToken(tokenString string, config *JWTConfig, logger *logger.Logger) (*Claims, error) {
// 	// Extract the token header to get the Key ID (kid)
// 	token, claims, err := InsecureParseToken(tokenString)

// 	if err != nil {
// 		return nil, fmt.Errorf("failed to parse token: %w", err)
// 	}

// 	claims, ok := token.Claims.(*Claims)
// 	if !ok {
// 		return nil, oaerrors.ErrInvalidToken
// 	}
// 	if err != nil {

// 		return nil, oaerrors.ErrUnauthorized
// 	}

// 	// Extract the Key ID from the token header
// 	kid, ok := token.Header["kid"].(string)
// 	if !ok || kid == "" {
// 		return nil, oaerrors.ErrInvalidToken
// 	}

// 	// Create parser options
// 	var opts []jwt.ParserOption
// 	opts = append(opts, jwt.WithIssuedAt())
// 	opts = append(opts, jwt.WithValidMethods([]string{jwt.SigningMethodPS256.Name}))
// 	opts = append(opts, jwt.WithAudience("gateway"))
// 	opts = append(opts, jwt.WithIssuer("authentication"))

// 	// Create parser with options
// 	parser := jwt.NewParser(opts...)

// 	// Prepare claims
// 	claims := &Claims{}

// 	// Get the public key for this kid from the auth service
// 	publicKey, err := GetPublicKeyByKID(kid, config.PublicKeySet)
// 	if err != nil {
// 		return nil, oaerrors.ErrUnauthorized
// 	}

// 	// Parse and validate the token with the public key
// 	_, err = parser.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {

// 		// Return the RSA public key for validation
// 		return publicKey, nil
// 	})

// 	if err != nil {
// 		// Check for specific error types
// 		logger.Info("Failed to parse token", zap.Error(err))
// 		if errors.Is(err, jwt.ErrTokenExpired) {
// 			return nil, oaerrors.ErrTokenExpired
// 		}
// 		return nil, oaerrors.ErrInvalidToken
// 	}

// 	return claims, nil
// }

// // GetPublicKeysFromAuthService retrieves the JWKS from the auth service and converts it to RSA public keys
// func VerifyTokens(accessTokenString *string, refreshTokenString *string) (bool, error) {

// 	_, accessTokenClaims, err := InsecureParseToken(*accessTokenString)
// 	if err != nil {
// 		return false, fmt.Errorf("failed to parse access token: %w", err)
// 	}

// 	accessTokenId := (*accessTokenClaims)["jti"].(string)

// 	var refreshTokenId string
// 	var refreshToken *jwt.Token
// 	if refreshTokenString != nil {
// 		r, refreshTokenClaims, err := InsecureParseToken(*refreshTokenString)
// 		if err != nil {
// 			return false, fmt.Errorf("failed to parse refresh token: %w", err)
// 		}
// 		refreshTokenId = (*refreshTokenClaims)["jti"].(string)
// 		refreshToken = r

// 		if refreshTokenId != accessTokenId {
// 			return false, errors.New("refresh and access token id mismatch")
// 		}
// 	}

// 	// Get gRPC connection to auth service
// 	authClient := auth.NewAuthClient(client.GetConnection())

// 	// Get JWKS response from auth service
// 	response, err := authClient.GetJWKS(context.Background(), &auth.GetJWKSRequest{
// 		TokenId: accessTokenId,
// 	})
// 	if err != nil {
// 		return false, fmt.Errorf("failed to get JWKS: %w", err)
// 	}

// 	if response.IsTokenBlacklisted {
// 		return false, nil
// 	}

// 	// Convert the protobuf JWKS response to our JWKS structure

// 	// Get kid from access token
// 	// accessTokenKid, ok := accessToken.Header["kid"].(string)
// 	// if !ok || accessTokenKid == "" {
// 	// 	return false, fmt.Errorf("invalid access token due to missing kid")
// 	// }

// 	// // Get kid from refresh token
// 	// refreshTokenKid, ok := refreshToken.Header["kid"].(string)
// 	// if !ok || refreshTokenKid == "" {
// 	// 	return false, fmt.Errorf("invalid refresh token due to missing kid")
// 	// }

// 	// var refreshTokenPublicKey *rsa.PublicKey
// 	// if refreshToken != nil {
// 	// 	refreshTokenPublicKey, err = GetPublicKeyByKID(refreshTokenKid, publicKeys)
// 	// 	if err != nil {
// 	// 		return false, fmt.Errorf("failed to get public key: %w", err)
// 	// 	}
// 	// }

// 	// Create parser options
// 	var opts []jwt.ParserOption
// 	opts = append(opts, jwt.WithIssuedAt())
// 	opts = append(opts, jwt.WithValidMethods([]string{jwt.SigningMethodPS256.Name}))
// 	opts = append(opts, jwt.WithAudience("gateway"))
// 	opts = append(opts, jwt.WithIssuer("authentication"))

// 	// Create parser with options
// 	parser := jwt.NewParser(opts...)

// 	retrievePublicKey := func(token *jwt.Token) (interface{}, error) {
// 		kid, ok := token.Header["kid"].(string)
// 		if !ok || kid == "" {
// 			return false, fmt.Errorf("invalid access token due to missing kid")
// 		}

// 		key, err := GetPublicKeyByKID(kid, publicKeys)
// 		if err != nil {
// 			return false, fmt.Errorf("failed to get public key: %w", err)
// 		}

// 		return key, nil
// 	}

// 	// Validate access token
// 	accessTokenExpired := false
// 	_, err = parser.ParseWithClaims(*accessTokenString, &Claims{}, retrievePublicKey)
// 	if err != nil {
// 		if errors.Is(err, jwt.ErrTokenExpired) {
// 			accessTokenExpired = true
// 		} else {
// 			return false, nil
// 		}
// 	}

// 	if !accessTokenExpired {
// 		return true, nil
// 	}

// 	// refreshTokenExpired := false
// 	if accessTokenExpired && refreshToken != nil {

// 		fmt.Println("Attempting to refresh the refresh token because access token expired", *refreshTokenString)
// 		// Validate refresh token
// 		_, err = parser.ParseWithClaims(*refreshTokenString, &Claims{}, retrievePublicKey)
// 		if err != nil {
// 			fmt.Println("Failed to validate refresh token", err)
// 			return false, nil
// 		}

// 		renewedToken, err := authClient.RefreshJWTToken(context.Background(), &auth.RefreshJWTTokenRequest{
// 			RefreshToken: *refreshTokenString,
// 		})

// 		if err != nil {
// 			return false, fmt.Errorf("failed to refresh token: %w", err)
// 		}

// 		fmt.Println("Successfully refreshed the refresh and access tokens", renewedToken)
// 		*accessTokenString = renewedToken.AccessToken
// 		*refreshTokenString = renewedToken.RefreshToken
// 	}

// 	return true, nil
// }

func GetJWTParser(opts ...jwt.ParserOption) *jwt.Parser {

	opts = append(opts, jwt.WithIssuedAt())
	opts = append(opts, jwt.WithValidMethods([]string{jwt.SigningMethodPS256.Name}))
	opts = append(opts, jwt.WithIssuer("authentication"))

	if len(opts) == 0 {
		opts = append(opts, jwt.WithAudience("gateway"))
	}

	parser := jwt.NewParser(opts...)

	return parser
}

func GetPublicKey(keys []*auth.JWK) func(token *jwt.Token) (interface{}, error) {
	return func(token *jwt.Token) (interface{}, error) {
		jwksObj := &JWKS{
			Keys: make([]JWK, len(keys)),
		}

		// Copy each key from the response to our JWKS structure
		for i, key := range keys {
			jwksObj.Keys[i] = JWK{
				Kid: key.Kid,
				Kty: key.Kty,
				Alg: key.Alg,
				Use: key.Use,
				N:   key.N,
				E:   key.E,
				// Note: We're not using Crv, X, Y, K fields here since
				// we're only supporting RSA keys for now
			}
		}

		// Convert the JWKS to a map of key IDs to RSA public keys
		publicKeys, err := JWKSToPublicKeys(jwksObj)
		if err != nil {
			return false, fmt.Errorf("failed to convert JWKS to public keys: %w", err)
		}

		kid, ok := token.Header["kid"].(string)
		if !ok || kid == "" {
			return false, fmt.Errorf("invalid access token due to missing kid")
		}

		key, err := GetPublicKeyByKID(kid, publicKeys)
		if err != nil {
			return false, fmt.Errorf("failed to get public key: %w", err)
		}

		return key, nil
	}
}

func InsecureParseToken(tokenString string) (*jwt.Token, *jwt.MapClaims, error) {
	jwtClaims := make(jwt.MapClaims)

	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &jwtClaims)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse token: %w", err)
	}

	return token, &jwtClaims, nil
}

// GetPublicKeyByKID retrieves a specific RSA public key by its Key ID from the auth service
func GetPublicKeyByKID(kid string, keySet map[string]*rsa.PublicKey) (*rsa.PublicKey, error) {
	publicKey, exists := keySet[kid]
	if !exists {
		return nil, fmt.Errorf("key with ID %s not found in JWKS", kid)
	}

	return publicKey, nil
}

// // GenerateToken creates a new JWT token with the provided claims (helpful for testing)
// func GenerateToken(enterpriseID, userID string, roles []string, expirationTime time.Duration) (string, error) {
// 	cfg := config.GetConfig()

// 	// Create claims with expiration time
// 	claims := &Claims{
// 		EnterpriseID: enterpriseID,
// 		UserID:       userID,
// 		Roles:        roles,
// 		RegisteredClaims: jwt.RegisteredClaims{
// 			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expirationTime)),
// 			IssuedAt:  jwt.NewNumericDate(time.Now()),
// 			NotBefore: jwt.NewNumericDate(time.Now()),
// 		},
// 	}

// 	// Add issuer if configured
// 	if cfg.JWT.Issuer != "" {
// 		claims.Issuer = cfg.JWT.Issuer
// 	}

// 	// Add audience if configured
// 	if len(cfg.JWT.Audience) > 0 {
// 		claims.Audience = cfg.JWT.Audience
// 	}

// 	// Create token
// 	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

// 	// Sign token with secret key
// 	tokenString, err := token.SignedString([]byte(cfg.JWT.SecretKey))
// 	if err != nil {
// 		return "", err
// 	}

// 	return tokenString, nil
// }
