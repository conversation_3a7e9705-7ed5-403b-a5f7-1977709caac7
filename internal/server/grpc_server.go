package server

import (
	"fmt"
	"log"
	"net"

	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
)

type GrpcServer struct {
	Port   uint16
	Router grpc.RouterIface
}

type GrpcServerInputParams struct {
	Port   uint16
	Router grpc.RouterIface
}

func NewGrpcServer(params *GrpcServerInputParams) *GrpcServer {
	return &GrpcServer{
		Port:   params.Port,
		Router: params.Router,
	}
}

func (gs *GrpcServer) Run() error {

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", gs.Port))

	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	return gs.Router.GetRouter().Serve(lis)
}
