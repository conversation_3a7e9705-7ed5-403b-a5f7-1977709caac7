package grpc

import (
	"fmt"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type GrpcClient struct {
	conn   *grpc.ClientConn
	config *ClientConfig
}

type ClientConfig struct {
	ServiceURL    string
	Timeout       time.Duration
	MaxRetries    int
	RetryInterval time.Duration
}

// GrpcClientManager manages GRPC client connections
type GrpcClientManager struct {
	clients map[string]*grpc.ClientConn
}

// NewGrpcClientManager creates a new GRPC client manager
func NewGrpcClientManager() *GrpcClientManager {
	return &GrpcClientManager{
		clients: make(map[string]*grpc.ClientConn),
	}
}

func NewGrpcClient(cfg *ClientConfig) (*GrpcClient, error) {
	conn, err := grpc.NewClient(
		cfg.ServiceURL,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create gRPC connection: %v", err)
	}

	return &GrpcClient{
		conn:   conn,
		config: cfg,
	}, nil
}

func (c *GrpcClient) GetConnection() *grpc.ClientConn {
	return c.conn
}

// AddClient adds a new GRPC client connection
func (m *GrpcClientManager) AddClient(name string, cfg *ClientConfig) error {
	if _, exists := m.clients[name]; exists {
		return fmt.Errorf("client %s already exists", name)
	}

	c, err := NewGrpcClient(cfg)
	if err != nil {
		return fmt.Errorf("failed to create client %s: %w", name, err)
	}

	m.clients[name] = c.GetConnection()
	return nil
}

// Get returns a GRPC client connection by name
func (m *GrpcClientManager) Get(name string) (*grpc.ClientConn, error) {
	conn, exists := m.clients[name]
	if !exists {
		return nil, fmt.Errorf("client %s not found", name)
	}
	return conn, nil
}

// Close closes all GRPC client connections
func (m *GrpcClientManager) Close() {
	for name, conn := range m.clients {
		if err := conn.Close(); err != nil {
			fmt.Printf("Error closing %s client: %v\n", name, err)
		}
	}
}
