package grpc

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-common/v0/common"
	"github.com/oneassure-tech/oa-utilities/go/oa-errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// create a type for downstreamProtocol
type DownstreamProtocol string

const (
	DownstreamProtocolGRPC DownstreamProtocol = "grpc"
	DownstreamProtocolHTTP DownstreamProtocol = "http"
)

// FromGrpcError converts a GRPC error to ErrorResponse
func FromGrpcError(downstreamProtocol DownstreamProtocol, err error) *response.ErrorResponse {
	/*
	* This function helps convert GRPC errors to our generic ErrorResponse.
	* It extracts status codes and problem details from the GRPC error.
	 */

	// Get the status from GRPC error
	st, ok := status.FromError(err)
	if !ok {
		return response.NewInternalServerError()
	}

	// Extract the error details
	details := st.Details()
	if len(details) == 0 {
		return &response.ErrorResponse{
			// Set the status based on the downstream protocol
			// For GRPC downstream, we prioritize GRPC status codes
			// For HTTP downstream, we prioritize HTTP status codes
			// This ensures proper status code handling based on the protocol being used
			Status: &response.Status{
				GrpcStatus: func() int {
					if downstreamProtocol == DownstreamProtocolGRPC {
						return int(st.Code())
					}
					return 0
				}(),
				HttpStatus: func() int {
					if downstreamProtocol == DownstreamProtocolHTTP {
						return grpcCodeToHTTP(st.Code())
					}
					return 0
				}(),
			},
			Problem: &response.Problem{
				Type:  oaerrors.ErrCodeUnexpected.GetCode(),
				Title: oaerrors.ErrCodeUnexpected.GetMessage(),
			},
		}
	}

	// Look for our custom error detail
	for _, detail := range details {
		if grpcErr, ok := detail.(*common.Error); ok {
			return &response.ErrorResponse{
				Status: &response.Status{
					GrpcStatus: func() int {
						if downstreamProtocol == DownstreamProtocolGRPC {
							return int(st.Code())
						}
						return 0
					}(),
					HttpStatus: func() int {
						if downstreamProtocol == DownstreamProtocolHTTP {
							return grpcCodeToHTTP(st.Code())
						}
						return 0
					}(),
					DownstreamStatus: grpcErr.DownstreamStatus,
				},
				Problem: &response.Problem{
					Type:   grpcErr.Type,
					Title:  grpcErr.Title,
					Detail: grpcErr.Detail,
					// Instance:       grpcErr.Instance,
					AdditionalInfo: grpcErr.AdditionalInfo,
				},
			}
		}
	}

	return response.NewInternalServerError()
}

// Helper function to convert GRPC codes to HTTP status codes
func grpcCodeToHTTP(code codes.Code) int {
	switch code {
	case codes.OK:
		return http.StatusOK
	case codes.Canceled:
		return http.StatusRequestTimeout
	case codes.Unknown:
		return http.StatusInternalServerError
	case codes.InvalidArgument:
		return http.StatusBadRequest
	case codes.DeadlineExceeded:
		return http.StatusGatewayTimeout
	case codes.NotFound:
		return http.StatusNotFound
	case codes.AlreadyExists:
		return http.StatusConflict
	case codes.PermissionDenied:
		return http.StatusForbidden
	case codes.Unauthenticated:
		return http.StatusUnauthorized
	case codes.ResourceExhausted:
		return http.StatusTooManyRequests
	case codes.FailedPrecondition:
		return http.StatusPreconditionFailed
	case codes.Aborted:
		return http.StatusConflict
	case codes.OutOfRange:
		return http.StatusBadRequest
	case codes.Unimplemented:
		return http.StatusNotImplemented
	case codes.Internal:
		return http.StatusInternalServerError
	case codes.Unavailable:
		return http.StatusServiceUnavailable
	case codes.DataLoss:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}
