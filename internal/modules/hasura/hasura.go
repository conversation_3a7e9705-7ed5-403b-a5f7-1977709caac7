package hasura

import (
	"context"

	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/hasura/handlers/v1"
)

type HasuraApp struct {
	app.MustEmbedApp
}

func New() app.AppIface {
	return &HasuraApp{}
}

func (a *HasuraApp) SetAppName() string {
	return "hasura"
}

func (a *HasuraApp) Initialize(appName string, appContext *app.AppContext) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	h1 := v1.NewHandler(appContext)

	apiRouter := appContext.Router[constant.HTTP_API]
	// adminRouter := appContext.Router[constant.HTTP_ADMIN]

	requireAuth := false

	apiRouter.RegisterRoute(ctx, appName, "GET", "/health-product-variants",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetHealthProductVariants,
				RequireValidation: false,
				RequireAuth:       &requireAuth,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/health-product-variants/list",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.ListHealthProductVariants,
				RequireValidation: false,
				RequireAuth:       &requireAuth,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/site-health-variants",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.ListSiteHealthVariants,
				RequireValidation: false,
				RequireAuth:       &requireAuth,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/bmi-content",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.BMIContent,
				RequireValidation: false,
				RequireAuth:       &requireAuth,
			}),
		},
	)

}
