package v1

import (
	"net/http"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

const listHealthProductVariantsQuery = `
query MyQuery2($limit: Int) {
  health_product_variants(limit: $limit) {
    id
    variant_name
    variant_slug
  }
}
`

func (h *Handler) ListHealthProductVariants(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Get limit parameter, default to 10 if not provided
	limitParam := ctx.GetQuery("limit")
	limit := 10 // default limit
	
	if limitParam != "" {
		if parsedLimit, err := strconv.Atoi(limitParam); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	variables := map[string]interface{}{
		"limit": limit,
	}

	result, err := h.hasuraClient.ExecuteQuery(listHealthProductVariantsQuery, variables)
	if err != nil {
		ctx.Logger.Error("Failed to fetch health product variants list from Hasura", zap.Error(err))
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusInternalServerError},
			Problem: &response.Problem{Detail: "Failed to fetch health product variants list"},
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: result.Data,
	}, nil
}