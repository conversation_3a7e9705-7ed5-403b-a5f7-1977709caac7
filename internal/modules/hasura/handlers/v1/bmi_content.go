package v1

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

const bmiContentQuery = `
query bmiContentQuery{
  site_bmi_content {
    id
    title
    content
    bmi_based_health_insurance_products {
      id
      title
      points
    }
    bmi_faqs {
      id
      question
      answer
    }
    bmi_for_men_women_children {
      id
      title
      healthy_bmi
      below_normal_bmi
      above_normal_bmi
    }
    bmi_related_insurance_plans {
      id
      title
      points
    }
    bmi_testimonials {
      id
      name
      content
    }
  }
}
`

func (h *Handler) BMIContent(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// id := ctx.GetQuery("id")

	// variables := map[string]interface{}{
	// 	"id": id,
	// }

	result, err := h.hasuraClient.ExecuteQuery(bmiContentQuery, nil)
	if err != nil {
		ctx.Logger.Error("Failed to fetch BMI content from Hasura", zap.Error(err))
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusInternalServerError},
			Problem: &response.Problem{Detail: "Failed to fetch BMI content"},
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: result.Data,
	}, nil
}
