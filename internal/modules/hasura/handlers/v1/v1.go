package v1

import (
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/hasura/client"
)

type Handler struct {
	handler.MustEmbedHandler
	hasuraClient *client.HasuraClient
}

func NewHandler(appCtx *app.AppContext) *Handler {
	hasuraClient := client.NewHasuraClient(appCtx.Config)
	
	return &Handler{
		hasuraClient: hasuraClient,
	}
}