package v1

import (
	"net/http"
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

const healthProductVariantsQuery = `
query MyQuery($slugs: [String!]) {
  health_product_variants(where: {variant_slug: {_in: $slugs}}) {
    best_for
    decision_guide
    id
    subtitle
    specialty
    variant_slug
    variant_name
    product {
      policy_brochure_url
      policy_wording_url
      name
      insurer {
        claim_settlement_ratio
        logo_url
        name
        network_hospital_count
        network_hospital_url
      }
    }
    feature_values {
      value
      compare_feature {
        name
        sequence
      }
    }
  }
  site_comparison_how_is_expert_consultation {
    title
    points
    id
  }
  site_comparison_why_choose_expert_consultation {
    logo_url
    title
    description
    id
  }
}
`

func (h *Handler) GetHealthProductVariants(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	slugsParam := ctx.GetQuery("slugs")
	if slugsParam == "" {
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusBadRequest},
			Problem: &response.Problem{Detail: "slugs query parameter is required"},
		}
	}

	// Split comma-separated slugs
	slugs := strings.Split(slugsParam, ",")
	for i, slug := range slugs {
		slugs[i] = strings.TrimSpace(slug)
	}

	variables := map[string]interface{}{
		"slugs": slugs,
	}

	result, err := h.hasuraClient.ExecuteQuery(healthProductVariantsQuery, variables)
	if err != nil {
		ctx.Logger.Error("Failed to fetch health product variants from Hasura", zap.Error(err))
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusInternalServerError},
			Problem: &response.Problem{Detail: "Failed to fetch health product variants"},
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: result.Data,
	}, nil
}
