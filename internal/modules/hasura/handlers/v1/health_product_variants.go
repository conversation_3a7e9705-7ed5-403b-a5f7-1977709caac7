package v1

import (
	"net/http"
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

const healthProductVariantsQuery = `
query MyQuery($ids: [String!]) {
  health_product_variants(where: {id: {_in: $ids}}) {
    id
    variant_slug
    variant_name
    product {
      policy_brochure_url
      policy_wording_url
      name
      insurer {
        claim_settlement_ratio
        logo_url
        name
        network_hospital_count
        network_hospital_url
        slug
      }
    }
    feature_values {
      value
      metadata
      compare_feature {
        name
        sequence
      }
    }
    health_variant_static_content {
      best_for
      comparison_enabled
      decision_guide
      specialty
      subtitle
      id
      product_popularity
    }
  }
  site_comparison_how_is_expert_consultation {
    title
    points
    id
  }
  site_comparison_why_choose_expert_consultation {
    logo_url
    title
    description
    id
  }
  site_comparison_health_faqs {
    answer
    id
    question
  }
}
`

func (h *Handler) GetHealthProductVariants(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	idsParam := ctx.GetQuery("ids")
	if idsParam == "" {
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusBadRequest},
			Problem: &response.Problem{Detail: "ids query parameter is required"},
		}
	}

	// Split comma-separated ids
	ids := strings.Split(idsParam, ",")
	for i, id := range ids {
		ids[i] = strings.TrimSpace(id)
	}

	variables := map[string]interface{}{
		"ids": ids,
	}

	result, err := h.hasuraClient.ExecuteQuery(healthProductVariantsQuery, variables)
	if err != nil {
		ctx.Logger.Error("Failed to fetch health product variants from Hasura", zap.Error(err))
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusInternalServerError},
			Problem: &response.Problem{Detail: "Failed to fetch health product variants"},
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: result.Data,
	}, nil
}
