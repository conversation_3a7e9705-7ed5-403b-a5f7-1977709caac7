package v1

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

const listSiteHealthVariantsQuery = `
query MyQuery2 {
  site_health_variant_static_content(where: {comparison_enabled: {_eq: true}}) {
    product_variant {
      id
      variant_name
      variant_slug
      product {
        insurer {
          name
          slug
        }
      }
    }
  }
}
`

func (h *Handler) ListSiteHealthVariants(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	result, err := h.hasuraClient.ExecuteQuery(listSiteHealthVariantsQuery, nil)
	if err != nil {
		ctx.Logger.Error("Failed to fetch site health variants from Hasura", zap.Error(err))
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusInternalServerError},
			Problem: &response.Problem{Detail: "Failed to fetch site health variants"},
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: result.Data,
	}, nil
}
