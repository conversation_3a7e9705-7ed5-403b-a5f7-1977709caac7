package client

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/go-resty/resty/v2"
	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
)

type HasuraClient struct {
	client *resty.Client
	config config.Config
}

type GraphQLRequest struct {
	Query     string                 `json:"query"`
	Variables map[string]interface{} `json:"variables,omitempty"`
}

type GraphQLResponse struct {
	Data   interface{} `json:"data"`
	Errors []struct {
		Message string `json:"message"`
	} `json:"errors,omitempty"`
}

func NewHasuraClient(cfg config.Config) *HasuraClient {
	client := resty.New()

	// Handle missing configuration
	hasuraURL := cfg.HASURA.Url
	if hasuraURL == "" {
		hasuraURL = "http://localhost" // Default fallback
	}

	// Add protocol if missing
	if !strings.HasPrefix(hasuraURL, "http") {
		hasuraURL = "http://" + hasuraURL
	}

	// Add port if specified
	if cfg.HASURA.Port != "" {
		hasuraURL = fmt.Sprintf("%s:%s", hasuraURL, cfg.HASURA.Port)
	}

	// Add GraphQL endpoint
	hasuraURL = hasuraURL + "/v1/graphql"

	client.SetBaseURL(hasuraURL)
	client.SetHeader("Content-Type", "application/json")

	// Only set admin secret if provided
	if cfg.HASURA.AdminSecret != "" {
		client.SetHeader("X-Hasura-Admin-Secret", cfg.HASURA.AdminSecret)
	}

	return &HasuraClient{
		client: client,
		config: cfg,
	}
}

func (h *HasuraClient) ExecuteQuery(query string, variables map[string]interface{}) (*GraphQLResponse, error) {
	request := GraphQLRequest{
		Query:     query,
		Variables: variables,
	}

	var response GraphQLResponse
	resp, err := h.client.R().
		SetBody(request).
		SetResult(&response).
		Post("")

	if err != nil {
		return nil, fmt.Errorf("failed to execute GraphQL query: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("GraphQL query failed with status %d: %s", resp.StatusCode(), resp.String())
	}

	if len(response.Errors) > 0 {
		var errorMsgs []string
		for _, err := range response.Errors {
			errorMsgs = append(errorMsgs, err.Message)
		}
		return nil, fmt.Errorf("GraphQL errors: %v", errorMsgs)
	}

	return &response, nil
}
