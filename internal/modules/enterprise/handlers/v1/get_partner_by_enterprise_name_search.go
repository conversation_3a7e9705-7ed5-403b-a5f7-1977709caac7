package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-enterprise/v0/enterprise"
	"go.uber.org/zap"
)

func (h *Handler) GetPartnerByEnterpriseNameSearch(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(enterprise.Enterprise_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting enterprise client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	search_param := ctx.GinCtx.Query("search")

	client := enterprise.NewEnterpriseClient(conn)

	protoReq := &enterprise.GetPartnerByEnterpriseNameSearchRequest{
		Search: search_param,
	}

	resp, err := client.GetPartnerByEnterpriseNameSearch(grpcCtx.(context.Context), protoReq)

	if err != nil {
		ctx.Logger.Error("Error making GetPartnerByEnterpriseNameSearch  request", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
