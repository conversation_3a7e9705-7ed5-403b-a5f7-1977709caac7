package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/enterprise/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-enterprise/v0/enterprise"
	"go.uber.org/zap"
)

func (h *Handler) GetPartnerTable(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(enterprise.Enterprise_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting enterprise client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	var req dto.GetPartnerTableRequest
	if err := ctx.GinCtx.ShouldBindQuery(&req); err != nil {
		ctx.Logger.Error("Invalid request body", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	client := enterprise.NewEnterpriseClient(conn)

	protoReq := &enterprise.GetPartnerTableRequest{
		LastPartnerId:  req.LastPartnerID,
		Limit:          req.Limit,
		FirstPartnerId: req.FirstPartnerID,
	}

	resp, err := client.GetPartnerTable(grpcCtx.(context.Context), protoReq)

	if err != nil {
		ctx.Logger.Error("Error getting table details", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
