package enterprise

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/enterprise/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/enterprise/handlers/v1"
)

type EnterpriseApp struct {
	app.MustEmbedApp
}

func New() app.AppIface {
	return &EnterpriseApp{}
}

func (app *EnterpriseApp) SetAppName() string {
	return "enterprise"
}

func (app *EnterpriseApp) Initialize(appName string, appContext *app.AppContext) {

	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	// Register HTTP routes
	apiRouter := appContext.Router[constant.HTTP_API]
	apiRouter.RegisterRoute(ctx, appName, "GET", "/partner-table",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPartnerTable,
				RequireValidation: false,
				QueryStruct:       &dto.GetPartnerTableRequest{},
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"enterprise.get-partner-table",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/partner-search",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPartnerByEnterpriseNameSearch,
				RequireValidation: false,
				QueryStruct:       &dto.GetPartnerByEnterpriseNameSearchRequest{},
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"enterprise.get-partner-table",
					}),
				},
			}),
		},
	)

}
