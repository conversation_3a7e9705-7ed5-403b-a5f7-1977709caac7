package dto

// GetPartnerTableRequest represents the request DTO for fetching partner table data.
type GetPartnerTableRequest struct {
	LastPartnerID  *string `json:"last_partner_id" form:"last_partner_id"`
	Limit          *int32  `json:"limit" form:"limit"`
	FirstPartnerID *string `json:"first_partner_id" form:"first_partner_id"`
}

type GetPartnerByEnterpriseNameSearchRequest struct {
	Search string `json:"search" form:"search"`
}
