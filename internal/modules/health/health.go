package health

import (
	"context"

	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	v0 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/health/handlers/v0"
)

type HealthApp struct {
	app.MustEmbedApp
}

// New is a constructor equivalent which initialises the HealthApp struct and returns a pointer to the object HealthApp
func New() app.AppIface {
	return &HealthApp{}
}

func (app *HealthApp) SetAppName() string {
	return "healthz"
}

func (app *HealthApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h0 := &v0.Handler{}

	apiRouter := appContext.Router[constant.HTTP_API]

	requireAuth := false
	// Attach the routes
	apiRouter.RegisterRoute(ctx, appName, "GET", "",
		map[uint8]*handler.OptionStruct{
			0: h0.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h0.HealthCheck,
				RequireValidation: false,
				RequireAuth:       &requireAuth,
			}),
		},
	)
}
