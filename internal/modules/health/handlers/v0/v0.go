package v0

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
)

type Handler struct {
	handler.MustEmbedHandler
}

func (h *Handler) HealthCheck(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: "OK",
	}, nil
}
