package v1

import (
	"fmt"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-rapidshort/v0/rapidshort"
	"go.uber.org/zap"
)

func (h *Handler) GetShortUrl(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(rapidshort.RapidShortService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting RapidShort client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := rapidshort.NewRapidShortServiceClient(conn)

	// Extract from URL path
	shortCode := ctx.GinCtx.Param("short_code")

	// Build the gRPC request
	res, err := client.GetShortUrl(ctx.GinCtx, &rapidshort.GetShortUrlRequest{
		ShortCode: shortCode,
	})

	if err != nil {
		ctx.Logger.Error("Error fetching short URL", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	statusCode, err := strconv.Atoi(res.StatusCode)
	if err != nil {
		ctx.Logger.Error("Error converting status code to int", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	fmt.Println("res.LongUrl", res.LongUrl)
	ctx.GinCtx.Redirect(statusCode, res.LongUrl)

	return &response.SuccessResponse{
		Payload: res,
	}, nil
}
