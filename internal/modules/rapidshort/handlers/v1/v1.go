package v1

import (
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-protos/go/oa-rapidshort/v0/rapidshort"
)

type Handler struct {
	handler.MustEmbedHandler
	GrpcClients *grpc.GrpcClientManager
}


type GrpcHandler struct {
	Handler *Handler // explicit reference, not embedding
	rapidshort.UnimplementedRapidShortServiceServer
}