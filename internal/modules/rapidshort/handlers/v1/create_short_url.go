package v1

import (
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/rapidshort/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	rapidshort "github.com/oneassure-tech/oa-protos/go/oa-rapidshort/v0/rapidshort"
	"go.uber.org/zap"
)

func (h *Handler) CreateShortUrl(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	// Check for dummy authentication
	if ctx.GinCtx.GetHeader("Authorization") != "Bearer "+config.GetConfig().RapidshortAdminToken {
		return nil, response.NewInternalServerError()
	}

	req := rapidshort.CreateShortUrlRequest{}
	if err := ctx.GinCtx.ShouldBindJSON(&req); err != nil {
		ctx.Logger.Error("Error binding request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	longUrl := req.LongUrl
	// redirectionCode := req.RedirectionCode

	conn, err := h.GrpcClients.Get(rapidshort.RapidShortService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting RapidShort client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := rapidshort.NewRapidShortServiceClient(conn)

	res, err := client.CreateShortUrl(ctx.GinCtx, &rapidshort.CreateShortUrlRequest{
		LongUrl:         longUrl,
		RedirectionCode: "302",
	})
	fmt.Println("res", res.ShortCode)

	if err != nil {
		ctx.Logger.Error("Error creating short url", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.CreateShortUrlResponse{
			ShortUrl: res.GetShortCode(),
		},
	}, nil
}
