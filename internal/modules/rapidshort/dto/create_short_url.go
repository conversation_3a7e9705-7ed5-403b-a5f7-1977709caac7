package dto

// type CreateShortUrlRequest struct {
// 	long_url         string `json:"long_url" binding:"required"`
// 	redirection_code string `json:"redirection_code" binding:"required"`
// }

// type CreateShortUrlResponse struct {
// 	short_code       string `json:"short_code"`
// 	redirection_code string `json:"redirection_code"`
// 	status_code      string `json:"status_code"`
// }

type CreateShortUrlResponse struct {
	ShortUrl string `json:"short_url"`
}

type GetShortUrlRequestQuery struct {
	ShortCode string `form:"short_code" validate:"required"`
}
