package rapidshort

import (
	"context"

	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"

	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/rapidshort/handlers/v1"
)

type RapidShortApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &RapidShortApp{}
}

// SetAppName returns the name of the application
func (app *RapidShortApp) SetAppName() string {
	return "rapidshort"
}

func (app *RapidShortApp) Initialize(appName string, appContext *app.AppContext) {
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	rapidshortRouter := appContext.Router[constant.HTTP_RAPIDSHORT]

	// requireAuth := false
	// publicRouter.RegisterRoute(ctx, appName, "POST", "/",
	// 	map[uint8]*handler.OptionStruct{
	// 		1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
	// 			Handler:           h1.CreateShortUrl,
	// 			QueryStruct:       &dto.CreateShortUrlRequest{},
	// 			RequireAuth:       &requireAuth,
	// 			RequireValidation: true,
	// 		}),
	// 	},
	// )
	rapidshortRouter.RegisterRoute(ctx, appName, "GET", "/:short_code",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.GetShortUrl,
			}),
		},
	)

	rapidshortRouter.RegisterRoute(ctx, appName, "POST", "/",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.CreateShortUrl,
			}),
		},
	)
}
