package dto

type GetCompareParams struct {
	Token string `json:"token"`
}

type GetCompareQuery struct {
	Variants string `form:"variants"`
}

type CreateCompareQuery struct {
	Variants string `form:"variants"`
}

type CreateCompareBody struct {
	Variants   []string `json:"variant_ids"`
	SumInsured int      `json:"sum_insured"`
}

type CreateCompareResponse struct {
	ShortUrl string `json:"short_url"`
}

type VariantDocument struct {
	Brochure       string `json:"brochure"`
	PolicyWordings string `json:"policy_wordings"`
}

type Variant struct {
	VariantID   string           `json:"variant_id"`
	Variant     string           `json:"variant"`
	ProductID   string           `json:"product_id"`
	Product     string           `json:"product"`
	InsurerID   string           `json:"insurer_id"`
	Insurer     string           `json:"insurer"`
	InsurerLogo string           `json:"insurer_logo"`
	Documents   *VariantDocument `json:"documents"`
}

// type FeatureValues struct {

// }

type FeatureItem struct {
	Score int    `json:"score"`
	Value string `json:"value"`
}

// ValueItem represents a single value and its associated metadata within a feature for a variant.
type ValueItem struct {
	Value    string                 `json:"value"`
	Metadata map[string]interface{} `json:"metadata"`
}

// VariantItem holds the score and specific values for a feature corresponding to a particular variant.
type VariantItem struct {
	Score  int          `json:"score"`
	Values []*ValueItem `json:"values"`
}

// Feature represents a specific comparison feature within a section.
// Items map variant IDs (e.g., "variant_1") to their respective details (VariantItem).
type Feature struct {
	Name     string                  `json:"feature"`
	Sequence int                     `json:"sequence"`
	HintText *string                 `json:"hint_text"`
	Items    map[string]*VariantItem `json:"items"`
}

// Section groups related features together for comparison.
type Section struct {
	Name     string     `json:"section"`
	Sequence int        `json:"sequence"`
	Features []*Feature `json:"features"`
}

// GetCompareResponse is the structure for the compare API response.
// Sections contains the structured comparison data.
type GetCompareResponse struct {
	Variants []*Variant `json:"variants"`
	Sections []*Section `json:"sections"`
}
