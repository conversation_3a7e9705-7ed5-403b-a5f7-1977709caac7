package compare

import (
	"context"

	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/compare/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/compare/handlers/v1"
)

type CompareApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &CompareApp{}
}

// SetAppName returns the name of the application
func (app *CompareApp) SetAppName() string {
	return "compare"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *CompareApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}
	requireAuth := false
	apiRouter := appContext.Router[constant.HTTP_API]

	apiRouter.RegisterRoute(ctx, appName, "GET", "",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetCompare,
				QueryStruct:       &dto.GetCompareQuery{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/term",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetTermCompare,
				QueryStruct:       &dto.GetCompareQuery{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/term",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.CreateTermCompare,
				ReqBodyStruct:     &dto.CreateCompareBody{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.CreateCompare,
				ReqBodyStruct:     &dto.CreateCompareBody{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
			}),
		},
	)

}
