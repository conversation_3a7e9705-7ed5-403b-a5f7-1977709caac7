package v1

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/jwt"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/compare/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/health"
	"github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/term"
	"github.com/oneassure-tech/oa-protos/go/oa-rapidshort/v0/rapidshort"
	"google.golang.org/protobuf/types/known/timestamppb"

	// grpc metadata import
	md "google.golang.org/grpc/metadata"

	"go.uber.org/zap"
)

func (h *Handler) GetCompare(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	metadata := md.New(map[string]string{
		"enterprise_id": "18TzlaIOrzcGFHY7",
	})

	grpcCtx := md.NewOutgoingContext(context.TODO(), metadata)

	grpcClient, err := h.GrpcClients.Get(health.HealthService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting grpc client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := health.NewHealthServiceClient(grpcClient)

	// Extract variants from token if available, otherwise use variants query parameter
	var variantIds []string
	tokenQuery := ctx.GetQuery("token")
	if tokenQuery != "" {
		// Parse JWT token to extract variants
		_, claims, err := jwt.InsecureParseToken(tokenQuery)
		if err != nil {
			ctx.Logger.Error("Failed to parse JWT token", zap.Error(err))
			return nil, response.NewInternalServerError()
		}

		// Extract variants from token claims
		if variantsClaim, exists := (*claims)["variants"]; exists {
			if variantsStr, ok := variantsClaim.(string); ok {
				variantIds = strings.Split(variantsStr, ",")
			}
		}
	} else {
		variantsQuery := ctx.GetQuery("variants")
		if variantsQuery != "" {
			variantIds = strings.Split(variantsQuery, ",")
		}
	}

	comparison, err := client.GetComparison(grpcCtx, &health.GetComparisonRequest{
		VariantIds: variantIds,
	})
	if err != nil {
		ctx.Logger.Error("Error getting comparison", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Parse Variants
	var variants []*dto.Variant
	for _, variant := range comparison.Variants {
		var parentVariantID *string
		if variant.ParentVariantId != nil {
			parentVariantID = variant.ParentVariantId
		}
		variants = append(variants, &dto.Variant{
			VariantID:   variant.VariantId,
			Variant:     variant.Variant,
			ProductID:   variant.ProductId,
			Product:     variant.Product,
			InsurerID:   variant.InsurerId,
			InsurerLogo: variant.InsurerLogo,
			Insurer:     variant.Insurer,
			Documents: &dto.VariantDocument{
				Brochure:       variant.Documents.Brochure,
				PolicyWordings: variant.Documents.PolicyWordings,
			},
			ParentVariantID: parentVariantID,
		})
	}

	// Parse Sections
	var sections []*dto.Section
	for _, section := range comparison.Sections {
		var features []*dto.Feature
		for _, feature := range section.Features {
			featureMap := make(map[string]*dto.VariantItem)
			for variantId, item := range feature.Items {
				var valueItems []*dto.ValueItem
				for _, value := range item.Values {
					// Convert protobuf struct to map[string]interface{}
					var metadata map[string]interface{}
					if value.Metadata != nil {
						metadata = value.Metadata.AsMap()
					}
					valueItems = append(valueItems, &dto.ValueItem{
						Value:    value.Value,
						Metadata: metadata,
					})
				}
				featureMap[variantId] = &dto.VariantItem{
					Score:  int(item.Score),
					Values: valueItems,
				}
			}
			features = append(features, &dto.Feature{
				Name:     feature.Name,
				Sequence: int(feature.Sequence),
				HintText: feature.HintText,
				Items:    featureMap,
			})
		}
		sections = append(sections, &dto.Section{
			Name:     section.Name,
			Sequence: int(section.Sequence),
			Features: features,
		})
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetCompareResponse{
			Variants: variants,
			Sections: sections,
		},
	}, nil
}

func (h *Handler) GetTermCompare(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	metadata := md.New(map[string]string{
		"enterprise_id": "18TzlaIOrzcGFHY7",
	})

	grpcCtx := md.NewOutgoingContext(context.TODO(), metadata)

	grpcClient, err := h.GrpcClients.Get(term.TermService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting grpc client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := term.NewTermServiceClient(grpcClient)

	// Extract variants from token if available, otherwise use variants query parameter or request body
	var variantIds []string
	tokenQuery := ctx.GetQuery("token")
	if tokenQuery != "" {
		// Parse JWT token to extract variants
		_, claims, err := jwt.InsecureParseToken(tokenQuery)
		if err != nil {
			ctx.Logger.Error("Failed to parse JWT token", zap.Error(err))
			return nil, response.NewInternalServerError()
		}

		// Extract variants from token claims
		if variantsClaim, exists := (*claims)["variants"]; exists {
			if variantsStr, ok := variantsClaim.(string); ok {
				variantIds = strings.Split(variantsStr, ",")
			}
		}
	} else {
		// Try to get variants from query parameter first
		variantsQuery := ctx.GetQuery("variants")
		if variantsQuery != "" {
			variantIds = strings.Split(variantsQuery, ",")
		} else {
			// Try to get variants from request body
			if ctx.Req != nil {
				if createBody, ok := ctx.Req.(*dto.CreateCompareBody); ok && createBody.Variants != nil {
					variantIds = createBody.Variants
				}
			}
		}
	}

	comparison, err := client.GetComparison(grpcCtx, &term.GetComparisonRequest{
		VariantIds: variantIds,
	})
	if err != nil {
		ctx.Logger.Error("Error getting comparison", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Parse Variants
	var variants []*dto.Variant
	for _, variant := range comparison.Variants {
		variants = append(variants, &dto.Variant{
			VariantID:   variant.VariantId,
			Variant:     variant.Variant,
			ProductID:   variant.ProductId,
			Product:     variant.Product,
			InsurerID:   variant.InsurerId,
			InsurerLogo: variant.InsurerLogo,
			Insurer:     variant.Insurer,
			Documents: &dto.VariantDocument{
				Brochure:       variant.Documents.Brochure,
				PolicyWordings: variant.Documents.PolicyWordings,
			},
		})
	}

	// Parse Sections
	var sections []*dto.Section
	for _, section := range comparison.Sections {
		var features []*dto.Feature
		for _, feature := range section.Features {
			featureMap := make(map[string]*dto.VariantItem)
			for variantId, item := range feature.Items {
				var valueItems []*dto.ValueItem
				for _, value := range item.Values {
					// Convert protobuf struct to map[string]interface{}
					var metadata map[string]interface{}
					if value.Metadata != nil {
						metadata = value.Metadata.AsMap()
					}
					valueItems = append(valueItems, &dto.ValueItem{
						Value:    value.Value,
						Metadata: metadata,
					})
				}
				featureMap[variantId] = &dto.VariantItem{
					Score:  int(item.Score),
					Values: valueItems,
				}
			}
			features = append(features, &dto.Feature{
				Name:     feature.Name,
				Sequence: int(feature.Sequence),
				HintText: feature.HintText,
				Items:    featureMap,
			})
		}
		sections = append(sections, &dto.Section{
			Name:     section.Name,
			Sequence: int(section.Sequence),
			Features: features,
		})
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetCompareResponse{
			Variants: variants,
			Sections: sections,
		},
	}, nil
}

func (h *Handler) CreateTermCompare(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	metadata := md.New(map[string]string{
		"enterprise_id": "18TzlaIOrzcGFHY7",
	})

	grpcCtx := md.NewOutgoingContext(context.TODO(), metadata)

	// Extract variants and sum insured from request body
	var variantIds []string
	var sumInsured int
	if ctx.Req != nil {
		ctx.Logger.Info("Request body found", zap.Any("req", ctx.Req))

		var createBody dto.CreateCompareBody
		if err := ctx.ExtractHttpRequest(&createBody); err != nil {
			ctx.Logger.Warn("Failed to parse request body as CreateCompareBody", zap.Error(err))
		} else {
			ctx.Logger.Info("Successfully parsed CreateCompareBody", zap.Any("createBody", createBody))
			if createBody.Variants != nil && len(createBody.Variants) > 0 {
				variantIds = createBody.Variants
				sumInsured = createBody.SumInsured
				ctx.Logger.Info("Extracted variants and sum insured", zap.Strings("variants", variantIds), zap.Int("sumInsured", sumInsured))
			} else {
				ctx.Logger.Warn("Variants is nil or empty in request body")
			}
		}
	} else {
		ctx.Logger.Warn("No request body found")
	}

	// Generate JWT token with custom claims
	authConn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	authClient := auth.NewAuthClient(authConn)

	// Create custom claims with variant IDs and sum insured
	customClaims := map[string]string{
		"variants":      strings.Join(variantIds, ","),
		"sum_insured":   fmt.Sprintf("%d", sumInsured),
		"enterprise_id": "18TzlaIOrzcGFHY7",
	}

	ctx.Logger.Info("Creating JWT token with claims", zap.Any("customClaims", customClaims))

	// Set expiration to 30 days from now
	expiresAt := time.Now().Add(30 * 24 * time.Hour)

	resp, err := authClient.GenerateJWTToken(grpcCtx, &auth.GenerateJWTTokenRequest{
		ExpiresAt:    timestamppb.New(expiresAt),
		CustomClaims: customClaims,
		Audience:     "gateway_public",
	})
	if err != nil {
		ctx.Logger.Error("Error generating JWT token", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Create short URL with the JWT token
	rapidshortConn, err := h.GrpcClients.Get(rapidshort.RapidShortService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting rapidshort client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	rapidshortClient := rapidshort.NewRapidShortServiceClient(rapidshortConn)

	res, err := rapidshortClient.CreateShortUrl(grpcCtx, &rapidshort.CreateShortUrlRequest{
		LongUrl: fmt.Sprintf("%s/customer/term/compare?token=%s",
			ctx.GinCtx.Request.Header.Get("Origin"),
			resp.AccessToken,
		),
		RedirectionCode: "302",
	})
	if err != nil {
		ctx.Logger.Error("Error creating short url", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.CreateCompareResponse{
			ShortUrl: fmt.Sprintf("%s/%s", "https://oasr.in", res.GetShortCode()),
		},
	}, nil
}

func (h *Handler) CreateCompare(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	metadata := md.New(map[string]string{
		"enterprise_id": "18TzlaIOrzcGFHY7",
	})

	grpcCtx := md.NewOutgoingContext(context.TODO(), metadata)

	// Extract variants from request body first, then fallback to query parameter
	var variantIds []string
	var sumInsured int

	// Try to get variants from request body first (for POST requests)
	if ctx.Req != nil {
		ctx.Logger.Info("Request body found", zap.Any("req", ctx.Req))

		var createBody dto.CreateCompareBody
		if err := ctx.ExtractHttpRequest(&createBody); err != nil {
			ctx.Logger.Warn("Failed to parse request body as CreateCompareBody", zap.Error(err))
		} else {
			ctx.Logger.Info("Successfully parsed CreateCompareBody", zap.Any("createBody", createBody))
			if createBody.Variants != nil && len(createBody.Variants) > 0 {
				variantIds = createBody.Variants
				sumInsured = createBody.SumInsured
				ctx.Logger.Info("Extracted variants and sum insured", zap.Strings("variants", variantIds), zap.Int("sumInsured", sumInsured))
			} else {
				ctx.Logger.Warn("Variants is nil or empty in request body")
			}
		}
	} else {
		ctx.Logger.Warn("No request body found")
	}

	// Fallback to query parameter if no variants found in request body
	if len(variantIds) == 0 {
		variantsQuery := ctx.GetQuery("variants")
		if variantsQuery != "" {
			variantIds = strings.Split(variantsQuery, ",")
		}
	}

	// Generate JWT token with custom claims
	authConn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	authClient := auth.NewAuthClient(authConn)

	// Create custom claims with variant IDs and sum insured
	customClaims := map[string]string{
		"variants":      strings.Join(variantIds, ","),
		"sum_insured":   fmt.Sprintf("%d", sumInsured),
		"enterprise_id": "18TzlaIOrzcGFHY7",
	}

	ctx.Logger.Info("Creating JWT token with claims", zap.Any("customClaims", customClaims))

	// Set expiration to 30 days from now
	expiresAt := time.Now().Add(30 * 24 * time.Hour)

	resp, err := authClient.GenerateJWTToken(grpcCtx, &auth.GenerateJWTTokenRequest{
		ExpiresAt:    timestamppb.New(expiresAt),
		CustomClaims: customClaims,
		Audience:     "gateway_public",
	})
	if err != nil {
		ctx.Logger.Error("Error generating JWT token", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Create short URL with the JWT token
	rapidshortConn, err := h.GrpcClients.Get(rapidshort.RapidShortService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting rapidshort client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	rapidshortClient := rapidshort.NewRapidShortServiceClient(rapidshortConn)

	res, err := rapidshortClient.CreateShortUrl(grpcCtx, &rapidshort.CreateShortUrlRequest{
		LongUrl: fmt.Sprintf("%s/customer/health/compare?token=%s",
			ctx.GinCtx.Request.Header.Get("Origin"),
			resp.AccessToken,
		),
		RedirectionCode: "302",
	})
	if err != nil {
		ctx.Logger.Error("Error creating short url", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.CreateCompareResponse{
			ShortUrl: fmt.Sprintf("%s/%s", "https://oasr.in", res.GetShortCode()),
		},
	}, nil
}
