package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	recommendation "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
	"go.uber.org/zap"
)

// Define structs for JSON marshalling with string enums
type PurchaseIntentForQuoteJSON struct {
	PurchaseIntentId  string `json:"purchase_intent_id"`
	VariantName       string `json:"variant_name"`
	ProductName       string `json:"product_name"`
	SumInsured        int64  `json:"sum_insured"`
	NextAction        string `json:"next_action"` // Use string for JSON
	SubmissionId      string `json:"submission_id"`
	PolicyTerm        string `json:"policy_term"`
	PaymentFrequency  string `json:"payment_frequency"`
	PremiumPayingTerm string `json:"premium_paying_term"`
}

type GetQuoteResponseJSON struct {
	Success              bool                          `json:"success"`
	Id                   string                        `json:"id"`
	LeadId               string                        `json:"lead_id"`
	LeadName             string                        `json:"lead_name"`
	PhoneNumber          string                        `json:"phone_number"`
	Email                string                        `json:"email"`
	Pincode              string                        `json:"pincode"`
	MemberCombination    string                        `json:"member_combination"`
	DateOfRecommendation string                        `json:"date_of_recommendation"`
	SumInsured           string                        `json:"sum_insured"`
	Vertical             string                        `json:"vertical"`
	PlanType             string                        `json:"plan_type"`
	PurchaseIntents      []*PurchaseIntentForQuoteJSON `json:"purchase_intents"` // Use the JSON struct slice
}

func (h *Handler) GetQuote(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	quoteID := ctx.GetParam("quote_id")

	conn, err := h.GrpcClients.Get(recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	client := recommendation.NewTermRecommendationClient(conn)

	getQuoteRequest := &recommendation.GetQuoteRequest{
		QuoteId: quoteID,
	}

	// This is the response from the recommendation service (contains int enums)
	getQuoteResponse, err := client.GetQuote(grpcCtx.(context.Context), getQuoteRequest)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	// --- Convert response for JSON output ---
	purchaseIntentsJSON := make([]*PurchaseIntentForQuoteJSON, 0, len(getQuoteResponse.PurchaseIntents))
	for _, pi := range getQuoteResponse.PurchaseIntents {
		purchaseIntentsJSON = append(purchaseIntentsJSON, &PurchaseIntentForQuoteJSON{
			PurchaseIntentId:  pi.PurchaseIntentId,
			VariantName:       pi.VariantName,
			ProductName:       pi.ProductName,
			SumInsured:        pi.SumInsured,
			NextAction:        pi.NextAction.String(),
			SubmissionId:      *pi.SubmissionId,
			PolicyTerm:        pi.PolicyTerm,
			PaymentFrequency:  pi.PaymentFrequency,
			PremiumPayingTerm: pi.PremiumPayingTerm,
		})
	}

	// Create the final response object for JSON marshalling
	jsonResponse := &GetQuoteResponseJSON{
		Success:              getQuoteResponse.Success,
		Id:                   getQuoteResponse.Id,
		LeadId:               getQuoteResponse.LeadId,
		LeadName:             getQuoteResponse.LeadName,
		PhoneNumber:          getQuoteResponse.PhoneNumber,
		Email:                getQuoteResponse.Email,
		Pincode:              getQuoteResponse.Pincode,
		MemberCombination:    getQuoteResponse.MemberCombination,
		DateOfRecommendation: getQuoteResponse.DateOfRecommendation,
		SumInsured:           getQuoteResponse.SumInsured,
		Vertical:             getQuoteResponse.Vertical,
		PlanType:             getQuoteResponse.PlanType,
		PurchaseIntents:      purchaseIntentsJSON,
	}

	// Return the JSON-ready response object as the payload
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: jsonResponse,
	}, nil
}
