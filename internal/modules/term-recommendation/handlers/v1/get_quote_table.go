package v1

import (
	"context"
	"net/http"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term-recommendation/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-gateway-svc/internal/utility"
	recommendation "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
	"go.uber.org/zap"
)

func (h *Handler) GetQuoteTable(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := recommendation.NewTermRecommendationClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Parse query parameters
	limitStr := ctx.GinCtx.DefaultQuery("limit", "10")

	startDate := ctx.GinCtx.Query("start_date")
	endDate := ctx.GinCtx.Query("end_date")
	lastQuoteId := ctx.GinCtx.Query("last_quote_id")
	firstQuoteId := ctx.GinCtx.Query("first_quote_id")

	parsedLimit, err := strconv.ParseInt(limitStr, 10, 32)
	if err != nil {
		ctx.Logger.Warn("Invalid limit format, using default", zap.String("limit", limitStr), zap.Error(err))
		parsedLimit = 10 // Use default if parsing fails
	}
	limitInt := int32(parsedLimit)

	// Build the gRPC request using updated proto fields
	grpcReq := &recommendation.GetQuoteTableRequest{
		Limit:        &limitInt,
		StartDate:    &startDate,
		EndDate:      &endDate,
		LastQuoteId:  &lastQuoteId,
		FirstQuoteId: &firstQuoteId,
	}
	if lastQuoteId != "" {
		grpcReq.LastQuoteId = &lastQuoteId
	}

	// Make the gRPC call
	res, err := client.GetQuoteTable(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetQuoteTable gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Extract partner IDs for enterprise name lookup
	partnerIds := make([]string, 0)
	for _, quote := range res.Quotes {
		if quote.PartnerId != "" {
			partnerIds = append(partnerIds, quote.PartnerId)
		}

	}

	role := ctx.GinCtx.GetString("role")
	enterpriseType := ctx.GinCtx.GetString("enterprise_type")

	partnerMap, err := utility.GetPartnerEnterpriseName(
		grpcCtx.(context.Context),
		h.GrpcClients,
		ctx.Logger,
		role,
		enterpriseType,
		partnerIds,
	)
	if err != nil {
		ctx.Logger.Error("Error getting partner enterprise names", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Convert gRPC response to DTO
	quotes := make([]dto.QuoteData, len(res.Quotes))
	for i, q := range res.Quotes {
		quotes[i] = dto.QuoteData{
			ID:                   q.Id,
			LeadID:               q.LeadId,
			LeadName:             q.LeadName,
			PhoneNumber:          q.PhoneNumber,
			Email:                q.Email,
			Pincode:              q.Pincode,
			DateOfRecommendation: q.DateOfRecommendation,
			PincodeID:            q.PincodeId,
			SumInsured:           q.SumInsured,
			Vertical:             q.Vertical,
			PlanType:             q.PlanType,
		}

		// Add partner and enterprise names if available
		if partnerInfo, exists := partnerMap[q.PartnerId]; exists {
			quotes[i].PartnerName = partnerInfo.PartnerName
			quotes[i].EnterpriseName = partnerInfo.EnterpriseName
		}
	}

	responseDTO := dto.GetQuoteTableResponse{
		Success:     res.Success,
		EndCursor:   res.EndCursor,
		Quotes:      quotes,
		StartCursor: res.StartCursor,
		HasPrevious: res.HasPrevious,
		HasNext:     res.HasNext,
		TotalCount:  res.TotalCount,
	}

	// Return the response from the downstream service
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: responseDTO,
	}, nil
}
