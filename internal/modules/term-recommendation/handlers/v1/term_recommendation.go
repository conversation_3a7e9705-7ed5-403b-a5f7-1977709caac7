package v1

import (
	"context"
	"net/http"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term-recommendation/dto"

	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term-recommendation/helpers"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
	"go.uber.org/zap"
)

// Utility functions for type conversions
// intToInt32Ptr converts an int to *int32

func (h *Handler) GenerateRecommendation(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.CreateRecommendationRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(term_recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation client", zap.Error(err))

		return nil, response.NewInternalServerError()
	}

	client := term_recommendation.NewTermRecommendationClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}
	annualIncome, err := strconv.ParseFloat(req.AnnualIncome, 64)
	if err != nil {
		ctx.Logger.Error("Error parsing annual income", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	createRecommendationRequest := &term_recommendation.CreateRecommendationRequest{
		PartnerId:    ctx.GinCtx.GetString("partner_id"),
		EnterpriseId: ctx.GinCtx.GetString("enterprise_id"),

		MemberDetails: &term_recommendation.MemberDetails{
			Name:          req.Name,
			Email:         req.Email,
			Phone:         req.Phone,
			CountryCode:   req.CountryCode,
			Dob:           req.Dob,
			MaritalStatus: req.MaritalStatus,
			Gender:        req.Gender,
			Occupation:    req.Occupation,
			AnnualIncome:  annualIncome,
			Education:     req.Education,
			Smoker:        req.Smoker,
		},
		// PlanType: &term_recommendation.PlanType(req.PlanType),
		Pincode:           req.Pincode,
		PremiumPayingTerm: helpers.IntToInt32Ptr(req.PremiumPayingTerm),
		PolicyTerm:        helpers.IntToInt32Ptr(req.PolicyTerm),
		PaymentFrequency:  helpers.StringToFrequencyPtr(req.PaymentFrequency),
	}

	if req.SumInsured != 0 {
		createRecommendationRequest.SumInsured = &req.SumInsured
	}
	// 	createRecommendationRequest.SumInsured = helpers.(1000000)
	// }

	submission, err := client.CreateRecommendation(grpcCtx.(context.Context), createRecommendationRequest)

	if err != nil {
		ctx.Logger.Error("Error submitting form response", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	resp := &dto.GenerateRecommendationResponse{
		QuoteID: submission.GetQuoteId(),
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func (h *Handler) GetRecommendation(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	quoteID := ctx.GetParam("quote_id")

	conn, err := h.GrpcClients.Get(term_recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation client", zap.Error(err))

		return nil, response.NewInternalServerError()
	}

	client := term_recommendation.NewTermRecommendationClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	getRecommendationRequest := &term_recommendation.GetRecommendationRequest{
		Id: quoteID, // Using QuoteId as shown in the proto definition
	}

	getRecommendationResponse, err := client.GetRecommendation(grpcCtx.(context.Context), getRecommendationRequest)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation", zap.Error(err))

		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	// Convert proto response to DTO
	resp := helpers.ConvertProtoToDTO(getRecommendationResponse)
	resp.MemberDetails = helpers.ConvertMemberDetailsToDTO(getRecommendationResponse.GetMemberDetails())

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

// Utility functions for response conversion
