package v1

import (
	"context"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	quotes "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
	"go.uber.org/zap"
)

func (h *<PERSON><PERSON>) GetQuoteTableSearchResult(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	searchQuery := ctx.GetQuery("search_query")

	conn, err := h.GrpcClients.Get(quotes.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting quotes client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := quotes.NewTermRecommendationClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	fmt.Println("Search query", searchQuery)

	// Create the request for gRPC service
	grpcReq := &quotes.GetQuotesTableSearchResultsRequest{
		SearchQuery: searchQuery,
	}

	// Call the gRPC service
	res, err := client.GetQuotesTableSearchResults(grpcCtx.(context.Context), grpcReq)
	fmt.Println("Response", res)

	if err != nil {
		ctx.Logger.Error("Error sending search results", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	ctx.Logger.Info("Service name", zap.String("name", quotes.TermRecommendation_ServiceDesc.ServiceName))

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
