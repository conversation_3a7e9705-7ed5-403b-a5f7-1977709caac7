package termrecommendation

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term-recommendation/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/term-recommendation/handlers/v1"
)

type TermRecommendationApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &TermRecommendationApp{}
}

// SetAppName returns the name of the application
func (app *TermRecommendationApp) SetAppName() string {
	return "term_recommendation"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *TermRecommendationApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}
	requireAuth := false

	// Register HTTP routes
	apiRouter := appContext.Router[constant.HTTP_API]

	apiRouter.RegisterRoute(ctx, appName, "POST", "/term",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.GenerateRecommendation,
				ReqBodyStruct: &dto.CreateRecommendationRequest{},
				RequireAuth:   &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"term_recommendation.create",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/:quote_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.GetRecommendation,
				QueryStruct: &dto.GetRecommendationRequest{},
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"term_recommendation.get-recommendation",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/quotes",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetQuoteTable,
				QueryStruct:       &dto.GetQuoteTableRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"term_recommendation.get-quotes-table",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/quotes/:quote_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetQuote,
				QueryStruct:       &dto.GetQuoteRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"term_recommendation.get-quote",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/quotes/search",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetQuoteTableSearchResult,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"term_recommendation.get-quotes-table",
					}),
				},
			}),
		},
	)
}
