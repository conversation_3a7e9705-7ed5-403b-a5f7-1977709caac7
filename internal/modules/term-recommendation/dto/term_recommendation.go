package dto

import (
	"encoding/json"
)

type CreateRecommendationRequest struct {
	// MemberDetails     MemberDetails `json:"member_details" validate:"required"`
	PlanType          string  `json:"plan_type"`
	SumInsured        float64 `json:"sum_insured"`
	Pincode           string  `json:"pincode"`
	PremiumPayingTerm int     `json:"premium_paying_term"`
	PolicyTerm        int     `json:"policy_term"`
	PaymentFrequency  string  `json:"payment_frequency"`
	Name              string  `json:"name"`
	Email             string  `json:"email"`
	Phone             string  `json:"phone"`
	CountryCode       string  `json:"country_code"`
	Dob               string  `json:"dob"`
	MaritalStatus     string  `json:"marital_status"`
	Gender            string  `json:"gender"`
	Smoker            string  `json:"smoker"`
	Occupation        string  `json:"occupation"`
	Education         string  `json:"education"`
	AnnualIncome      string  `json:"annual_income"`
}

type MemberDetails struct {
	Name          string  `json:"name"`
	Email         string  `json:"email"`
	Phone         string  `json:"phone"`
	CountryCode   string  `json:"country_code"`
	Dob           string  `json:"dob"`
	MaritalStatus string  `json:"marital_status"`
	Gender        string  `json:"gender"`
	Smoker        string  `json:"smoker"`
	Occupation    string  `json:"occupation"`
	Education     string  `json:"education"`
	AnnualIncome  float64 `json:"annual_income"`
}

type GenerateRecommendationResponse struct {
	QuoteID string `json:"quote_id"`
}

// GET

type GetRecommendationRequest struct {
	QuoteID string `json:"quote_id" validate:"required"`
}

// Recommendations
type GetRecommendationResponse struct {
	ID                string          `json:"id"`
	LeadID            string          `json:"lead_id"`
	PartnerID         string          `json:"partner_id"`
	EnterpriseID      string          `json:"enterprise_id"`
	SumInsured        float64         `json:"sum_insured"`
	Pincode           string          `json:"pincode"`
	Status            string          `json:"status"`
	TotalVariants     int             `json:"total_variants"`
	CompletedVariants int             `json:"completed_variants"`
	Recommendations   Recommendations `json:"recommendations"`
	Filters           []Filter        `json:"filters"`
	MemberDetails     MemberDetails   `json:"member_details"`
	PlanType          string          `json:"plan_type"`
	PaymentFrequency  string          `json:"payment_frequency"`
	PremiumPayingTerm int             `json:"premium_paying_term"`
	PolicyTerm        int             `json:"policy_term"`
}

type Recommendations struct {
	Companies []Company `json:"companies"`
}

type Company struct {
	ID                   string    `json:"id"`
	Name                 string    `json:"name"`
	Logo                 string    `json:"logo"`
	Description          string    `json:"description"`
	NetworkHospitalCount int       `json:"network_hospital_count"`
	ClaimSettlementRatio int       `json:"claim_settlement_ratio"`
	Products             []Product `json:"products"`
}

type Product struct {
	ID       string    `json:"id"`
	Name     string    `json:"name"`
	Variants []Variant `json:"variants"`
}

type Variant struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Premium     float64 `json:"premium"`
	Order       int     `json:"order"`
}

type Filter struct {
	Tag    string          `json:"tag"`
	Name   string          `json:"name"`
	Values json.RawMessage `json:"values"`
}
