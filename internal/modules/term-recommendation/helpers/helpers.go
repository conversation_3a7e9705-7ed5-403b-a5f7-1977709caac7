package helpers

import (
	"encoding/json"

	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term-recommendation/dto"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
)

func IntToInt32Ptr(value int) *int32 {
	val := int32(value)
	return &val
}

// float64ToPtr converts a float64 to *float64
func Float64ToPtr(value float64) *float64 {
	return &value
}

// stringToFrequencyPtr converts a string frequency to *PaymentFrequency enum
func StringToFrequencyPtr(frequency string) *term_recommendation.PaymentFrequency {
	var freq term_recommendation.PaymentFrequency
	switch frequency {
	case "MONTHLY":
		freq = term_recommendation.PaymentFrequency_MONTHLY
	case "QUARTERLY":
		freq = term_recommendation.PaymentFrequency_QUARTERLY
	case "HALF_YEARLY":
		freq = term_recommendation.PaymentFrequency_HALF_YEARLY
	case "YEARLY":
		freq = term_recommendation.PaymentFrequency_YEARLY
	default:
		freq = term_recommendation.PaymentFrequency_MONTHLY
	}
	return &freq
}

func ConvertRecommendations(protoRecommendations *term_recommendation.Recommendations) dto.Recommendations {
	dtoRecommendations := dto.Recommendations{
		Companies: make([]dto.Company, 0),
	}

	if protoCompanies := protoRecommendations.GetCompanies(); protoCompanies != nil {
		for _, protoCompany := range protoCompanies {
			dtoCompany := dto.Company{
				ID:                   protoCompany.GetId(),
				Name:                 protoCompany.GetName(),
				Logo:                 protoCompany.GetLogo(),
				Description:          protoCompany.GetDescription(),
				ClaimSettlementRatio: int(protoCompany.GetClaimSettlementRatio()),
				Products:             make([]dto.Product, 0),
			}

			if protoProducts := protoCompany.GetProducts(); protoProducts != nil {
				for _, protoProduct := range protoProducts {
					dtoProduct := dto.Product{
						ID:       protoProduct.GetId(),
						Name:     protoProduct.GetName(),
						Variants: make([]dto.Variant, 0),
					}

					if protoVariants := protoProduct.GetVariants(); protoVariants != nil {
						for _, protoVariant := range protoVariants {
							dtoVariant := dto.Variant{
								ID:      protoVariant.GetId(),
								Name:    protoVariant.GetName(),
								Premium: float64(protoVariant.GetPremium()),
								Order:   int(protoVariant.GetOrder()),
							}

							dtoProduct.Variants = append(dtoProduct.Variants, dtoVariant)
						}
					}

					dtoCompany.Products = append(dtoCompany.Products, dtoProduct)
				}
			}

			dtoRecommendations.Companies = append(dtoRecommendations.Companies, dtoCompany)
		}
	}

	return dtoRecommendations
}

func ConvertFilters(protoFilters []*term_recommendation.Filter) []dto.Filter {
	dtoFilters := make([]dto.Filter, 0, len(protoFilters))

	for _, protoFilter := range protoFilters {
		dtoFilter := dto.Filter{
			Tag:    protoFilter.GetTag(),
			Name:   protoFilter.GetName(),
			Values: json.RawMessage{}, // Default empty JSON
		}

		// Convert the proto struct to JSON if it exists
		if values := protoFilter.GetValues(); values != nil {
			if data, err := json.Marshal(values); err == nil {
				dtoFilter.Values = json.RawMessage(data)
			}
		}

		dtoFilters = append(dtoFilters, dtoFilter)
	}

	return dtoFilters
}

func ConvertProtoToDTO(response *term_recommendation.GetRecommendationResponse) *dto.GetRecommendationResponse {
	if response == nil {
		return nil
	}

	// Create base response structure
	dtoResponse := &dto.GetRecommendationResponse{
		ID:                response.GetId(),
		LeadID:            response.GetLeadId(),
		PartnerID:         response.GetPartnerId(),
		EnterpriseID:      response.GetEnterpriseId(),
		SumInsured:        float64(response.GetSumInsured()),
		Pincode:           response.GetPincode(),
		Status:            response.GetStatus(),
		TotalVariants:     int(response.GetTotalVariants()),
		CompletedVariants: int(response.GetCompletedVariants()),
		PremiumPayingTerm: int(response.GetPremiumPayingTerm()),
		PolicyTerm:        int(response.GetPolicyTerm()),
		PlanType:          response.GetPlanType().String(),
	}

	// Handle payment frequency
	switch response.GetPaymentFrequency() {
	case term_recommendation.PaymentFrequency_MONTHLY:
		dtoResponse.PaymentFrequency = "MONTHLY"
	case term_recommendation.PaymentFrequency_QUARTERLY:
		dtoResponse.PaymentFrequency = "QUARTERLY"
	case term_recommendation.PaymentFrequency_HALF_YEARLY:
		dtoResponse.PaymentFrequency = "HALF_YEARLY"
	case term_recommendation.PaymentFrequency_YEARLY:
		dtoResponse.PaymentFrequency = "YEARLY"
	}

	// Handle conversion of recommendations and filters
	protoRecommendations := response.GetRecommendations()
	if protoRecommendations != nil {
		dtoResponse.Recommendations = ConvertRecommendations(protoRecommendations)
	}

	// Convert Filters
	if protoFilters := response.GetFilters(); protoFilters != nil {
		dtoResponse.Filters = ConvertFilters(protoFilters)
	}

	// TODO: Implement filter conversion
	// dtoResponse.Filters = convertFiltersToDTO(response.GetFilters())

	return dtoResponse
}

func ConvertMemberDetailsToDTO(member *term_recommendation.MemberDetails) dto.MemberDetails {
	if member == nil {
		return dto.MemberDetails{}
	}

	return dto.MemberDetails{
		Name:          member.GetName(),
		Email:         member.GetEmail(),
		Phone:         member.GetPhone(),
		CountryCode:   member.GetCountryCode(),
		Dob:           member.GetDob(),
		MaritalStatus: member.GetMaritalStatus(),
		Gender:        member.GetGender(),
		Occupation:    member.GetOccupation(),
		AnnualIncome:  member.GetAnnualIncome(),
		Education:     member.GetEducation(),
		Smoker:        member.GetSmoker(),
		// Add other fields as needed
	}
}
