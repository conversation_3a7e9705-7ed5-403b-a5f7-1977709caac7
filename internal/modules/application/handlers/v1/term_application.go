package v1

import (
	"context"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/application/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	term_recommendation "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (h *Handler) GenerateTermApplication(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.GenerateApplicationRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	conn, err := h.GrpcClients.Get(term_recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting application client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Get purchase intent ID from request or token/cookie
	purchaseIntentID := req.PurchaseIntentID

	// Get enterprise ID and partner ID from token/cookie
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	enterpriseId := md.Get("enterprise_id")
	partnerId := md.Get("partner_id")
	isPublic := false
	if len(partnerId) == 0 {
		partnerId = md.Get("sub")
	}
	if purchaseIntentID == "" {
		isPublic = true
		purchaseIntentIDs := md.Get("purchase_intent_id")
		if len(purchaseIntentIDs) > 0 {
			purchaseIntentID = purchaseIntentIDs[0]
		}
	}

	// Create gRPC context with enterprise ID and partner ID
	gCtx := grpcCtx.(context.Context)
	if len(enterpriseId) > 0 {
		gCtx = metadata.AppendToOutgoingContext(gCtx, "enterprise_id", enterpriseId[0])
	}

	if len(partnerId) > 0 {
		gCtx = metadata.AppendToOutgoingContext(gCtx, "partner_id", partnerId[0])
	}

	client := term_recommendation.NewTermRecommendationClient(conn)

	purchaseIntentResponse, err := client.GetPurchaseIntent(gCtx, &term_recommendation.GetPurchaseIntentRequest{
		PurchaseIntentId: purchaseIntentID,
	})
	if err != nil {
		ctx.Logger.Error("Error getting purchase intent", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}
	isOffline := false
	if purchaseIntentResponse.GetIsOffline() {
		isOffline = true
	}

	createFormRequest := &term_recommendation.CreateFormRequest{
		PurchaseIntentId: purchaseIntentID,
		IsOffline:        &isOffline,
	}

	submission, err := client.CreateForm(gCtx, createFormRequest)
	if err != nil {
		ctx.Logger.Error("Error submitting form response", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	var resp dto.GenerateApplicationResponse

	if submission.GetSuccess() {
		resp.Message = "Created Form successfully"

		// Check if the request came from public endpoint

		// Generate token if the request is from public endpoint
		if isPublic {
			// Generate token directly
			authConn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
			if err != nil {
				ctx.Logger.Error("Error getting authentication client", zap.Error(err))
			} else {
				// Create authentication client
				authClient := auth.NewAuthClient(authConn)

				// Create custom claims
				customClaims := map[string]string{
					"purchase_intent_id": purchaseIntentID,
					"submission_id":      submission.GetSubmissionId(),
				}

				// Add enterprise_id and sub if available
				if len(enterpriseId) > 0 {
					customClaims["enterprise_id"] = enterpriseId[0]
				}

				if len(partnerId) > 0 {
					customClaims["sub"] = partnerId[0]
				}

				// Call authentication service to create JWT token
				tokenResponse, err := authClient.GenerateJWTToken(gCtx, &auth.GenerateJWTTokenRequest{
					Audience:     "gateway_public",
					CustomClaims: customClaims,
					ExpiresAt:    timestamppb.New(time.Now().Add(time.Hour * 24)),
				})

				if err != nil {
					ctx.Logger.Error("Error creating JWT token", zap.Error(err))
				} else {
					resp.Token = tokenResponse.GetAccessToken()
				}
			}
		} else {
			resp.SubmissionID = submission.GetSubmissionId()
		}
	} else {
		resp.Message = "Failed to create form"
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
