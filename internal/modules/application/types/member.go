package types

// Member represents different types of members available
type Member int32

const (
	// Self represents the self member
	Self Member = 0
	// Spouse represents the spouse member
	Spouse Member = 1
	// Father represents the father member
	Father Member = 2
	// Mother represents the mother member
	Mother Member = 3
	// Daughter represents the daughter member
	Daughter Member = 4
	// Son represents the son member
	Son Member = 5
)

// String returns the string representation of the PlanType
func (m Member) String() string {
	switch m {
	case Self:
		return "Self"
	case Spouse:
		return "Spouse"
	case Father:
		return "Father"
	case Mother:
		return "Mother"
	case Daughter:
		return "Daughter"
	default:
		return "UNKNOWN"
	}
}

// IsValid checks if the PlanType is valid
func (m Member) IsValid() bool {
	switch m {
	case Self, Spouse, Father, Mother, Daughter, Son:
		return true
	default:
		return false
	}
}

// FromString converts a string to PlanType
func MemberFromString(s string) (Member, bool) {
	switch s {
	case "Self":
		return Self, true
	case "Spouse":
		return Spouse, true
	case "Father":
		return Father, true
	case "Mother":
		return Mother, true
	case "Daughter":
		return Daughter, true
	default:
		return 0, false
	}
}

// ToProto converts the PlanType to its proto representation
// Note: This assumes you have imported the proto generated code
func (m Member) ToProto() int32 {
	return int32(m)
}

// FromProto creates a PlanType from a proto int32 value
func MemberFromProto(protoValue int32) (Member, bool) {
	member := Member(protoValue)
	return member, member.IsValid()
}
