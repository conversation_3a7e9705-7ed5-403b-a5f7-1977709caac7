package types

// PlanType represents different types of plans available
type PlanType int32

const (
	// <PERSON><PERSON> represents the base plan type
	BASE PlanType = 0
	// SUPER_TOP_UP represents the super top up plan type
	SUPER_TOP_UP PlanType = 1
)

// String returns the string representation of the PlanType
func (p PlanType) String() string {
	switch p {
	case BASE:
		return "BASE"
	case SUPER_TOP_UP:
		return "SUPER_TOP_UP"
	default:
		return "UNKNOWN"
	}
}

// IsValid checks if the PlanType is valid
func (p PlanType) IsValid() bool {
	switch p {
	case BASE, SUPER_TOP_UP:
		return true
	default:
		return false
	}
}

// FromString converts a string to PlanType
func PlanTypeFromString(s string) (PlanType, bool) {
	switch s {
	case "BASE":
		return BASE, true
	case "SUPER_TOP_UP":
		return SUPER_TOP_UP, true
	default:
		return 0, false
	}
}

// ToProto converts the PlanType to its proto representation
// Note: This assumes you have imported the proto generated code
func (p PlanType) ToProto() int32 {
	return int32(p)
}

// From<PERSON>rot<PERSON> creates a PlanType from a proto int32 value
func PlanTypeFromProto(protoValue int32) (PlanType, bool) {
	planType := PlanType(protoValue)
	return planType, planType.IsValid()
}
