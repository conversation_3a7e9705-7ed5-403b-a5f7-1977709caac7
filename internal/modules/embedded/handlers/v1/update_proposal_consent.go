package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	grpc "github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/embedded/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-affinity/v0/proposal"
	"github.com/oneassure-tech/oa-protos/go/oa-common/v0/common"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

func (h *Handler) UpdateProposalConsent(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	proposalID := ctx.GetParam("proposal_id")

	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := proposal.NewProposalClient(conn)

	enterpriseID, ok := ctx.GinCtx.Get(constant.EnterpriseIDKey)
	if !ok {
		ctx.Logger.Error("Enterprise ID not found in context")
		return nil, response.NewInternalServerError()
	}

	mCtx := metadata.NewOutgoingContext(context.Background(), metadata.New(map[string]string{
		constant.EnterpriseIDKey: enterpriseID.(string),
	}))

	_, err = client.UpdateProposal(mCtx, &proposal.UpdateProposalRequest{
		ProposalId: proposalID,
		Status:     common.ProposalStatus_PENDING,
	})

	if err != nil {
		ctx.Logger.Error("Error updating proposal", zap.Error(err))
		return nil, grpc.FromGrpcError(
			grpc.DownstreamProtocolHTTP,
			err,
		)
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.UpdateProposalConsentResponse{
			Status: common.ProposalStatus_PENDING.String(),
		},
	}, nil
}
