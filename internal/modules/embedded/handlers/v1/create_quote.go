package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	grpc "github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/embedded/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-affinity/v0/recommendation"
	"github.com/oneassure-tech/oa-protos/go/oa-common/v0/common"
	"go.uber.org/zap"
)

func (h *Handler) CreateQuote(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.CreateQuoteRequestBody

	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// enterpriseID, ok := ctx.GinCtx.Get(constant.EnterpriseIDKey)
	// if !ok {
	// 	ctx.Logger.Error("Enterprise ID not found in context")
	// 	return nil, response.NewInternalServerError()
	// }x

	// mCtx := metadata.NewOutgoingContext(ctx.GinCtx, metadata.New(map[string]string{
	// 	constant.EnterpriseIDKey: enterpriseID.(string),
	// }))

	conn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting grpc client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	recommendationClient := recommendation.NewRecommendationClient(conn)

	var affinityContext []*common.AffinityContext

	for _, contextData := range req.ContextData {
		affinityContext = append(affinityContext, &common.AffinityContext{
			Key:    contextData.Key,
			Values: contextData.Values,
		})
	}

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("Grpc metadata context not found in context")
		return nil, response.NewInternalServerError()
	}

	recommendations, err := recommendationClient.CreateQuote(grpcCtx.(context.Context), &recommendation.CreateQuoteRequest{
		Dob:         req.DateOfBirth,
		Gender:      recommendation.Gender(recommendation.Gender_value[req.Gender]),
		CoverAmount: req.CoverAmount,
		PolicyTerm:  int32(req.PolicyTerm),
		ContextData: affinityContext,
	})
	if err != nil {
		return nil, grpc.FromGrpcError(
			grpc.DownstreamProtocolHTTP,
			err,
		)
	}

	var recomendationRes []*dto.Recommendation

	for _, recommendation := range recommendations.Recommendations {

		recomendationRes = append(recomendationRes, &dto.Recommendation{
			InsurerName:      recommendation.InsurerName,
			InsurerLogo:      recommendation.InsurerLogo,
			ProductID:        recommendation.ProductId,
			ProductName:      recommendation.ProductName,
			VariantID:        recommendation.VariantId,
			VariantName:      recommendation.VariantName,
			BasePremium:      recommendation.BasePremium,
			GST:              recommendation.Gst,
			TotalPremium:     recommendation.TotalPremium,
			IsError:          recommendation.IsError,
			ErrorMessage:     recommendation.ErrorMessage,
			RecommendationID: recommendation.Id,
		})
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.CreateQuoteResponse{
			QuoteID:         recommendations.QuoteId,
			MatchedProducts: int(recommendations.MatchedProducts),
			Recommendations: recomendationRes,
		},
	}, nil
}
