package v1

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	grpc "github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/embedded/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-affinity/v0/proposal"
	"github.com/oneassure-tech/oa-protos/go/oa-common/v0/common"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/structpb"
)

func (h *Handler) GetProposerStatus(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	// _ := ctx.GetParam("proposal_id")

	// conn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	// if err != nil {
	// 	ctx.Logger.Error("Error getting forms client", zap.Error(err))

	// 	return nil, response.NewInternalServerError()
	// }

	// client := forms.NewFormsServiceClient(conn)

	// schema, err := client.GetFormSchema(context.Background(), &forms.GetFormSchemaRequest{
	// 	FormId: formID,
	// })

	// if err != nil {
	// 	ctx.Logger.Error("Error getting form schema", zap.Error(err))
	// 	return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	// }

	// formSchema := &dto.GetFormSchemaResponse{
	// 	FormID:     schema.GetFormId(),
	// 	FormSchema: json.RawMessage(protojson.Format(schema.GetFormSchema())),
	// 	UiSchema:   json.RawMessage(protojson.Format(schema.GetUiSchema())),
	// 	FormType:   dto.FormTypeNames[schema.GetFormType().String()],
	// }

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetProposalStatusResponse{
			ProposalID: "pro_18abj8830djjo3",
			Status:     "DRAFT",
		},
	}, nil
}

func (h *Handler) PatchProposal(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetProposalStatusResponse{
			ProposalID: "pro_18abj8830djjo3",
			Status:     "DRAFT",
		},
	}, nil
}

func (h *Handler) CreateProposal(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.CreateProposalRequestBody

	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	enterpriseID, ok := ctx.GinCtx.Get(constant.EnterpriseIDKey)
	if !ok {
		ctx.Logger.Error("Enterprise ID not found in context")
		return nil, response.NewInternalServerError()
	}

	mCtx := metadata.NewOutgoingContext(ctx.GinCtx, metadata.New(map[string]string{
		constant.EnterpriseIDKey: enterpriseID.(string),
	}))

	// Get the grpc client for the proposal service
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting grpc client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Create the proposal client with the connection
	proposalClient := proposal.NewProposalClient(conn)

	proposalData := h.createProposalData(req)
	proposalStruct, err := structpb.NewStruct(proposalData)
	if err != nil {
		ctx.Logger.Error("Error transforming proposal data into protobuf struct", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	contextData := make([]*common.AffinityContext, len(req.ContextData))
	for i, data := range req.ContextData {
		contextData[i] = &common.AffinityContext{
			Key:    data.Key,
			Values: data.Values,
		}
	}

	// Create the proposal request
	proposalResp, err := proposalClient.CreateProposal(mCtx, &proposal.CreateProposalRequest{
		QuoteId:          req.QuotationID,
		RecommendationId: req.RecommendationID,
		Status:           common.ProposalStatus(common.ProposalStatus_value[req.Status]),
		ProposalData:     proposalStruct,
		AffinityContext:  contextData,
	})
	if err != nil {
		return nil, grpc.FromGrpcError(
			grpc.DownstreamProtocolHTTP,
			err,
		)
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.CreateProposalResponse{
			ProposalID: proposalResp.GetProposalId(),
			Status:     req.Status,
		},
	}, nil
}

// mapOptionalString adds a field to the map only if the pointer is not nil
func (h *Handler) mapOptionalString(m map[string]interface{}, key string, value *string) {
	if value != nil {
		m[key] = *value
	}
}

// mapCountryCode adds a country code to the map, with default "+91" if nil
func (h *Handler) mapCountryCode(m map[string]interface{}, value *string) {
	if value != nil {
		m["country_code"] = *value
	} else {
		m["country_code"] = "+91" // Default as requested
	}
}

// mapAddress converts an Address DTO to a map with appropriate handling of nil pointers
func (h *Handler) mapAddress(address *dto.Address) map[string]interface{} {
	// For required addresses, we know they cannot be nil due to validation
	// But we'll keep a nil check for safety in case this function is used elsewhere
	if address == nil {
		return map[string]interface{}{
			"address_line_1": "",
			"address_line_2": "",
			"city":           "",
			"state":          "",
			"country":        "India", // Default country as requested
			"pincode":        "",
		}
	}

	addressMap := map[string]interface{}{
		"address_line_1": address.AddressLine1,
		"address_line_2": address.AddressLine2,
		"city":           address.City,
		"state":          address.State,
		"pincode":        address.Pincode,
	}

	// Only add optional fields if they are not nil
	h.mapOptionalString(addressMap, "address_line_3", address.AddressLine3)

	// Always set country (either from value or default)
	if address.Country != nil {
		addressMap["country"] = *address.Country
	} else {
		addressMap["country"] = "India" // Default country as requested
	}

	return addressMap
}

// mapCommonMemberFields sets common fields from CommonMember to the map
func (h *Handler) mapCommonMemberFields(m map[string]interface{}, member dto.CommonMember) {
	m["title"] = member.Title
	m["first_name"] = member.FirstName
	m["last_name"] = member.LastName
	m["email"] = member.Email
	m["mobile"] = member.Mobile
	m["date_of_birth"] = member.DateOfBirth
	m["gender"] = member.Gender
	m["nationality"] = member.Nationality

	// Optional fields
	h.mapOptionalString(m, "middle_name", member.MiddleName)

	// Fields with defaults
	h.mapCountryCode(m, member.CountryCode)

	// Required address
	m["address"] = h.mapAddress(member.Address)
}

// mapRequiredDocuments converts a slice of Document DTOs to a slice of map interfaces
// Use this for document fields marked as "required" in DTOs
func (h *Handler) mapRequiredDocuments(documents []*dto.Document) []interface{} {
	// Documents slice is required, but might be empty
	docs := make([]interface{}, 0)

	for _, document := range documents {
		docs = append(docs, map[string]interface{}{
			"document_type":   document.DocumentType,
			"document_number": document.DocumentNumber,
		})
	}

	return docs
}

func (h *Handler) createProposalData(req dto.CreateProposalRequestBody) map[string]interface{} {
	proposer := make(map[string]interface{})
	insuredMembers := make([]interface{}, len(req.InsuredMembers))
	nominee := make([]interface{}, len(req.Nominee))

	// Map common member fields from CommonMember
	h.mapCommonMemberFields(proposer, req.Proposer.CommonMember)

	// Map Proposer-specific fields (all required)
	proposer["marital_status"] = req.Proposer.MaritalStatus
	proposer["annual_income"] = req.Proposer.AnnualIncome
	proposer["occupation"] = req.Proposer.Occupation
	proposer["organization"] = req.Proposer.Organization
	proposer["education"] = req.Proposer.Education

	// Optional Proposer fields
	h.mapOptionalString(proposer, "employer", req.Proposer.Employer)

	// Handle Documents - Documents are required in DTO
	proposer["documents"] = h.mapRequiredDocuments(req.Proposer.Documents)

	for i, insuredMember := range req.InsuredMembers {
		memberMap := make(map[string]interface{})

		// Map common member fields
		h.mapCommonMemberFields(memberMap, insuredMember.CommonMember)

		// Map InsuredMember-specific required fields
		memberMap["marital_status"] = insuredMember.MaritalStatus
		memberMap["education"] = insuredMember.Education

		// Map InsuredMember-specific optional fields
		h.mapOptionalString(memberMap, "annual_income", insuredMember.AnnualIncome)
		h.mapOptionalString(memberMap, "occupation", insuredMember.Occupation)
		h.mapOptionalString(memberMap, "organization", insuredMember.Organization)
		h.mapOptionalString(memberMap, "employer", insuredMember.Employer)

		// Handle documents - Documents are required in DTO
		memberMap["documents"] = h.mapRequiredDocuments(insuredMember.Documents)

		insuredMembers[i] = memberMap
	}

	for i, n := range req.Nominee {
		nomineeMap := map[string]interface{}{
			"title":              n.Title,
			"first_name":         n.FirstName,
			"last_name":          n.LastName,
			"gender":             n.Gender,
			"date_of_birth":      n.DateOfBirth,
			"relationship":       n.Relationship,
			"nationality":        n.Nationality,
			"percentage_sharing": n.PercentageSharing,
		}

		// Handle optional pointer fields
		h.mapOptionalString(nomineeMap, "middle_name", n.MiddleName)
		h.mapOptionalString(nomineeMap, "mobile", n.Mobile)
		h.mapCountryCode(nomineeMap, n.CountryCode)

		// Handle Address - Address is required in DTO
		nomineeMap["address"] = h.mapAddress(n.Address)

		// Handle documents - Documents are required in DTO
		nomineeMap["documents"] = h.mapRequiredDocuments(n.Documents)

		// Handle appointee details - AppointeeDetails is optional
		if n.AppointeeDetails != nil {
			appointeeMap := map[string]interface{}{
				"relationship_to_nominee": n.AppointeeDetails.RelationshipToNominee,
				"title":                   n.AppointeeDetails.Title,
				"first_name":              n.AppointeeDetails.FirstName,
				"last_name":               n.AppointeeDetails.LastName,
				"gender":                  n.AppointeeDetails.Gender,
				"date_of_birth":           n.AppointeeDetails.DateOfBirth,
				"nationality":             n.AppointeeDetails.Nationality,
				"percentage_sharing":      n.AppointeeDetails.PercentageSharing,
			}

			// Handle optional pointer fields
			h.mapOptionalString(appointeeMap, "middle_name", n.AppointeeDetails.MiddleName)
			h.mapOptionalString(appointeeMap, "mobile", n.AppointeeDetails.Mobile)
			h.mapCountryCode(appointeeMap, n.AppointeeDetails.CountryCode)

			// Handle appointee address - Address is required in Appointee DTO
			appointeeMap["address"] = h.mapAddress(n.AppointeeDetails.Address)

			nomineeMap["appointee_details"] = appointeeMap
		}
		// Don't add appointee_details if nil - omit from JSON entirely

		nominee[i] = nomineeMap
	}

	return map[string]interface{}{
		"proposer":        proposer,
		"insured_members": insuredMembers,
		"nominee":         nominee,
	}
}

// func (h *Handler) createContextData(req dto.CreateProposalRequestBody) []map[string]interface{} {

// 	contextData := make([]map[string]interface{}, len(req.ContextData))

// 	for i, data := range req.ContextData {
// 		contextData[i] = map[string]interface{}{
// 			"key":    data.Key,
// 			"values": data.Values,
// 		}
// 	}

// 	return contextData
// }

// func (h *Handler) FetchFormResponse(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

// 	submissionID := ctx.GetParam("submission_id")

// 	conn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
// 	if err != nil {
// 		ctx.Logger.Error("Error getting forms client", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}

// 	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", "test-enterprise")

// 	client := forms.NewFormsServiceClient(conn)

// 	submission, err := client.FetchFormResponse(grpcCtx, &forms.FetchFormResponseRequest{
// 		SubmissionId: submissionID,
// 	})

// 	if err != nil {
// 		ctx.Logger.Error("Error getting submission", zap.Error(err))
// 		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
// 	}

// 	responseData := make(map[string]interface{})
// 	for _, data := range submission.GetResponseData() {
// 		responseData[data.GetSectionName()] = data.GetResponseData()
// 	}

// 	resp := &dto.GetFormResponseDataResponse{
// 		FormID:       submission.GetFormId(),
// 		SubmissionID: submission.GetSubmissionId(),
// 		Status:       dto.FormStatusNames[submission.GetStatus().String()],
// 		ResponseData: responseData,
// 	}

// 	if submission.GetStatus() == forms.FormStatus_DRAFT {
// 		resp.FormSchema = json.RawMessage(protojson.Format(submission.GetFormSchema()))
// 		resp.UiSchema = json.RawMessage(protojson.Format(submission.GetUiSchema()))
// 		resp.FormType = dto.FormTypeNames[submission.GetFormType().String()]
// 	}

// 	return &response.SuccessResponse{
// 		Status:  http.StatusOK,
// 		Payload: resp,
// 	}, nil
// }

// func (h *Handler) SubmitFormResponse(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

// 	submissionID := ctx.GetParam("submission_id")
// 	var req dto.SubmitFormRequest
// 	err := ctx.ExtractHttpRequest(&req)
// 	if err != nil {
// 		ctx.Logger.Error("Error extracting http request", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}

// 	conn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
// 	if err != nil {
// 		ctx.Logger.Error("Error getting forms client", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}

// 	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", "test-enterprise")
// 	client := forms.NewFormsServiceClient(conn)

// 	// Convert the response data to protobuf Struct
// 	pbStruct, err := structpb.NewStruct(req.ResponseData)
// 	if err != nil {
// 		ctx.Logger.Error("Error converting response data to protobuf struct", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}

// 	submission, err := client.SubmitFormResponse(grpcCtx, &forms.SubmitFormResponseRequest{
// 		FormId:       req.FormID,
// 		SubmissionId: submissionID,
// 		SectionName:  req.SectionName,
// 		ResponseData: pbStruct,
// 	})

// 	if err != nil {
// 		ctx.Logger.Error("Error submitting form response", zap.Error(err))
// 		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
// 	}

// 	resp := &dto.SubmitFormResponse{
// 		ResponseID: submission.GetResponseId(),
// 	}

// 	return &response.SuccessResponse{
// 		Status:  http.StatusOK,
// 		Payload: resp,
// 	}, nil
// }
