package embedded

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/embedded/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/embedded/handlers/v1"
)

type EmbeddedApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &EmbeddedApp{}
}

// SetAppName returns the name of the application
func (app *EmbeddedApp) SetAppName() string {
	return "embedded"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *EmbeddedApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	// Register HTTP routes
	apiRouter := appContext.Router[constant.HTTP_API]

	apiRouter.RegisterRoute(ctx, appName, "PATCH", "/proposal/:proposal_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.PatchProposal,
				// RequireValidation: true,
				ParamStruct:    &dto.PatchProposalRequestParams{},
				ReqBodyStruct:  &dto.PatchProposalRequestBody{},
				AuthMiddleware: []func(c *gin.Context) bool{middleware.BearerAuthMiddleware([]string{}, "gateway_api")},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/proposal/:proposal_id/status",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.GetProposerStatus,
				// RequireValidation: true,
				ParamStruct:    &dto.GetProposalStatusRequestParams{},
				AuthMiddleware: []func(c *gin.Context) bool{middleware.BearerAuthMiddleware([]string{}, "gateway_api")},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/proposal",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.CreateProposal,
				// RequireValidation: true,
				ReqBodyStruct:  &dto.CreateProposalRequestBody{},
				AuthMiddleware: []func(c *gin.Context) bool{middleware.BearerAuthMiddleware([]string{}, "gateway_api")},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/proposal/:proposal_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.GetProposal,
				// RequireValidation: true,
				ParamStruct:    &dto.GetProposalStatusRequestParams{},
				AuthMiddleware: []func(c *gin.Context) bool{middleware.BearerAuthMiddleware([]string{}, "gateway_api")},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/proposal/:proposal_id/consent",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.UpdateProposalConsent,
				// ReqBodyStruct:&dto.CreateProposalRequestBody{},
				// RequireValidation: true,
				ParamStruct:    &dto.UpdateProposalConsentRequestParams{},
				AuthMiddleware: []func(c *gin.Context) bool{middleware.BearerAuthMiddleware([]string{}, "gateway_api")},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/quote",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.CreateQuote,
				// RequireValidation: true,
				ReqBodyStruct: &dto.CreateQuoteRequestBody{},
				// AuthMiddleware:    middleware.BearerAuthMiddleware([]string{}, "gateway"),
				AuthMiddleware: []func(c *gin.Context) bool{middleware.BearerAuthMiddleware([]string{}, "gateway_api")},
			}),
		},
	)
}
