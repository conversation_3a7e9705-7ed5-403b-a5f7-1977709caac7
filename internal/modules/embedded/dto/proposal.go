package dto

type GetProposalStatusRequestParams struct {
	ProposalID string `uri:"proposal_id" validate:"required"`
}

type GetProposalStatusResponse struct {
	ProposalID string  `json:"proposal_id"`
	Status     string  `json:"status"`
	PolicyUrl  *string `json:"policy_url"`
}

type PatchProposalRequestParams struct {
	ProposalID string `uri:"proposal_id" validate:"required"`
}

type PatchProposalRequestBody struct {
	Status string `json:"status" validate:"required,oneof=DRAFT SUBMITTED"`
}

type Document struct {
	DocumentType   string `json:"document_type" validate:"required"`
	DocumentNumber string `json:"document_number" validate:"required"`
}

type Address struct {
	AddressLine1 string  `json:"address_line_1" validate:"required"`
	AddressLine2 string  `json:"address_line_2" validate:"required"`
	AddressLine3 *string `json:"address_line_3"`
	City         string  `json:"city" validate:"required"`
	State        string  `json:"state" validate:"required"`
	Country      *string `json:"country"`
	Pincode      string  `json:"pincode" validate:"required,numeric"`
}

type CommonMember struct {
	Title       string   `json:"title" validate:"required,oneof=MR MS MRS"`
	FirstName   string   `json:"first_name" validate:"required"`
	MiddleName  *string  `json:"middle_name"`
	LastName    string   `json:"last_name" validate:"required"`
	Email       string   `json:"email" validate:"required,email"`
	Mobile      string   `json:"mobile" validate:"required,numeric"`
	CountryCode *string  `json:"country_code"`
	DateOfBirth string   `json:"date_of_birth" validate:"required,datetime=02/01/2006"`
	Address     *Address `json:"address" validate:"required"`
	Nationality string   `json:"nationality" validate:"required,oneof=FN IND NRI OCI PIO"`
	Gender      string   `json:"gender" validate:"required,oneof=MALE FEMALE"`
}

type Proposer struct {
	CommonMember
	MaritalStatus string      `json:"marital_status" validate:"required,oneof=SINGLE SEPARATED MARRIED DIVORCED WIDOWED"`
	AnnualIncome  string      `json:"annual_income" validate:"required,numeric"`
	Occupation    string      `json:"occupation" validate:"required,oneof=LABOURER UNEMPLOYED HOUSEWIFE RETIRED OTHERS FARMER SALARIED SELF_EMPLOYED SERVICE STUDENT"`
	Organization  string      `json:"organization" validate:"required,oneof=GOVERNMENT PRIVATE_LTD PUBLIC_LTD OTHERS"`
	Education     string      `json:"education" validate:"required,oneof=POST_GRADUATE GRADUATE 12TH_PASS 10TH_PASS BELOW_10TH ILLITERATE"`
	Employer      *string     `json:"employer"`
	Documents     []*Document `json:"documents" validate:"required,dive"`
}

type InsuredMember struct {
	CommonMember
	MaritalStatus string      `json:"marital_status" validate:"required,oneof=SINGLE SEPARATED MARRIED DIVORCED WIDOWED"`
	AnnualIncome  *string     `json:"annual_income" validate:"numeric"`
	Occupation    *string     `json:"occupation" validate:"oneof=LABOURER UNEMPLOYED HOUSEWIFE RETIRED OTHERS FARMER SALARIED SELF_EMPLOYED SERVICE STUDENT"`
	Organization  *string     `json:"organization" validate:"oneof=GOVERNMENT PRIVATE_LTD PUBLIC_LTD OTHERS"`
	Education     string      `json:"education" validate:"oneof=POST_GRADUATE GRADUATE 12TH_PASS 10TH_PASS BELOW_10TH ILLITERATE"`
	Employer      *string     `json:"employer"`
	Documents     []*Document `json:"documents" validate:"required,dive"`
}

type Nominee struct {
	Title             string      `json:"title" validate:"required,oneof=MR MS MRS"`
	FirstName         string      `json:"first_name" validate:"required"`
	MiddleName        *string     `json:"middle_name"`
	LastName          string      `json:"last_name" validate:"required"`
	Gender            string      `json:"gender" validate:"required,oneof=MALE FEMALE"`
	DateOfBirth       string      `json:"date_of_birth" validate:"required,datetime=02/01/2006"`
	Mobile            *string     `json:"mobile" validate:"numeric"`
	CountryCode       *string     `json:"country_code"`
	Relationship      string      `json:"relationship" validate:"required,oneof=HUSBAND SPOUSE FATHER MOTHER SON DAUGHTER OTHERS BROTHER SISTER WIFE NEPHEW GRANDMOTHER UNCLE SISTER_IN_LAW GRANDFATHER"`
	Address           *Address    `json:"address" validate:"required"`
	Nationality       string      `json:"nationality" validate:"required,oneof=FN IND NRI OCI PIO"`
	PercentageSharing float64     `json:"percentage_sharing" validate:"required,min=0,max=100"`
	Documents         []*Document `json:"documents" validate:"required,dive"`
	AppointeeDetails  *Appointee  `json:"appointee_details"`
}

type Appointee struct {
	RelationshipToNominee string   `json:"relationship_to_nominee" validate:"required,oneof=HUSBAND SPOUSE FATHER MOTHER SON DAUGHTER OTHERS BROTHER SISTER WIFE NEPHEW GRANDMOTHER UNCLE SISTER_IN_LAW GRANDFATHER"`
	Title                 string   `json:"title" validate:"required,oneof=MR MS MRS"`
	FirstName             string   `json:"first_name" validate:"required"`
	MiddleName            *string  `json:"middle_name"`
	LastName              string   `json:"last_name" validate:"required"`
	Gender                string   `json:"gender" validate:"required,oneof=MALE FEMALE"`
	DateOfBirth           string   `json:"date_of_birth" validate:"required,datetime=02/01/2006"`
	Mobile                *string  `json:"mobile" validate:"numeric"`
	CountryCode           *string  `json:"country_code"`
	Address               *Address `json:"address" validate:"required"`
	Nationality           string   `json:"nationality" validate:"required,oneof=FN IND NRI OCI PIO"`
	PercentageSharing     float64  `json:"percentage_sharing" validate:"required,min=0,max=100"`
}

type ContextData struct {
	Key    string   `json:"key" validate:"required"`
	Values []string `json:"values" validate:"required,gt=0,dive,required"`
}

// CreateProposalRequestBody represents the request body for creating a new proposal
type CreateProposalRequestBody struct {
	QuotationID      string           `json:"quote_id" validate:"required"`
	RecommendationID string           `json:"recommendation_id" validate:"required"`
	Status           string           `json:"status" validate:"required,oneof=DRAFT SUBMITTED"`
	Proposer         *Proposer        `json:"proposer" validate:"required"`
	InsuredMembers   []*InsuredMember `json:"insured_members" validate:"required,gt=0,dive,required"`
	Nominee          []*Nominee       `json:"nominee" validate:"dive,required"`
	ContextData      []*ContextData   `json:"context_data" validate:"dive,required"`
}

type CreateProposalResponse struct {
	ProposalID string `json:"proposal_id"`
	Status     string `json:"status"`
}

type UpdateProposalConsentRequestParams struct {
	ProposalID string `uri:"proposal_id" validate:"required"`
}

type UpdateProposalConsentResponse struct {
	Status string `json:"status"`
}

type GetProposalRequestParams struct {
	ProposalID string `uri:"proposal_id" validate:"required"`
}

type GetProposalResponse struct {
	ProposerName string `json:"proposer_name"`
}
