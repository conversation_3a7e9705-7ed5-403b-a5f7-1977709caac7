package dto

type CreateQuoteRequestBody struct {
	DateOfBirth string         `json:"date_of_birth" validate:"required,datetime=02/01/2006"`
	Gender      string         `json:"gender" validate:"required,oneof=MALE FEMALE"`
	CoverAmount string         `json:"cover_amount" validate:"required,numeric"`
	PolicyTerm  int            `json:"policy_term" validate:"required,min=1"`
	ContextData []*ContextData `json:"context_data" validate:"dive,required"`
}

type CreateQuoteResponse struct {
	QuoteID         string            `json:"quote_id"`
	MatchedProducts int               `json:"matched_products"`
	Recommendations []*Recommendation `json:"recommendations"`
}

type Recommendation struct {
	RecommendationID string `json:"recommendation_id"`
	InsurerName      string `json:"insurer_name"`
	InsurerLogo      string `json:"insurer_logo"`
	ProductID        string `json:"product_id"`
	ProductName      string `json:"product_name"`
	VariantID        string `json:"variant_id"`
	VariantName      string `json:"variant_name"`
	BasePremium      string `json:"base_premium"`
	GST              string `json:"gst"`
	TotalPremium     string `json:"total_premium"`
	IsError          bool   `json:"is_error"`
	ErrorMessage     string `json:"error_message"`
}
