package dto

type Variant struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type Product struct {
	ID       string     `json:"id"`
	Name     string     `json:"name"`
	Type     string     `json:"type"`
	Variants []*Variant `json:"variants"`
}

type GetTopInsurersResponse struct {
	ID       string     `json:"id"`
	Name     string     `json:"name"`
	Logo     string     `json:"logo"`
	Products []*Product `json:"products"`
}

type SearchTopInsurersRequest struct {
	Query string `form:"query"`
}

type SearchTopInsurersResponse struct {
	ID       string     `json:"id"`
	Name     string     `json:"name"`
	Logo     string     `json:"logo"`
	Products []*Product `json:"products"`
}
