package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/catalog/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/health"

	"google.golang.org/protobuf/types/known/emptypb"

	"go.uber.org/zap"
)

func (h *Handler) GetTopInsurers(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	grpcClient, err := h.GrpcClients.Get(health.HealthService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting grpc client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := health.NewHealthServiceClient(grpcClient)

	topInsurers, err := client.GetTopInsurers(context.Background(), &emptypb.Empty{})
	if err != nil {
		ctx.Logger.Error("Error getting top insurers", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Parse Variants
	var r []*dto.GetTopInsurersResponse
	for _, i := range topInsurers.GetInsurer() {
		var insurer dto.GetTopInsurersResponse
		insurer.ID = i.GetId()
		insurer.Name = i.GetName()
		insurer.Logo = i.GetLogo()
		insurer.Products = make([]*dto.Product, 0)
		for _, p := range i.GetProducts() {
			insurer.Products = append(insurer.Products, &dto.Product{
				ID:       p.GetId(),
				Name:     p.GetName(),
				Type:     p.GetType().String(),
				Variants: make([]*dto.Variant, 0),
			})
			for _, v := range p.GetVariants() {
				insurer.Products[len(insurer.Products)-1].Variants = append(insurer.Products[len(insurer.Products)-1].Variants, &dto.Variant{
					ID:   v.GetId(),
					Name: v.GetName(),
				})
			}
		}
		r = append(r, &insurer)
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: r,
	}, nil
}
