package catalog

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/catalog/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/catalog/handlers/v1"
)

type catalogApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &catalogApp{}
}

// SetAppName returns the name of the application
func (app *catalogApp) SetAppName() string {
	return "catalog"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *catalogApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}
	requireAuth := true
	apiRouter := appContext.Router[constant.HTTP_API]

	apiRouter.RegisterRoute(ctx, appName, "GET", "insurers/top",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetTopInsurers,
				RequireAuth:       &requireAuth,
				RequireValidation: false,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware(
						[]string{},
						"gateway_portal",
						[]string{},
					),
				},
			}),
		},
	)
	apiRouter.RegisterRoute(ctx, appName, "GET", "insurers/search",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.SearchInsurers,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				QueryStruct:       &dto.SearchTopInsurersRequest{},
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware(
						[]string{},
						"gateway_portal",
						[]string{},
					),
				},
			}),
		},
	)

}
