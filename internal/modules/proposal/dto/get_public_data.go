package dto

type GetPublicDataRequest struct {
	Token string `json:"token"`
}

// StepInfo represents common step information
type StepInfo struct {
	Status    string `json:"status"`
	CreatedAt string `json:"created_at,omitempty"`
	NextStep  string `json:"next_step,omitempty"`
}

// ApplicationStep represents the application step in the proposal lifecycle
type ApplicationStep struct {
	StepInfo *StepInfo `json:"step_info,omitempty"`
	Metadata struct {
		LatestProposalID string `json:"latest_proposal_id,omitempty"`
	} `json:"metadata,omitempty"`
}

// KycStep represents the KYC step in the proposal lifecycle
type KycStep struct {
	StepInfo *StepInfo `json:"step_info,omitempty"`
	Metadata struct {
		KycID                  string      `json:"kyc_id,omitempty"`
		KycType                string      `json:"kyc_type,omitempty"`
		KycNumber              string      `json:"kyc_number,omitempty"`
		KycDate                string      `json:"kyc_date,omitempty"`
		RedirectURL            string      `json:"redirect_url,omitempty"`
		SupportedDocumentTypes interface{} `json:"supported_document_types,omitempty"`
		ManualVerification     bool        `json:"manual_verification,omitempty"`
	} `json:"metadata,omitempty"`
}

// ProposalStep represents the proposal step in the proposal lifecycle
type ProposalStep struct {
	StepInfo *StepInfo `json:"step_info,omitempty"`
	Metadata struct {
		InsurerProposalID string `json:"insurer_proposal_id,omitempty"`
	} `json:"metadata,omitempty"`
}

// PaymentStep represents the payment step in the proposal lifecycle
type PaymentStep struct {
	StepInfo *StepInfo `json:"step_info,omitempty"`
	Metadata struct {
		PaymentID      string      `json:"payment_id,omitempty"`
		PaymentType    string      `json:"payment_type,omitempty"`
		RedirectURL    string      `json:"redirect_url,omitempty"`
		AdditionalInfo interface{} `json:"additional_info,omitempty"`
	} `json:"metadata,omitempty"`
}

// PolicyStep represents the policy step in the proposal lifecycle
type PolicyStep struct {
	StepInfo *StepInfo `json:"step_info,omitempty"`
	Metadata struct {
		PolicyID        string `json:"policy_id,omitempty"`
		PolicyNumber    string `json:"policy_number,omitempty"`
		PolicyStartDate string `json:"policy_start_date,omitempty"`
		PolicyEndDate   string `json:"policy_end_date,omitempty"`
		PolicyStatus    string `json:"policy_status,omitempty"`
		URL             string `json:"url,omitempty"`
	} `json:"metadata,omitempty"`
}

// LifecycleInfo represents all steps in the proposal lifecycle
type LifecycleInfo struct {
	Application      *ApplicationStep `json:"application,omitempty"`
	Kyc              *KycStep         `json:"kyc,omitempty"`
	Proposal         *ProposalStep    `json:"proposal,omitempty"`
	Payment          *PaymentStep     `json:"payment,omitempty"`
	Policy           *PolicyStep      `json:"policy,omitempty"`
	CustomerConsent  bool             `json:"customer_consent,omitempty"`
	RejectionReason  string           `json:"rejection_reason,omitempty"`
	ProposalIntentID string           `json:"proposal_intent_id,omitempty"`
	PurchaseIntentID string           `json:"purchase_intent_id,omitempty"`
	InsurerID        string           `json:"insurer_id,omitempty"`
	ProductID        string           `json:"product_id,omitempty"`
	ProposalID       string           `json:"proposal_id,omitempty"`
}

// ProposalData defines the structure returned by GetProposalData
type ProposalData struct {
	ProposalIntentID string         `json:"proposal_intent_id"`
	Name             string         `json:"name"`
	Phone            string         `json:"phone"`
	CompanyName      string         `json:"company_name"`
	ProductName      string         `json:"product_name"`
	VariantName      string         `json:"variant_name"`
	SumInsured       string         `json:"sum_insured"`
	TotalPremium     string         `json:"total_premium"`
	TotalGST         string         `json:"total_gst"`
	TotalAmount      string         `json:"total_amount"`
	SubmissionID     string         `json:"submission_id"`
	PurchaseIntentID string         `json:"purchase_intent_id"`
	ProposalStatus   string         `json:"proposal_status"`
	InsurerLogo      string         `json:"insurer_logo"`
	RejectionReason  string         `json:"rejection_reason"`
	RiderData        []RiderData    `json:"rider_data,omitempty"`
	Info             *LifecycleInfo `json:"info,omitempty"`
	Tenure           string         `json:"tenure"`
}

type Summary struct {
	CustomerName       string `json:"customer_name"`
	CustomerPhone      string `json:"customer_phone"`
	CompanyName        string `json:"company_name"`
	ProductName        string `json:"product_name"`
	VariantName        string `json:"variant_name"`
	InsurerLogo        string `json:"insurer_logo"`
	SumInsured         string `json:"sum_insured"`
	BasePremium        string `json:"base_premium"`
	TotalGST           string `json:"total_gst"`
	TotalAmount        string `json:"total_amount"`
	ConsentDocumentURL string `json:"consent_document_url"`
	Tenure             string `json:"tenure"`
}
type ResponseData struct {
	SectionName  string                 `json:"section_name"`
	ResponseData map[string]interface{} `json:"response_data"`
}

type ApplicationForm struct {
	FormSchema   map[string]interface{} `json:"form_schema"`
	ResponseData []ResponseData         `json:"response_data"`
	Status       string                 `json:"status"`
	SubmissionID string                 `json:"submission_id"`
}

type RiderData struct {
	Name     string `json:"name"`
	Sequence int    `json:"sequence"`
}

// GetPublicDataResponse defines the response structure for the public data API
type GetPublicDataResponse struct {
	ApplicationForm *ApplicationForm `json:"application_form"`
	Summary         *Summary         `json:"summary,omitempty"`
	Lifecycle       *LifecycleInfo   `json:"lifecycle,omitempty"`
	Riders          []RiderData      `json:"riders,omitempty"`
	Proposal        *ProposalData    `json:"proposal,omitempty"`
}
