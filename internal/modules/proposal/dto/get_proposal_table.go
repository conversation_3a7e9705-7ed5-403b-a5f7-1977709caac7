package dto

// // CreateClientRequest represents the request to create a new client
// type GetProposalTableRequest struct {
// 	EnterpriseID string `json:"enterprise_id" validate:"required"`
// }

// // CreateClientResponse represents the response after creating a new client
// // type CreateClientResponse struct {
// // 	EnterpriseID string `json:"enterprise_id"`
// // 	ClientID     string `json:"client_id"`
// // 	ClientSecret string `json:"client_secret"` // Plain text secret, only returned once
// // 	IsEnabled    bool   `json:"is_enabled"`
// // }

type GetProposalTableRequest struct {
	Limit                 int    `form:"limit"`
	StartDate             string `form:"start_date"`
	EndDate               string `form:"end_date"`
	LastProposalIntentID  string `form:"last_proposal_intent_id"`
	FirstProposalIntentID string `form:"first_proposal_intent_id"`
	Status                string `form:"status"`
}

// ProposalTableData represents individual proposal data for table display
type ProposalTableData struct {
	ProposalIntentID  string  `json:"proposal_intent_id"`
	ProposalID        string  `json:"proposal_id"`
	Name              string  `json:"name"`
	Phone             string  `json:"phone"`
	Email             string  `json:"email"`
	InsurerName       string  `json:"insurer_name"`
	ProductName       string  `json:"product_name"`
	VariantName       string  `json:"variant_name"`
	Tenure            string  `json:"tenure"`
	NetPremium        string  `json:"net_premium"`
	TotalPremium      string  `json:"total_premium"`
	Status            string  `json:"status"`
	SubmissionID      string  `json:"submission_id"`
	CreatedAt         string  `json:"created_at"`
	UpdatedAt         string  `json:"updated_at"`
	StatusCreatedAt   string  `json:"status_created_at"`
	ReferenceID       string  `json:"reference_id"`
	InsurerProposalID *string `json:"insurer_proposal_id,omitempty"`
	PurchaseIntentID  string  `json:"purchase_intent_id"`
	PartnerName       string  `json:"partner_name"`
	EnterpriseName    string  `json:"enterprise_name"`
}

// GetProposalTableResponse represents the response for getting proposal table data
type GetProposalTableResponse struct {
	Status          string              `json:"status"`
	Message         string              `json:"message"`
	Proposals       []ProposalTableData `json:"proposals"`
	CreatedAt       string              `json:"created_at"`
	LastProposalID  string              `json:"last_proposal_id"`
	FirstProposalID string              `json:"first_proposal_id"`
	HasNext         bool                `json:"has_next"`
	HasPrevious     bool                `json:"has_previous"`
	Count           int32               `json:"count"`
}
