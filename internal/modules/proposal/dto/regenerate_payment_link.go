package dto

// // CreateClientRequest represents the request to create a new client
// type GetProposalTableRequest struct {
// 	EnterpriseID string `json:"enterprise_id" validate:"required"`
// }

// // CreateClientResponse represents the response after creating a new client
// // type CreateClientResponse struct {
// // 	EnterpriseID string `json:"enterprise_id"`
// // 	ClientID     string `json:"client_id"`
// // 	ClientSecret string `json:"client_secret"` // Plain text secret, only returned once
// // 	IsEnabled    bool   `json:"is_enabled"`
// // }

type RegeneratePaymentLinkRequest struct {
	ProposalIntentId string `json:"proposal_intent_id"`
}
