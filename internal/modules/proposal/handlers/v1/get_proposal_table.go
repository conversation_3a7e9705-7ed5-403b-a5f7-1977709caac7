package v1

import (
	"context"
	"net/http"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/proposal/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-gateway-svc/internal/utility"
	proposal "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
)

func (h *Handler) GetProposalTable(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := proposal.NewProposalClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Check For Limit and conver to int32
	limit := ctx.GinCtx.Query("limit")
	if limit == "" {
		limit = "10"
	}

	parsedLimit, err := strconv.ParseInt(limit, 10, 32)
	if err != nil {
		ctx.Logger.Error("Error converting limit to int32", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	limitInt := int32(parsedLimit)

	startDate := ctx.GinCtx.Query("start_date")
	endDate := ctx.GinCtx.Query("end_date")
	lastProposalId := ctx.GinCtx.Query("last_proposal_id")
	status := ctx.GinCtx.Query("status")
	firstProposalId := ctx.GinCtx.Query("first_proposal_id")

	res, err := client.GetProposalTable(grpcCtx.(context.Context), &proposal.GetProposalTableRequest{
		Limit:           &limitInt,
		StartDate:       &startDate,
		EndDate:         &endDate,
		LastProposalId:  &lastProposalId,
		StatusFilter:    &status,
		FirstProposalId: &firstProposalId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting proposal table", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	partnerIds := make([]string, 0)
	for _, proposal := range res.Proposals {
		if proposal.PartnerId != "" {
			partnerIds = append(partnerIds, proposal.PartnerId)
		}
	}

	role := ctx.GinCtx.GetString("role")
	enterpriseType := ctx.GinCtx.GetString("enterprise_type")

	partnerMap, err := utility.GetPartnerEnterpriseName(
		grpcCtx.(context.Context),
		h.GrpcClients,
		ctx.Logger,
		role,
		enterpriseType,
		partnerIds,
	)
	if err != nil {
		ctx.Logger.Error("Error getting partner enterprise names", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Convert gRPC response to DTO
	proposals := make([]dto.ProposalTableData, len(res.Proposals))
	for i, p := range res.Proposals {
		proposals[i] = dto.ProposalTableData{
			ProposalIntentID:  p.ProposalIntentId,
			ProposalID:        p.ProposalId,
			Name:              p.Name,
			Phone:             p.Phone,
			Email:             p.Email,
			InsurerName:       p.InsurerName,
			ProductName:       p.ProductName,
			VariantName:       p.VariantName,
			Tenure:            p.Tenure,
			NetPremium:        p.NetPremium,
			TotalPremium:      p.TotalPremium,
			Status:            p.Status,
			SubmissionID:      p.SubmissionId,
			CreatedAt:         p.CreatedAt,
			UpdatedAt:         p.UpdatedAt,
			StatusCreatedAt:   p.StatusCreatedAt,
			ReferenceID:       p.ReferenceId,
			InsurerProposalID: p.InsurerProposalId,
			PurchaseIntentID:  p.PurchaseIntentId,
		}
		if partnerInfo, exists := partnerMap[p.PartnerId]; exists {
			proposals[i].PartnerName = partnerInfo.PartnerName
			proposals[i].EnterpriseName = partnerInfo.EnterpriseName
		}
	}

	responseDTO := dto.GetProposalTableResponse{
		Status:          res.Status,
		Message:         res.Message,
		Proposals:       proposals,
		CreatedAt:       res.CreatedAt,
		LastProposalID:  res.LastProposalId,
		FirstProposalID: res.FirstProposalId,
		HasNext:         res.HasNext,
		HasPrevious:     res.HasPrevious,
		Count:           res.Count,
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: responseDTO,
	}, nil
}
