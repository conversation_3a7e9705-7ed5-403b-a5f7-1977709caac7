package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
)

func (h *Handler) KycHandler(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Get Insurer Id, Proposal Id and Product Id from the query params
	insurerId := ctx.GinCtx.Query("insurer_id")
	proposalId := ctx.GinCtx.Query("proposal_id")
	productId := ctx.GinCtx.Query("product_id")

	if insurerId == "" || proposalId == "" || productId == "" {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Title:  "Bad Request",
				Detail: "Insurer ID, Proposal ID and Product ID are required",
			},
		}
	}

	// Call the Kyc Endpoint
	_, err := h.signalKyc(ctx, insurerId, proposalId, productId)
	if err != nil {
		return nil, err
	}

	return &response.SuccessResponse{
		Status: 200,
	}, nil
}

func (h *Handler) signalKyc(ctx *handler.HandlerContext, insurerId string, proposalId string, productId string) (*proposal.SignalKycResponse, *response.ErrorResponse) {

	log := ctx.Logger
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Failed to connect to proposal service: %v", zap.Error(err))
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed to connect to proposal service",
			},
		}
	}

	client := proposal.NewProposalClient(conn)

	req := proposal.SignalKycRequest{
		InsurerId:  insurerId,
		ProductId:  productId,
		ProposalId: proposalId,
	}

	result, err := client.SignalKyc(context.Background(), &req)
	if err != nil {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed to signal kyc",
			},
		}
	}

	return result, nil
}
