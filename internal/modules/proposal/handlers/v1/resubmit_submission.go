package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/proposal/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	proposal "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
)

func (h *<PERSON>ler) ResubmitSubmission(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.ResubmitSubmissionRequest

	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := proposal.NewProposalClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	res, err := client.CreateSubmissionIdWithProposalIntent(grpcCtx.(context.Context), &proposal.CreateSubmissionIdWithProposalIntentRequest{
		ProposalIntentId: req.ProposalIntentID,
	})

	if err != nil {
		ctx.Logger.Error("Error resubmitting submission", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.ResubmitSubmissionResponse{
			SubmissionID:     res.GetSubmissionId(),
			PurchaseIntentID: res.GetPurchaseIntentId(),
		},
	}, nil
}
