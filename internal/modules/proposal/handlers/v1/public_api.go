package v1

import (
	"context"
	"net/http"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/proposal/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-gateway-svc/internal/utility"
	catalog_metadata "github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/metadata"
	"github.com/oneassure-tech/oa-protos/go/oa-forms/v0/forms"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	proposal "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

func (h *Handler) GetPublicData(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}
	decodedToken := make(map[string]interface{})
	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	for _, claim := range []string{"enterprise_id", "partner_id", "purchase_intent_id", "submission_id"} {
		claimValue := md.Get(claim)

		if len(claimValue) == 0 {
			if claim == "partner_id" {
				subClaim := md.Get("sub")
				if len(subClaim) != 0 {
					decodedToken[claim] = subClaim[0]
					continue
				}
			}
			parsedClaim := ctx.GinCtx.Request.URL.Query().Get(claim)
			if parsedClaim != "" {
				decodedToken[claim] = parsedClaim
				continue
			}
			ctx.Logger.Error("Missing required claim", zap.String("claim", claim))
			return nil, response.NewInternalServerError()
		}
		decodedToken[claim] = claimValue[0]
	}

	// Call The Get Proposal Endpoint on the sales to get the proposal data
	proposalData := h.GetProposalData(ctx, decodedToken)

	if proposalData == nil {
		// Get Purchase Intent Data
		purchaseIntentData := h.GetPurchaseIntentData(ctx, decodedToken)
		if purchaseIntentData == nil {
			return nil, response.NewInternalServerError()
		}
		// Get The Form Config With Form Responses
		formSubmissionId := decodedToken["submission_id"].(string)
		if formSubmissionId == "" {
			formSubmissionId = purchaseIntentData.SubmissionId
		}
		formResponse := h.GetFormConfigData(ctx, formSubmissionId, decodedToken)
		if formResponse == nil {
			return nil, response.NewInternalServerError()
		}

		FormResponseData := make([]dto.ResponseData, 0)
		for _, data := range formResponse.GetResponseData() {
			FormResponseData = append(FormResponseData, dto.ResponseData{
				SectionName:  data.GetSectionName(),
				ResponseData: data.GetResponseData().AsMap(),
			})
		}

		// Extract rider data from purchase intent
		riderData := make([]dto.RiderData, 0)
		if purchaseIntentData.Riders != nil {
			for _, rider := range purchaseIntentData.Riders {
				if rider.IsSelected || rider.IsMandatory {
					riderData = append(riderData, dto.RiderData{
						Name:     rider.Name,
						Sequence: int(rider.Sequence),
					})
				}
			}
		}
		consentDocumentURL := h.GetConsentDocumentURL(ctx, decodedToken, purchaseIntentData.ProductId)

		res := &dto.GetPublicDataResponse{
			ApplicationForm: &dto.ApplicationForm{
				SubmissionID: formResponse.SubmissionId,
				FormSchema:   formResponse.FormSchema.AsMap(),
				ResponseData: FormResponseData,
				Status:       formResponse.Status.String(),
			},
			Summary: &dto.Summary{
				CustomerName:       purchaseIntentData.CustomerName,
				CustomerPhone:      purchaseIntentData.CustomerPhone,
				CompanyName:        purchaseIntentData.VariantStaticDetails.InsurerName,
				ProductName:        purchaseIntentData.VariantStaticDetails.VariantName,
				VariantName:        purchaseIntentData.VariantStaticDetails.VariantName,
				InsurerLogo:        purchaseIntentData.VariantStaticDetails.InsurerLogo,
				SumInsured:         strconv.FormatInt(purchaseIntentData.SumInsured, 10),
				BasePremium:        strconv.FormatFloat(utility.RoundFloat64(float64(purchaseIntentData.BasePremium), 2), 'f', -1, 64),
				TotalGST:           strconv.FormatFloat(utility.RoundFloat64(float64(purchaseIntentData.TotalGst), 2), 'f', -1, 64),
				TotalAmount:        strconv.FormatFloat(utility.RoundFloat64(float64(purchaseIntentData.TotalPremium), 2), 'f', -1, 64),
				ConsentDocumentURL: consentDocumentURL,
				Tenure:             strconv.FormatInt(int64(purchaseIntentData.Tenure), 10),
			},
			Riders: riderData,
		}

		return &response.SuccessResponse{
			Status:  http.StatusOK,
			Payload: res,
		}, nil
	}

	// Get The Form Config With Form Responses
	formResponse := h.GetFormConfigData(ctx, proposalData.SubmissionID, decodedToken)
	if formResponse == nil {
		return nil, response.NewInternalServerError()
	}

	responseData := make([]dto.ResponseData, 0)
	for _, data := range formResponse.GetResponseData() {
		responseData = append(responseData, dto.ResponseData{
			SectionName:  data.GetSectionName(),
			ResponseData: data.GetResponseData().AsMap(),
		})
	}

	// Extract rider data from proposal
	riderData := make([]dto.RiderData, 0)
	if proposalData.RiderData != nil {
		for _, rider := range proposalData.RiderData {
			riderData = append(riderData, dto.RiderData{
				Name:     rider.Name,
				Sequence: int(rider.Sequence),
			})
		}
	}

	res := &dto.GetPublicDataResponse{
		ApplicationForm: &dto.ApplicationForm{
			FormSchema:   formResponse.FormSchema.AsMap(),
			ResponseData: responseData,
			Status:       formResponse.Status.String(),
			SubmissionID: proposalData.SubmissionID,
		},
		Summary: &dto.Summary{
			CustomerName:  proposalData.Name,
			CustomerPhone: proposalData.Phone,
			CompanyName:   proposalData.CompanyName,
			ProductName:   proposalData.ProductName,
			VariantName:   proposalData.VariantName,
			InsurerLogo:   proposalData.InsurerLogo,
			SumInsured:    proposalData.SumInsured,
			BasePremium:   proposalData.TotalPremium,
			TotalGST:      proposalData.TotalGST,
			TotalAmount:   proposalData.TotalAmount,
			Tenure:        proposalData.Tenure,
		},
		Lifecycle: proposalData.Info,
		Riders:    riderData,
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}

func (h *Handler) GetProposalData(ctx *handler.HandlerContext, decodedToken map[string]interface{}) *dto.ProposalData {
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil
	}

	policyConn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil
	}

	client := proposal.NewProposalClient(conn)
	policyClient := policies.NewPoliciesClient(policyConn)

	submissionId := decodedToken["submission_id"].(string)
	purchaseIntentId := decodedToken["purchase_intent_id"].(string)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil
	}

	proposalRes, err := client.GetProposal(grpcCtx.(context.Context), &proposal.GetProposalRequest{
		PurchaseIntentId: purchaseIntentId,
		SubmissionId:     &submissionId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting proposal", zap.Error(err))
		return nil
	}

	if proposalRes.SubmissionId != decodedToken["submission_id"].(string) {
		return nil
	}

	riderData := make([]dto.RiderData, 0)
	if proposalRes.RiderData != nil {
		for index, rider := range proposalRes.RiderData.AsSlice() {
			riderMap, ok := rider.(map[string]interface{})
			if !ok {
				continue
			}
			riderName, ok := riderMap["name"].(string)
			if !ok {
				continue
			}
			sequence, _ := riderMap["sequence"].(int32)
			riderData = append(riderData, dto.RiderData{
				Name:     riderName,
				Sequence: int(sequence) | index,
			})
		}
	}

	res, err := client.GetProposalLifecycle(grpcCtx.(context.Context), &proposal.GetProposalLifecycleRequest{
		ProposalIntentId: &proposalRes.ProposalIntentId,
		PurchaseIntentId: &purchaseIntentId,
		SubmissionId:     &submissionId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting proposal lifecycle", zap.Error(err))
		return nil
	}
	// Convert the response to a structured type for safer handling
	proposalData := &dto.ProposalData{
		ProposalIntentID: res.GetProposalIntentId(),
		Name:             res.GetName(),
		Phone:            res.GetPhone(),
		CompanyName:      res.GetCompanyName(),
		ProductName:      res.GetProductName(),
		VariantName:      res.GetVariantName(),
		SumInsured:       res.GetSumInsured(),
		TotalPremium:     res.GetTotalAmount(),
		TotalGST:         res.GetTotalGst(),
		TotalAmount:      res.GetTotalPremium(),
		SubmissionID:     res.GetSubmissionId(),
		PurchaseIntentID: res.GetPurchaseIntentId(),
		ProposalStatus:   res.GetProposalStatus().String(),
		InsurerLogo:      res.GetLogoUrl(),
		RejectionReason:  res.GetRejectionReason(),
		RiderData:        riderData,
		Tenure:           res.GetTenure(),
	}

	// Process the info data structure with all the steps
	if info := res.GetInfo(); info != nil {
		lifecycleInfo := &dto.LifecycleInfo{
			CustomerConsent:  proposalRes.GetCustomerConsent(),
			RejectionReason:  proposalRes.GetRejectionReason(),
			ProposalIntentID: proposalRes.GetProposalIntentId(),
			PurchaseIntentID: purchaseIntentId,
			InsurerID:        res.GetInsurerId(),
			ProductID:        res.GetProductId(),
			ProposalID:       res.GetProposalId(),
		}

		// Process application step if present
		if app := info.GetApplication(); app != nil {
			appStep := &dto.ApplicationStep{}
			if stepInfo := app.GetStepInfo(); stepInfo != nil {
				appStep.StepInfo = &dto.StepInfo{
					Status:    stepInfo.GetStatus().String(),
					CreatedAt: stepInfo.GetCreatedAt(),
				}
				if nextStep := stepInfo.GetNextStep(); nextStep != "" {
					appStep.StepInfo.NextStep = nextStep
				}
			}
			if meta := app.GetMetadata(); meta != nil {
				appStep.Metadata.LatestProposalID = meta.GetLatestProposalId()
			}
			lifecycleInfo.Application = appStep
		}

		// Process KYC step if present
		if kyc := info.GetKyc(); kyc != nil {
			kycStep := &dto.KycStep{}
			if stepInfo := kyc.GetStepInfo(); stepInfo != nil {
				kycStep.StepInfo = &dto.StepInfo{
					Status:    stepInfo.GetStatus().String(),
					CreatedAt: stepInfo.GetCreatedAt(),
				}
				if nextStep := stepInfo.GetNextStep(); nextStep != "" {
					kycStep.StepInfo.NextStep = nextStep
				}
			}
			if meta := kyc.GetMetadata(); meta != nil {
				kycStep.Metadata.KycID = meta.GetKycId()
				kycStep.Metadata.KycType = meta.GetKycType()
				kycStep.Metadata.KycNumber = meta.GetKycNumber()
				kycStep.Metadata.KycDate = meta.GetKycDate()
				if url := meta.GetRedirectUrl(); url != "" {
					kycStep.Metadata.RedirectURL = url
				}
				if supportedDocumentTypes := meta.GetSupportedDocumentTypes(); supportedDocumentTypes != nil {
					kycStep.Metadata.SupportedDocumentTypes = supportedDocumentTypes
				}
				kycStep.Metadata.ManualVerification = meta.GetManualKycVerification()
			}
			lifecycleInfo.Kyc = kycStep
		}

		// Process proposal step if present
		if prop := info.GetProposal(); prop != nil {
			propStep := &dto.ProposalStep{}
			if stepInfo := prop.GetStepInfo(); stepInfo != nil {
				propStep.StepInfo = &dto.StepInfo{
					Status:    stepInfo.GetStatus().String(),
					CreatedAt: stepInfo.GetCreatedAt(),
				}
				if nextStep := stepInfo.GetNextStep(); nextStep != "" {
					propStep.StepInfo.NextStep = nextStep
				}
			}
			lifecycleInfo.Proposal = propStep
		}

		// Process payment step if present
		if payment := info.GetPayment(); payment != nil {
			paymentStep := &dto.PaymentStep{}
			if stepInfo := payment.GetStepInfo(); stepInfo != nil {
				paymentStep.StepInfo = &dto.StepInfo{
					Status:    stepInfo.GetStatus().String(),
					CreatedAt: stepInfo.GetCreatedAt(),
				}
				if nextStep := stepInfo.GetNextStep(); nextStep != "" {
					paymentStep.StepInfo.NextStep = nextStep
				}
			}
			if meta := payment.GetMetadata(); meta != nil {
				paymentStep.Metadata.PaymentID = meta.GetPaymentId()
				paymentStep.Metadata.PaymentType = meta.GetPaymentType()
				if url := meta.GetRedirectUrl(); url != "" {
					paymentStep.Metadata.RedirectURL = url
				}

				if additionalInfo := meta.GetAdditionalInfo(); additionalInfo != nil {
					paymentStep.Metadata.AdditionalInfo = additionalInfo
				}
			}
			lifecycleInfo.Payment = paymentStep
		}

		// Process policy step if present
		if policy := info.GetPolicy(); policy != nil {
			policyStep := &dto.PolicyStep{}
			if stepInfo := policy.GetStepInfo(); stepInfo != nil {
				policyStep.StepInfo = &dto.StepInfo{
					Status:    stepInfo.GetStatus().String(),
					CreatedAt: stepInfo.GetCreatedAt(),
				}
				if nextStep := stepInfo.GetNextStep(); nextStep != "" {
					policyStep.StepInfo.NextStep = nextStep
				}
			}
			if meta := policy.GetMetadata(); meta != nil {
				policyStep.Metadata.PolicyID = meta.GetPolicyId()
				policyStep.Metadata.PolicyNumber = meta.GetPolicyNumber()
				policyStep.Metadata.PolicyStartDate = meta.GetPolicyStartDate()
				policyStep.Metadata.PolicyEndDate = meta.GetPolicyEndDate()
				policyStep.Metadata.PolicyStatus = meta.GetPolicyStatus()
				if url := meta.GetUrl(); url != "" {
					policyStep.Metadata.URL = url
				} else {
					policyDocument, err := policyClient.GetPolicyDocument(grpcCtx.(context.Context), &policies.GetPolicyDocumentRequest{
						PolicyId: meta.GetPolicyId(),
					})
					if err != nil {
						ctx.Logger.Error("Error getting policy document", zap.Error(err))
					}
					policyStep.Metadata.URL = policyDocument.GetDocumentUrl()
				}
			}
			lifecycleInfo.Policy = policyStep
		}

		proposalData.Info = lifecycleInfo
	}

	return proposalData
}

func (h *Handler) GetPurchaseIntentData(ctx *handler.HandlerContext, decodedToken map[string]interface{}) *recommendation.GetPurchaseIntentResponse {

	conn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil
	}

	client := recommendation.NewRecommendationClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil
	}

	res, err := client.GetPurchaseIntent(grpcCtx.(context.Context), &recommendation.GetPurchaseIntentRequest{
		PurchaseIntentId: decodedToken["purchase_intent_id"].(string),
	})
	if err != nil {
		ctx.Logger.Error("Error getting purchase intent", zap.Error(err))
		return nil
	}

	return res
}

func (h *Handler) GetFormConfigData(ctx *handler.HandlerContext, SubmissionId string, decodedToken map[string]interface{}) *forms.FetchFormResponseResponse {

	conn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil
	}

	client := forms.NewFormsServiceClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil
	}

	formResponse, err := client.FetchFormResponse(grpcCtx.(context.Context), &forms.FetchFormResponseRequest{
		SubmissionId:      SubmissionId,
		IncludeFormSchema: true,
	})
	if err != nil {
		ctx.Logger.Error("Error getting form config", zap.Error(err))
		return nil
	}

	return formResponse
}

func (h *Handler) GetConsentDocumentURL(ctx *handler.HandlerContext, decodedToken map[string]interface{}, productId string) string {

	conn, err := h.GrpcClients.Get(catalog_metadata.MetadataService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting catalog client", zap.Error(err))
		return ""
	}

	client := catalog_metadata.NewMetadataServiceClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return ""
	}

	metadataRes, err := client.GetMetadata(grpcCtx.(context.Context), &catalog_metadata.GetMetadataRequest{
		Keys:      []string{"common"},
		ProductId: &productId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting consent document url", zap.Error(err))
		return ""
	}
	if len(metadataRes.Metadata) > 0 {
		if commonMetadata := metadataRes.Metadata[0].Value.Fields["consent-document"]; commonMetadata != nil {
			return commonMetadata.GetStringValue()
		}
	}
	return ""
}
