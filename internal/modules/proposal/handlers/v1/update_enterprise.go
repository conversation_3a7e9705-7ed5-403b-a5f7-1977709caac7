package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
)

func (h *Handler) UpdateEnterprise(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting policies client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	policiesClient := policies.NewPoliciesClient(conn)

	resp, err := policiesClient.UpdateEnterprise(context.Background(), &policies.UpdateEnterpriseRequest{
		PolicyId:     ctx.GinCtx.GetString("policy_id"),
		EnterpriseId: ctx.GinCtx.GetString("enterprise_id"),
	})
	if err != nil {
		ctx.Logger.Error("Error updating enterprise", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	//now call the proposal service to update the proposal

	proposalClient := proposal.NewProposalClient(conn)

	proposalResp, err := proposalClient.UpdateEnterprise(context.Background(), &proposal.UpdateEnterpriseRequest{
		PartnerId:        ctx.GinCtx.GetString("partner_id"),
		ProposalIntentId: resp.ProposalIntentId,
		EnterpriseId:     ctx.GinCtx.GetString("enterprise_id"),
	})

	if err != nil {
		ctx.Logger.Error("Error updating proposal", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: proposalResp,
	}, nil
}
