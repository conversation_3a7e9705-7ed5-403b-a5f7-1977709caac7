package v1

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/proposal/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"github.com/oneassure-tech/oa-protos/go/oa-rapidshort/v0/rapidshort"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// GetPublicTokenHandler is an adapter that conforms to CustomHandler type
func (h *Handler) GetPublicTokenHandler(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	submissionId := ctx.GetParam("submission_id")
	purchaseIntentId := ctx.GetParam("purchase_intent_id")
	if submissionId == "" || purchaseIntentId == "" {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Type:   "invalid_request",
				Title:  "Invalid Request",
				Detail: "Missing proposal ID",
			},
		}
	}
	return h.GetPublicToken(ctx, &dto.GetPublicTokenRequest{
		SubmissionId:     submissionId,
		PurchaseIntentId: purchaseIntentId,
	})
}

func (h *Handler) GetPublicToken(ctx *handler.HandlerContext, request *dto.GetPublicTokenRequest) (*response.SuccessResponse, *response.ErrorResponse) {
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}
	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	// Connect to authentication service
	authConn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting authentication client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	recommendationConn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	recommendationClient := recommendation.NewRecommendationClient(recommendationConn)

	// Get the enterprise id from the metadata
	rapidshortConn, err := h.GrpcClients.Get(rapidshort.RapidShortService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting rapidshort client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Create authentication client
	authClient := auth.NewAuthClient(authConn)

	partnerId := md.Get("sub")
	if len(partnerId) == 0 {
		partnerId = md.Get("partner_id")
	}

	// Create token request with appropriate client credentials
	customClaims := map[string]string{
		"purchase_intent_id": request.PurchaseIntentId,
		"submission_id":      request.SubmissionId,
		"enterprise_id":      md.Get("enterprise_id")[0],
		"sub":                partnerId[0],
	}

	recommendationResponse, err := recommendationClient.GetPurchaseIntent(grpcCtx.(context.Context), &recommendation.GetPurchaseIntentRequest{
		PurchaseIntentId: request.PurchaseIntentId,
	})

	if err != nil {
		ctx.Logger.Error("Error getting recommendation", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	tenure := recommendationResponse.GetTenure()

	expiry := time.Now().Add(time.Duration(tenure) * time.Hour * 24 * 365)

	// Call authentication service to create JWT token
	tokenResponse, err := authClient.GenerateJWTToken(grpcCtx.(context.Context), &auth.GenerateJWTTokenRequest{
		Audience:     "gateway_public",
		CustomClaims: customClaims,
		ExpiresAt:    timestamppb.New(expiry),
	})
	if err != nil {
		ctx.Logger.Error("Error creating JWT token", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	rapidshortClient := rapidshort.NewRapidShortServiceClient(rapidshortConn)

	// protocol := "https"
	// if strings.Contains(ctx.GinCtx.Request.Header.Get("Origin"), "localhost") {
	// 	protocol = "http"
	// }

	fmt.Println(ctx.GinCtx.Request.Header.Get("Origin"))

	res, err := rapidshortClient.CreateShortUrl(ctx.GinCtx, &rapidshort.CreateShortUrlRequest{
		LongUrl: fmt.Sprintf(
			"%s/customer/health/summary/%s",
			// protocol,
			ctx.GinCtx.Request.Header.Get("Origin"),
			tokenResponse.GetAccessToken(),
		),
		RedirectionCode: "302",
	})
	if err != nil {
		ctx.Logger.Error("Error getting short url", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetPublicTokenResponse{
			Token:    tokenResponse.GetAccessToken(),
			ShortUrl: fmt.Sprintf("%s/%s", "https://oasr.in", res.GetShortCode()),
		},
	}, nil
}
