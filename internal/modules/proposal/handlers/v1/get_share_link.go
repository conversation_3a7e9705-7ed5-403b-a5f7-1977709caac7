package v1

import (
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/proposal/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

func (h *Handler) GetShareLink(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.GetShareLinkRequest

	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Call the Auth Service To Get the link for sharing

	// After The Link is generated, Shorten The link using RapidShort in order to get a short link

	// Append The Short Link To The Response

	return nil, nil
}
