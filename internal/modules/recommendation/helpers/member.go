package helpers

import (
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/recommendation/dto"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
)

// buildMemberDetailsMap builds a map of member details, only including members that are present in the request
func BuildMemberDetailsMap(members map[dto.MemberEnum][]dto.InsuredMember) map[string]*recommendation.MemberList {
	memberDetails := make(map[string]*recommendation.MemberList)

	// Define relationship types and whether they can have multiple members
	relationshipConfigs := map[dto.MemberEnum]bool{
		dto.Self:     false,
		dto.Spouse:   false,
		dto.Father:   false,
		dto.Mother:   false,
		dto.Daughter: true,
		dto.Son:      true,
	}

	// Only add members that are present in the request
	for relationship, isMultiple := range relationshipConfigs {
		if memberList, ok := members[relationship]; ok && len(memberList) > 0 {
			memberDetails[string(relationship)] = createMemberList(members, relationship, isMultiple)
		}
	}

	return memberDetails
}

// createMemberList converts dto members to recommendation.InsuredMember objects
// The multipleMembers parameter determines whether to handle a single member or multiple members
func createMemberList(members map[dto.MemberEnum][]dto.InsuredMember, relationship dto.MemberEnum, multipleMembers bool) *recommendation.MemberList {
	if memberList, ok := members[relationship]; ok && len(memberList) > 0 {
		if !multipleMembers {
			// For relationships with a single member (self, spouse, father, mother)
			return &recommendation.MemberList{
				Members: []*recommendation.InsuredMember{
					{
						Age: int32(memberList[0].Age),
					},
				},
			}
		} else {
			// For relationships with potentially multiple members (son, daughter)
			result := make([]*recommendation.InsuredMember, len(memberList))
			for i, member := range memberList {
				result[i] = &recommendation.InsuredMember{
					Age: int32(member.Age),
				}
			}
			return &recommendation.MemberList{
				Members: result,
			}
		}
	}
	// Return empty member list if no members exist for the relationship
	return &recommendation.MemberList{
		Members: []*recommendation.InsuredMember{},
	}
}

func GetPlanType(planType string) recommendation.PlanType {
	if strings.ToLower(planType) == "base" {
		return recommendation.PlanType_BASE
	}
	return recommendation.PlanType_SUPER_TOP_UP
}
