package helpers

import (
	"encoding/json"

	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/recommendation/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/utility"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
)

// ConvertProtoToDTO converts the proto GetRecommendationResponse to DTO GetRecommendationResponse
func ConvertProtoToDTO(proto *recommendation.GetRecommendationResponse) *dto.GetRecommendationResponse {
	// Create the response DTO
	resp := &dto.GetRecommendationResponse{
		ID: proto.GetId(),
		// LeadID:              proto.GetLeadId(),
		PartnerID:    proto.GetPartnerId(),
		EnterpriseID: proto.GetEnterpriseId(),
		// VisitID:             proto.GetVisitId(),
		SumInsured:          proto.GetSumInsured(),
		PinCode:             proto.GetPincode(),
		Deductible:          utility.RoundFloat64(float64(proto.GetDeductible()), 2),
		Copay:               proto.GetCopay(),
		Status:              proto.GetStatus(),
		TotalVariants:       int(proto.GetTotalVariants()),
		CompletedVariants:   int(proto.GetCompletedVariants()),
		PreExistingDiseases: proto.GetPreExistingDiseases(),
		CustomerName:        proto.GetCustomerName(),
		CustomerPhone:       proto.GetCustomerPhone(),
		CustomerEmail:       proto.GetCustomerEmail(),
		PlanType:            proto.GetPlanType().String(),
		CustomerGender:      proto.GetProposorGender(),
	}

	// Convert Recommendations
	protoRecommendations := proto.GetRecommendations()
	if protoRecommendations != nil {
		resp.Recommendations = convertRecommendations(protoRecommendations)
	}

	// Convert Filters
	if protoFilters := proto.GetFilters(); protoFilters != nil {
		resp.Filters = convertFilters(protoFilters)
	}

	if protoNewRecommendations := proto.GetNewRecommendations(); protoNewRecommendations != nil {
		resp.NewRecommendations = convertNewRecommendations(protoNewRecommendations)
	}

	// Convert MemberDetails
	// if protoMemberDetails := proto.GetMemberDetails(); protoMemberDetails != nil {
	// 	resp.MemberDetails = convertMemberDetails(protoMemberDetails)
	// }

	return resp
}

func convertNewRecommendations(protoNewRecommendations []*recommendation.NewRecommendations) []dto.NewRecommendation {
	dtoNewRecommendations := make([]dto.NewRecommendation, 0, len(protoNewRecommendations))

	for _, protoNewRecommendation := range protoNewRecommendations {
		dtoNewRecommendations = append(dtoNewRecommendations, convertNewRecommendation(protoNewRecommendation))
	}

	return dtoNewRecommendations
}

func convertNewRecommendation(protoNewRecommendation *recommendation.NewRecommendations) dto.NewRecommendation {
	dtoNewRecommendation := dto.NewRecommendation{
		CompanyID:                   protoNewRecommendation.GetCompanyId(),
		CompanyName:                 protoNewRecommendation.GetCompanyName(),
		CompanyLogo:                 protoNewRecommendation.GetCompanyLogo(),
		CompanyDescription:          protoNewRecommendation.GetCompanyDescription(),
		CompanyNetworkHospitalCount: int(protoNewRecommendation.GetCompanyNetworkHospitalCount()),
		CompanyClaimSettlementRatio: int(protoNewRecommendation.GetCompanyClaimSettlementRatio()),
		ProductID:                   protoNewRecommendation.GetProductId(),
		ProductName:                 protoNewRecommendation.GetProductName(),
		VariantID:                   protoNewRecommendation.GetVariantId(),
		VariantName:                 protoNewRecommendation.GetVariantName(),
		VariantDescription:          protoNewRecommendation.GetVariantDescription(),
		Recommended:                 protoNewRecommendation.GetRecommended(),
		Online:                      protoNewRecommendation.GetOnline(),
		ParentVariantID:             protoNewRecommendation.GetParentVariantId(),
	}
	if protoTenureMaps := protoNewRecommendation.GetVariantTenureMap(); protoTenureMaps != nil {
		for _, protoTenureMap := range protoTenureMaps {
			dtoTenureMap := dto.TenureMap{
				Tenure:     int(protoTenureMap.GetTenure()),
				Premium:    utility.RoundFloat64(float64(protoTenureMap.GetPremium()), 2),
				Deductible: float64(protoTenureMap.GetDeductible()),
			}
			dtoNewRecommendation.TenureMap = append(dtoNewRecommendation.TenureMap, dtoTenureMap)
		}
	}

	return dtoNewRecommendation
}

// convertRecommendations converts proto Recommendations to DTO Recommendations
func convertRecommendations(protoRecommendations *recommendation.Recommendations) dto.Recommendations {
	dtoRecommendations := dto.Recommendations{
		Companies: make([]dto.Company, 0),
	}

	if protoCompanies := protoRecommendations.GetCompanies(); protoCompanies != nil {
		for _, protoCompany := range protoCompanies {
			dtoCompany := dto.Company{
				ID:                   protoCompany.GetId(),
				Name:                 protoCompany.GetName(),
				Logo:                 protoCompany.GetLogo(),
				Description:          protoCompany.GetDescription(),
				NetworkHospitalCount: int(protoCompany.GetNetworkHospitalCount()),
				ClaimSettlementRatio: int(protoCompany.GetClaimSettlementRatio()),
				Products:             make([]dto.Product, 0),
			}

			if protoProducts := protoCompany.GetProducts(); protoProducts != nil {
				for _, protoProduct := range protoProducts {
					dtoProduct := dto.Product{
						ID:       protoProduct.GetId(),
						Name:     protoProduct.GetName(),
						Variants: make([]dto.Variant, 0),
					}

					if protoVariants := protoProduct.GetVariants(); protoVariants != nil {
						for _, protoVariant := range protoVariants {
							dtoVariant := dto.Variant{
								ID:              protoVariant.GetId(),
								Name:            protoVariant.GetName(),
								Description:     protoVariant.GetDescription(),
								TenureMap:       make([]dto.TenureMap, 0),
								Recommended:     protoVariant.GetRecommended(),
								ParentVariantID: protoVariant.GetParentVariantId(),
							}

							if protoTenureMaps := protoVariant.GetTenureMap(); protoTenureMaps != nil {
								for _, protoTenureMap := range protoTenureMaps {
									dtoTenureMap := dto.TenureMap{
										Tenure:  int(protoTenureMap.GetTenure()),
										Premium: utility.RoundFloat64(float64(protoTenureMap.GetPremium()), 2),
									}
									dtoVariant.TenureMap = append(dtoVariant.TenureMap, dtoTenureMap)
								}
							}

							dtoProduct.Variants = append(dtoProduct.Variants, dtoVariant)
						}
					}

					dtoCompany.Products = append(dtoCompany.Products, dtoProduct)
				}
			}

			dtoRecommendations.Companies = append(dtoRecommendations.Companies, dtoCompany)
		}
	}

	return dtoRecommendations
}

// convertFilters converts proto Filters to DTO Filters
func convertFilters(protoFilters []*recommendation.Filter) []dto.Filter {
	dtoFilters := make([]dto.Filter, 0, len(protoFilters))

	for _, protoFilter := range protoFilters {
		dtoFilter := dto.Filter{
			Tag:    protoFilter.GetTag(),
			Name:   protoFilter.GetName(),
			Values: json.RawMessage{}, // Default empty JSON
		}

		// Convert the proto struct to JSON if it exists
		if values := protoFilter.GetValues(); values != nil {
			if data, err := json.Marshal(values); err == nil {
				dtoFilter.Values = json.RawMessage(data)
			}
		}

		dtoFilters = append(dtoFilters, dtoFilter)
	}

	return dtoFilters
}

func ConvertMemberDetails(memberDetails map[string]*recommendation.MemberList) map[dto.MemberEnum][]dto.InsuredMember {
	result := make(map[dto.MemberEnum][]dto.InsuredMember)

	// Initialize all member types with empty arrays
	memberTypes := []string{"self", "spouse", "father", "mother", "daughter", "son"}
	for _, memberType := range memberTypes {
		result[dto.MemberEnum(memberType)] = []dto.InsuredMember{}
	}

	// Fill in data from memberDetails where available
	for key, value := range memberDetails {
		members := make([]dto.InsuredMember, len(value.GetMembers()))
		for i, member := range value.GetMembers() {
			members[i] = dto.InsuredMember{
				Age: int(member.GetAge()),
			}
		}
		result[dto.MemberEnum(key)] = members
	}
	return result
}
