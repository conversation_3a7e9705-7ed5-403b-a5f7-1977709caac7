package v1

import (
	"context"

	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/recommendation/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/recommendation/helpers"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
	oa_errors "github.com/oneassure-tech/oa-utilities/go/oa-errors"
	"go.uber.org/zap"
)

func (h *Handler) GenerateRecommendation(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.GenerateRecommendationRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	AuthConn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	var partnerID string
	var enterpriseID string

	if req.EnterpriseID != "" && req.PartnerID != "" && !(req.EnterpriseID == ctx.GinCtx.GetString("enterprise_id") && req.PartnerID == ctx.GinCtx.GetString("partner_id")) {
		authClient := auth.NewAuthClient(AuthConn)

		authorizeResponse, err := authClient.Authorize(context.Background(), &auth.AuthorizeRequest{
			Sub:                 ctx.GinCtx.GetString("sub"),
			TokenId:             ctx.GinCtx.GetString("jti"),
			RequiredPermissions: []string{"recommendation.policy.attribution"},
		})

		if err != nil {
			ctx.Logger.Error("Error authorizing user", zap.Error(err))
			return nil, response.NewInternalServerError()
		}

		if !authorizeResponse.IsAuthorized {
			ctx.Logger.Error("User does not have the required permissions", zap.String("token_id", ctx.GinCtx.GetString("jti")), zap.String("Reason", *authorizeResponse.AuthorizationDeniedReason))
			return nil, &response.ErrorResponse{
				Status: &response.Status{
					HttpStatus: http.StatusUnauthorized,
				},
				Problem: &response.Problem{
					Type:   oa_errors.ErrInvalidInput.GetCode(),
					Title:  oa_errors.ErrInvalidInput.GetMessage(),
					Detail: "User does not have the required permissions",
				},
			}
		}

		partnerID = req.PartnerID
		enterpriseID = req.EnterpriseID

	}

	conn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Get the pre-configured gRPC context from middleware
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	client := recommendation.NewRecommendationClient(conn)

	var planType recommendation.PlanType
	if req.PlanType == "" {
		planType = recommendation.PlanType_BASE
	} else {
		planType = helpers.GetPlanType(req.PlanType)
	}

	createRecommendationRequest := &recommendation.CreateRecommendationRequest{
		MemberDetails:       helpers.BuildMemberDetailsMap(req.Members),
		ProposorGender:      req.Customer.Gender,
		Pincode:             req.Customer.PinCode,
		PreExistingDiseases: req.SelectedPEDs,
		CustomerName:        req.Customer.Name,
		CustomerPhone:       req.Customer.PhoneNumber,
		CustomerEmail:       req.Customer.Email,
		Deductible:          req.Deductible,
		PlanType:            planType,
		PartnerId:           &partnerID,
		EnterpriseId:        &enterpriseID,
	}

	if req.SumInsured != 0 {
		createRecommendationRequest.SumInsured = req.SumInsured
	} else {
		createRecommendationRequest.SumInsured = 1000000
	}

	submission, err := client.CreateRecommendation(grpcCtx.(context.Context), createRecommendationRequest)

	if err != nil {
		ctx.Logger.Error("Error submitting form response", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	resp := &dto.GenerateRecommendationResponse{
		QuoteID: submission.GetQuoteId(),
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func (h *Handler) GetRecommendation(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	quoteID := ctx.GetParam("quote_id")

	conn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	// Get the pre-configured gRPC context from middleware
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	client := recommendation.NewRecommendationClient(conn)

	getRecommendationRequest := &recommendation.GetRecommendationRequest{
		QuoteId: quoteID,
	}

	getRecommendationResponse, err := client.GetRecommendation(grpcCtx.(context.Context), getRecommendationRequest)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	// Convert proto response to DTO
	resp := helpers.ConvertProtoToDTO(getRecommendationResponse)
	resp.QuoteID = quoteID
	resp.MemberDetails = helpers.ConvertMemberDetails(getRecommendationResponse.GetMemberDetails())

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
