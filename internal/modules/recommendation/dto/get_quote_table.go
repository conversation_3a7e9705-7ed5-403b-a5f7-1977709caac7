package dto

type GetQuoteTableRequest struct {
	Limit        int    `form:"limit"`
	StartDate    string `form:"start_date"`
	EndDate      string `form:"end_date"`
	LastQuoteId  string `form:"last_quote_id"`
	FirstQuoteId string `form:"first_quote_id"`
}

// QuoteData represents individual quote data for table display
type QuoteData struct {
	ID                   string `json:"id"`
	LeadID               string `json:"lead_id"`
	LeadName             string `json:"lead_name"`
	PhoneNumber          string `json:"phone_number"`
	Email                string `json:"email"`
	Pincode              string `json:"pincode"`
	DateOfRecommendation string `json:"date_of_recommendation"`
	PincodeID            string `json:"pincode_id"`
	SumInsured           string `json:"sum_insured"`
	Vertical             string `json:"vertical"`
	PlanType             string `json:"plan_type"`
	PartnerName          string `json:"partner_name"`
	EnterpriseName       string `json:"enterprise_name"`
}

// GetQuoteTableResponse represents the response for getting quote table data
type GetQuoteTableResponse struct {
	Success     bool        `json:"success"`
	EndCursor   *string     `json:"end_cursor,omitempty"`
	Quotes      []QuoteData `json:"quotes"`
	StartCursor *string     `json:"start_cursor,omitempty"`
	HasPrevious bool        `json:"has_previous"`
	HasNext     bool        `json:"has_next"`
	TotalCount  int32       `json:"total_count"`
}
