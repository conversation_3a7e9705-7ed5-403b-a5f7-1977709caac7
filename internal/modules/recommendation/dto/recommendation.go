package dto

import (
	"encoding/json"
)

type MemberEnum string

const (
	Self     MemberEnum = "self"
	Spouse   MemberEnum = "spouse"
	Father   MemberEnum = "father"
	Mother   MemberEnum = "mother"
	Daughter MemberEnum = "daughter"
	Son      MemberEnum = "son"
)

type PlanTypeEnum string

type GenerateRecommendationRequest struct {
	LeadID       string                         `json:"lead_id" validate:"required"`
	UserID       string                         `json:"user_id" validate:"required"`
	Customer     CustomerDetails                `json:"customer" validate:"required"`
	Members      map[MemberEnum][]InsuredMember `json:"members" validate:"required"`
	HasPED       bool                           `json:"hasPED" validate:"required"`
	SelectedPEDs []string                       `json:"selectedPEDs"`
	PlanType     string                         `json:"plan_type"`
	SumInsured   float64                        `json:"sum_insured"`
	Deductible   *float64                       `json:"deductible"`
	PartnerID    string                         `json:"partner_id"`
	EnterpriseID string                         `json:"enterprise_id"`
}

type CustomerDetails struct {
	Name        string `json:"name" validate:"required"`
	Gender      string `json:"gender" validate:"required"`
	PhoneNumber string `json:"phoneNumber" validate:"required"`
	PinCode     string `json:"pinCode" validate:"required"`
	Email       string `json:"email" validate:"required"`
}

type InsuredMember struct {
	Age int `json:"age" validate:"required"`
}

type GenerateRecommendationResponse struct {
	QuoteID string `json:"quote_id"`
}

// GET

type GetRecommendationRequest struct {
	QuoteID string `json:"quote_id" validate:"required"`
}

// Recommendations
type Recommendations struct {
	Companies []Company `json:"companies"`
}

type Company struct {
	ID                   string    `json:"id"`
	Name                 string    `json:"name"`
	Logo                 string    `json:"logo"`
	Description          string    `json:"description"`
	NetworkHospitalCount int       `json:"network_hospital_count"`
	ClaimSettlementRatio int       `json:"claim_settlement_ratio"`
	Products             []Product `json:"products"`
}

type Product struct {
	ID       string    `json:"id"`
	Name     string    `json:"name"`
	Variants []Variant `json:"variants"`
}

type Variant struct {
	ID              string      `json:"id"`
	Name            string      `json:"name"`
	Description     string      `json:"description"`
	TenureMap       []TenureMap `json:"tenure_map"`
	Recommended     bool        `json:"recommended"`
	ParentVariantID string      `json:"parent_variant_id"`
}

type TenureMap struct {
	Tenure     int     `json:"tenure"`
	Premium    float64 `json:"premium"`
	Deductible float64 `json:"deductible"`
}

type Filter struct {
	Tag    string          `json:"tag"`
	Name   string          `json:"name"`
	Values json.RawMessage `json:"values"`
}

// MemberDetails
type MemberList struct {
	Members []InsuredMember `json:"members"`
}

// GetRecommendationResponse
type GetRecommendationResponse struct {
	ID                  string                         `json:"id"`
	LeadID              string                         `json:"lead_id"`
	PartnerID           string                         `json:"partner_id"`
	EnterpriseID        string                         `json:"enterprise_id"`
	VisitID             string                         `json:"visit_id"`
	SumInsured          float64                        `json:"sum_insured"`
	PinCode             string                         `json:"pincode"`
	Deductible          float64                        `json:"deductible"`
	Copay               float64                        `json:"copay"`
	Status              string                         `json:"status"`
	TotalVariants       int                            `json:"total_variants"`
	CompletedVariants   int                            `json:"completed_variants"`
	Recommendations     Recommendations                `json:"recommendations"`
	Filters             []Filter                       `json:"filters"`
	MemberDetails       map[MemberEnum][]InsuredMember `json:"member_details"`
	PlanType            string                         `json:"plan_type"`
	PreExistingDiseases []string                       `json:"pre_existing_diseases"`
	CustomerName        string                         `json:"customer_name"`
	CustomerPhone       string                         `json:"customer_phone"`
	CustomerGender      string                         `json:"customer_gender"`
	CustomerEmail       string                         `json:"customer_email"`
	QuoteID             string                         `json:"quote_id"`

	NewRecommendations []NewRecommendation `json:"new_recommendations"`
}

type NewRecommendation struct {
	CompanyID                   string      `json:"company_id"`
	CompanyName                 string      `json:"company_name"`
	CompanyLogo                 string      `json:"company_logo"`
	CompanyDescription          string      `json:"company_description"`
	CompanyNetworkHospitalCount int         `json:"company_network_hospital_count"`
	CompanyClaimSettlementRatio int         `json:"company_claim_settlement_ratio"`
	ProductID                   string      `json:"product_id"`
	ProductName                 string      `json:"product_name"`
	VariantID                   string      `json:"variant_id"`
	VariantName                 string      `json:"variant_name"`
	VariantDescription          string      `json:"variant_description"`
	TenureMap                   []TenureMap `json:"tenure_map"`
	Recommended                 bool        `json:"recommended"`
	Online                      bool        `json:"online"`
	ParentVariantID             string      `json:"parent_variant_id"`
}
