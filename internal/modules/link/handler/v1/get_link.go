package v1

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/link/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	healthCatalog "github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/health"
	"go.uber.org/zap"
)

func (h *Handler) GetLink(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	// Extract the request body
	var request dto.GetLinkRequest
	ctx.ExtractHttpRequest(&request)

	if request.Type == dto.LinkTypeRenewal {
		var err error
		conn, err := h.GrpcClients.Get(healthCatalog.HealthService_ServiceDesc.ServiceName)
		if err != nil {
			ctx.Logger.Error("Error getting health client", zap.Error(err))
			return nil, response.NewInternalServerError()
		}
		client := healthCatalog.NewHealthServiceClient(conn)

		healthClient := client.(healthCatalog.HealthServiceClient)
		link, err := healthClient.GetOfflineRenewalLink(h.GrpcCtx, &healthCatalog.GetOfflineRenewalLinkRequest{
			InsurerId: request.Metadata["insurer_id"].(string),
		})
		if err != nil {
			ctx.Logger.Error("Error getting offline renewal link", zap.Error(err))
			return nil, response.NewInternalServerError()
		}
		return &response.SuccessResponse{
			Status: http.StatusOK,
			Payload: &dto.GetLinkResponse{
				Link: link.GetLink(),
			},
		}, nil
	}

	return nil, nil
}
