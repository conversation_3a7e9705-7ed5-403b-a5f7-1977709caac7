package link

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/link/handler/v1"
)

type LinkApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &LinkApp{}
}

// SetAppName returns the name of the application
func (app *LinkApp) SetAppName() string {
	return "link"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *LinkApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
		GrpcCtx:     ctx,
	}

	apiRouter := appContext.Router[constant.HTTP_API]

	apiRouter.RegisterRoute(ctx, appName, "POST", "",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetLink,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{}),
				},
			}),
		},
	)

}
