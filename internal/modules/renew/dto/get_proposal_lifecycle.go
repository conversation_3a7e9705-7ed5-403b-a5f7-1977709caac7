package dto

type GetProposalLifecycleRequest struct {
	ProposalIntentID string `uri:"proposal_intent_id" validate:"required"`
}

type GetProposalLifecycleResponse struct {
	RenewalIntentId string  `json:"renewal_intent_id"`
	Name            string  `json:"name"`
	Phone           string  `json:"phone"`
	InsurerId       string  `json:"insurer_id"`
	ProductId       string  `json:"product_id"`
	VariantId       string  `json:"variant_id"`
	SumInsured      string  `json:"sum_insured"`
	TotalAmount     *string `json:"total_amount"`
	TotalGst        *string `json:"total_gst"`
	TotalPremium    *string `json:"total_premium"`
	ReferenceId     string  `json:"reference_id"`
	SubmissionId    *string `json:"submission_id"`
	ProposalStatus  string  `json:"proposal_status"`
	RejectionReason *string `json:"rejection_reason"`
	ProposalId      string  `json:"proposal_id"`
	PartnerId       string  `json:"partner_id"`
	Tenure          string  `json:"tenure"`
}
