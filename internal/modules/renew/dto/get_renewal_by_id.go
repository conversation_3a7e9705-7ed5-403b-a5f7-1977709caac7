package dto

type GetRenewalByIdRequest struct {
	RenewalID string `json:"renewal_id"`
}

type RiderResponse struct {
	RiderId      string  `json:"rider_id"`
	RiderName    string  `json:"rider_name"`
	RiderPremium float64 `json:"rider_premium"`
}

type MemberDetails struct {
	MemberId     string `json:"member_id"`
	Name         string `json:"member_name"`
	DOB          string `json:"dob"`
	Relationship string `json:"relationship"`
}

type Renewal struct {
	RenewalId            string          `json:"renewal_id"`
	ProductId            string          `json:"product_id"`
	VariantId            string          `json:"variant_id"`
	SubmissionId         string          `json:"submission_id"`
	SumInsured           string          `json:"sum_insured"`
	BasePremium          string          `json:"base_premium"`
	Deductible           string          `json:"deductible"`
	Tenure               string          `json:"tenure"`
	RiderResponses       []RiderResponse `json:"rider_responses"`
	TotalGstAmount       string          `json:"total_gst_amount"`
	TotalPremium         string          `json:"total_premium"`
	MemberDetails        []MemberDetails `json:"member_details"`
	Name                 string          `json:"name"`
	Email                string          `json:"email"`
	CountryCode          *string         `json:"country_code"`
	Phone                string          `json:"phone"`
	EnterpriseId         string          `json:"enterprise_id"`
	PartnerId            string          `json:"partner_id"`
	InsurerId            string          `json:"insurer_id"`
	InsurerName          string          `json:"insurer_name"`
	ProductName          string          `json:"product_name"`
	VariantName          string          `json:"variant_name"`
	PreviousPolicyNumber string          `json:"previous_policy_number"`
	PreviousPolicyId     string          `json:"previous_policy_id"`
	PrevPolicyExpiryDate string          `json:"prev_policy_expiry_date"`
	IntegrationType      string          `json:"integration_type"`
	Pincode              string          `json:"pincode"`
	InsurerLogo          string          `json:"insurer_logo"`
}

type GetRenewalByIdResponse struct {
	Renewal Renewal `json:"renewal"`
}
