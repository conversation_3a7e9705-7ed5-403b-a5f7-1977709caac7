package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	catalog "github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/health"
	renewal "github.com/oneassure-tech/oa-protos/go/oa-renewal/v0/renewal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (h *Handler) GetProposalLifecycle(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(renewal.Renewal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := renewal.NewRenewalClient(conn)

	connCatalog, err := h.GrpcClients.Get(catalog.HealthService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	clientCatalog := catalog.NewHealthServiceClient(connCatalog)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	renewalIntentId := ctx.GinCtx.Param("renewal_intent_id")
	res, err := client.GetProposalLifeCycle(grpcCtx.(context.Context), &renewal.GetProposalLifeCycleRequest{
		RenewalIntentId: renewalIntentId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting proposal lifecycle", zap.Error(err))
		if status.Code(err) == codes.NotFound {
			return nil, &response.ErrorResponse{
				Status: &response.Status{HttpStatus: http.StatusNotFound},
				Problem: &response.Problem{
					Title:  "Proposal Not Found",
					Detail: "The specified proposal was not found",
				},
			}
		}
		return nil, response.NewInternalServerError()
	}

	variant, err := clientCatalog.GetVariantById(grpcCtx.(context.Context), &catalog.GetVariantByIdRequest{
		VariantId: res.GetVariantId(),
	})
	if err != nil {
		ctx.Logger.Error("Error getting variant", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Convert the response to a map structure for easier manipulation
	responseMap := map[string]interface{}{
		"renewal_intent_id": res.GetRenewalIntentId(),
		"name":              res.GetName(),
		"phone":             res.GetPhone(),
		"company_name":      variant.GetInsurerName(),
		"product_name":      variant.GetProductName(),
		"variant_name":      variant.GetVariantName(),
		"sum_insured":       res.GetSumInsured(),
		"total_premium":     res.GetTotalPremium(),
		"total_gst":         res.GetTotalGst(),
		"total_amount":      res.GetTotalAmount(),
		"submission_id":     res.GetSubmissionId(),
		"proposal_status":   res.GetProposalStatus().String(), // Convert enum to string
		"insurer_logo":      variant.GetInsurerLogo(),
		"rejection_reason":  res.GetRejectionReason(),
		"insurer_id":        res.GetInsurerId(),
		"product_id":        res.GetProductId(),
		"proposal_id":       res.GetProposalId(),
		"partner_id":        res.GetPartnerId(),
	}

	// Process the info data structure with all the steps
	// if info := res.GetInfo(); info != nil {
	// 	infoMap := make(map[string]interface{})

	// 	// Process application step if present
	// 	if app := info.GetApplication(); app != nil {
	// 		appMap := make(map[string]interface{})
	// 		if stepInfo := app.GetStepInfo(); stepInfo != nil {
	// 			appMap["step_info"] = map[string]interface{}{
	// 				"status":     stepInfo.GetStatus().String(), // Convert enum to string
	// 				"created_at": stepInfo.GetCreatedAt(),
	// 			}
	// 			if nextStep := stepInfo.GetNextStep(); nextStep != "" {
	// 				appMap["step_info"].(map[string]interface{})["next_step"] = nextStep
	// 			}
	// 		}
	// 		if meta := app.GetMetadata(); meta != nil {
	// 			appMap["metadata"] = map[string]interface{}{
	// 				"latest_proposal_id": meta.GetLatestProposalId(),
	// 			}
	// 		}
	// 		infoMap["application"] = appMap
	// 	}

	// 	// Process KYC step if present
	// 	if kyc := info.GetKyc(); kyc != nil {
	// 		kycMap := make(map[string]interface{})
	// 		if stepInfo := kyc.GetStepInfo(); stepInfo != nil {
	// 			kycMap["step_info"] = map[string]interface{}{
	// 				"status":     stepInfo.GetStatus().String(), // Convert enum to string
	// 				"created_at": stepInfo.GetCreatedAt(),
	// 			}
	// 			if nextStep := stepInfo.GetNextStep(); nextStep != "" {
	// 				kycMap["step_info"].(map[string]interface{})["next_step"] = nextStep
	// 			}
	// 		}
	// 		if meta := kyc.GetMetadata(); meta != nil {
	// 			kycMap["metadata"] = map[string]interface{}{
	// 				"kyc_id":     meta.GetKycId(),
	// 				"kyc_type":   meta.GetKycType(),
	// 				"kyc_number": meta.GetKycNumber(),
	// 				"kyc_date":   meta.GetKycDate(),
	// 			}
	// 			if url := meta.GetRedirectUrl(); url != "" {
	// 				kycMap["metadata"].(map[string]interface{})["redirect_url"] = url
	// 			}
	// 			if supportedDocumentTypes := meta.GetSupportedDocumentTypes(); supportedDocumentTypes != nil {
	// 				kycMap["metadata"].(map[string]interface{})["supported_document_types"] = supportedDocumentTypes
	// 			}
	// 			if manualVerification := meta.GetManualKycVerification(); manualVerification {
	// 				kycMap["metadata"].(map[string]interface{})["manual_verification"] = manualVerification
	// 			}
	// 		}
	// 		infoMap["kyc"] = kycMap
	// 	}

	// 	// Process proposal step if present
	// 	if prop := info.GetProposal(); prop != nil {
	// 		propMap := make(map[string]interface{})
	// 		if stepInfo := prop.GetStepInfo(); stepInfo != nil {
	// 			propMap["step_info"] = map[string]interface{}{
	// 				"status":     stepInfo.GetStatus().String(), // Convert enum to string
	// 				"created_at": stepInfo.GetCreatedAt(),
	// 			}
	// 			if nextStep := stepInfo.GetNextStep(); nextStep != "" {
	// 				propMap["step_info"].(map[string]interface{})["next_step"] = nextStep
	// 			}
	// 		}
	// 		if meta := prop.GetMetadata(); meta != nil {
	// 			propMap["metadata"] = map[string]interface{}{
	// 				"insurer_proposal_id": meta.GetInsurerProposalId(),
	// 			}
	// 		}
	// 		infoMap["proposal"] = propMap
	// 	}

	// 	// Process payment step if present
	// 	if payment := info.GetPayment(); payment != nil {
	// 		paymentMap := make(map[string]interface{})
	// 		if stepInfo := payment.GetStepInfo(); stepInfo != nil {
	// 			paymentMap["step_info"] = map[string]interface{}{
	// 				"status":     stepInfo.GetStatus().String(), // Convert enum to string
	// 				"created_at": stepInfo.GetCreatedAt(),
	// 			}
	// 			if nextStep := stepInfo.GetNextStep(); nextStep != "" {
	// 				paymentMap["step_info"].(map[string]interface{})["next_step"] = nextStep
	// 			}
	// 		}
	// 		if meta := payment.GetMetadata(); meta != nil {
	// 			paymentMap["metadata"] = map[string]interface{}{
	// 				"payment_id":   meta.GetPaymentId(),
	// 				"payment_type": meta.GetPaymentType(),
	// 			}
	// 			if url := meta.GetRedirectUrl(); url != "" {
	// 				paymentMap["metadata"].(map[string]interface{})["redirect_url"] = url
	// 			}

	// 			if additionalInfo := meta.GetAdditionalInfo(); additionalInfo != nil {
	// 				paymentMap["metadata"].(map[string]interface{})["additional_info"] = additionalInfo
	// 			}
	// 		}
	// 		infoMap["payment"] = paymentMap
	// 	}

	// 	// Process policy step if present
	// 	if policy := info.GetPolicy(); policy != nil {
	// 		policyMap := make(map[string]interface{})
	// 		if stepInfo := policy.GetStepInfo(); stepInfo != nil {
	// 			policyMap["step_info"] = map[string]interface{}{
	// 				"status": stepInfo.GetStatus().String(), // Convert enum to string
	// 				// "created_at": stepInfo.GetCreatedAt(),
	// 			}
	// 			if nextStep := stepInfo.GetNextStep(); nextStep != "" {
	// 				policyMap["step_info"].(map[string]interface{})["next_step"] = nextStep
	// 			}
	// 		}
	// 		if meta := policy.GetMetadata(); meta != nil {
	// 			policyMap["metadata"] = map[string]interface{}{
	// 				"policy_id": meta.GetPolicyId(),
	// 			}
	// 			if url := meta.GetUrl(); url != "" {
	// 				policyMap["metadata"].(map[string]interface{})["url"] = url
	// 			}
	// 		}
	// 		infoMap["policy"] = policyMap
	// 	}

	// 	responseMap["info"] = infoMap
	// }

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: responseMap,
	}, nil
}
