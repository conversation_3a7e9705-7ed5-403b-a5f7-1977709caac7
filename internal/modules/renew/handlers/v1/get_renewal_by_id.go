package v1

import (
	"context"
	"net/http"
	"net/url"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/renew/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	catalog "github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/health"
	renewal "github.com/oneassure-tech/oa-protos/go/oa-renewal/v0/renewal"
	"go.uber.org/zap"
)

// getStringValue safely extracts string value from a pointer, returning empty string if nil
func getStringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func (h *Handler) GetRenewalById(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(renewal.Renewal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting renewal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := renewal.NewRenewalClient(conn)

	connCatalog, err := h.GrpcClients.Get(catalog.HealthService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	clientCatalog := catalog.NewHealthServiceClient(connCatalog)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	renewalID := ctx.GinCtx.Param("id")

	// Decode url encoded renewal id
	decodedRenewalID, err := url.QueryUnescape(renewalID)
	if err != nil {
		ctx.Logger.Error("Error decoding renewal ID", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcReq := &renewal.GetRenewalByIdRequest{
		RenewalIntentId: decodedRenewalID,
	}

	res, err := client.GetRenewalById(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetRenewalById gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	variant, err := clientCatalog.GetVariantById(grpcCtx.(context.Context), &catalog.GetVariantByIdRequest{
		VariantId: res.GetVariantId(),
	})
	if err != nil {
		ctx.Logger.Error("Error getting variant", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	var renewalResponse dto.Renewal

	riderResponses := make([]dto.RiderResponse, 0)
	for key, value := range res.RiderResponses.AsMap() {
		riderResponses = append(riderResponses, dto.RiderResponse{
			RiderId:      key,
			RiderName:    value.(string),
			RiderPremium: value.(float64),
		})
	}

	memberDetails := make([]dto.MemberDetails, 0)
	for _, memberDetail := range res.MemberDetails {
		memberDetails = append(memberDetails, dto.MemberDetails{
			MemberId:     memberDetail.MemberId,
			Name:         memberDetail.Name,
			DOB:          memberDetail.Dob,
			Relationship: memberDetail.Relationship,
		})
	}

	renewalResponse = dto.Renewal{
		RenewalId:            res.RenewalId,
		ProductId:            res.ProductId,
		VariantId:            res.VariantId,
		SubmissionId:         getStringValue(res.SubmissionId),
		SumInsured:           res.SumInsured,
		BasePremium:          getStringValue(res.BasePremium),
		Deductible:           getStringValue(res.Deductible),
		Tenure:               res.Tenure,
		RiderResponses:       riderResponses,
		TotalGstAmount:       getStringValue(res.TotalGstAmount),
		TotalPremium:         res.TotalPremium,
		MemberDetails:        memberDetails,
		Name:                 res.Name,
		Email:                res.Email,
		CountryCode:          res.CountryCode,
		Phone:                res.Phone,
		EnterpriseId:         res.EnterpriseId,
		PartnerId:            res.PartnerId,
		InsurerId:            res.InsurerId,
		InsurerName:          res.InsurerName,
		ProductName:          res.ProductName,
		VariantName:          res.VariantName,
		PreviousPolicyNumber: res.PreviousPolicyNumber,
		PreviousPolicyId:     res.PreviousPolicyId,
		PrevPolicyExpiryDate: res.PrevPolicyExpiryDate,
		IntegrationType:      res.IntegrationType,
		Pincode:              res.Pincode,
		InsurerLogo:          variant.GetInsurerLogo(),
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetRenewalByIdResponse{
			Renewal: renewalResponse,
		},
	}, nil
}
