package v1

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/summary/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"github.com/oneassure-tech/oa-protos/go/oa-rapidshort/v0/rapidshort"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (h *Handler) GetRenewalPublicToken(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	RenewalIntentId := c.GetParam("renewal_intent_id")
	baseUrl := c.GetQuery("base_url")
	conn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting authentication client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	// Get the enterprise id from the metadata
	rapidshortConn, err := h.GrpcClients.Get(rapidshort.RapidShortService_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting rapidshort client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Create authentication client
	authClient := auth.NewAuthClient(conn)
	rapidshortClient := rapidshort.NewRapidShortServiceClient(rapidshortConn)

	// grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	// if !ok {
	// 	c.Logger.Error("gRPC metadata context not found in gin context")
	// 	return nil, response.NewInternalServerError()
	// }
	// md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	// if !ok {
	// 	c.Logger.Error("Failed to extract metadata from context")
	// 	return nil, response.NewInternalServerError()
	// }
	enterpriseId := c.GinCtx.GetString("enterprise_id")
	partnerId := c.GinCtx.GetString("partner_id")

	customClaims := map[string]string{
		"renewal_intent_id": RenewalIntentId,
		"sub":               partnerId,
	}
	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", enterpriseId)

	// Call authentication service to create JWT token
	tokenResponse, err := authClient.GenerateJWTToken(grpcCtx, &auth.GenerateJWTTokenRequest{
		Audience:     "gateway_public",
		CustomClaims: customClaims,
		ExpiresAt:    timestamppb.New(time.Now().Add(time.Hour * 24 * 30)),
	})
	if err != nil {
		c.Logger.Error("Error creating JWT token", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Construct the long URL properly
	var longUrl string
	if baseUrl != "" {
		// If baseUrl is provided, use it directly
		longUrl = fmt.Sprintf("%s/customer/health/renew/summary/%s?token=%s", baseUrl, RenewalIntentId, tokenResponse.GetAccessToken())
	} else {
		// Fallback to using Origin header
		longUrl = fmt.Sprintf("%s/customer/health/renew/summary/%s?token=%s", c.GinCtx.Request.Header.Get("Origin"), RenewalIntentId, tokenResponse.GetAccessToken())
	}

	res, err := rapidshortClient.CreateShortUrl(c.GinCtx, &rapidshort.CreateShortUrlRequest{
		LongUrl:         longUrl,
		RedirectionCode: "302",
	})
	if err != nil {
		c.Logger.Error("Error creating short url", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetPurchaseIntentSummaryTokenResponse{
			Token:    tokenResponse.GetAccessToken(),
			ShortUrl: fmt.Sprintf("%s/%s", "https://oasr.in", res.GetShortCode()),
		},
	}, nil
}
