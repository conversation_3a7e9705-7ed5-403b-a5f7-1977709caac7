package v1

import (
	"context"
	"net/http"
	"net/url"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/renew/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	renewal "github.com/oneassure-tech/oa-protos/go/oa-renewal/v0/renewal"
	"go.uber.org/zap"
)

func (h *Handler) PostInitiateRenewal(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(renewal.Renewal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting renewal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := renewal.NewRenewalClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	policyID := ctx.GinCtx.Param("id")

	// Decode url encoded renewal id
	decodedPolicyID, err := url.QueryUnescape(policyID)
	if err != nil {
		ctx.Logger.Error("Error decoding renewal ID", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcReq := &renewal.StartRenewalWorkflowRequest{
		PolicyId: decodedPolicyID,
	}

	res, err := client.StartRenewalWorkflow(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetRenewalById gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	workflowID := res.WorkflowId

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.InitiateRenewalResponse{
			WorkflowID: workflowID,
		},
	}, nil
}
