package v1

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_summary/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_summary/helpers"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	termRecommendationPb "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"
)

func (h *Handler) GeneratePurchaseIntent(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.GeneratePurchaseIntentRequest
	err := c.ExtractHttpRequest(&req)
	if err != nil {
		c.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(termRecommendationPb.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting recommendation client", zap.Error(err))

		return nil, response.NewInternalServerError()
	}

	grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		c.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	client := termRecommendationPb.NewTermRecommendationClient(conn)

	createPurchaseIntentRequest := &termRecommendationPb.CreatePurchaseIntentRequest{
		VariantId: req.VariantID,
		// SubmissionId:     req.SubmissionID,
		QuoteId:          req.QuoteID,
		RecommendationId: req.RecommendationID,
		ProductId:        req.ProductID,
	}

	createPurchaseIntentResponse, err := client.CreatePurchaseIntent(grpcCtx.(context.Context), createPurchaseIntentRequest)
	if err != nil {
		c.Logger.Error("Error creating purchase intent", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	resp := dto.GeneratePurchaseIntentResponse{
		PurchaseIntentID: createPurchaseIntentResponse.GetPurchaseIntentId(),
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func (h *Handler) GetPurchaseIntent(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	PurchaseIntentID := c.GetQuery("purchase_intent_id")

	if PurchaseIntentID == "" {
		PurchaseIntentID = c.GinCtx.GetString("purchase_intent_id")
	}

	conn, err := h.GrpcClients.Get(termRecommendationPb.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		c.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	client := termRecommendationPb.NewTermRecommendationClient(conn)

	getPurchaseIntentRequest := &termRecommendationPb.GetPurchaseIntentRequest{
		PurchaseIntentId: PurchaseIntentID,
	}

	getPurchaseIntentResponse, err := client.GetPurchaseIntent(grpcCtx.(context.Context), getPurchaseIntentRequest)
	if err != nil {
		c.Logger.Error("Error getting purchase intent", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	resp := helpers.ConvertProtoToDTO(getPurchaseIntentResponse)

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func (h *Handler) UpdatePurchaseIntent(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.UpdatePurchaseIntentRequest
	err := c.ExtractHttpRequest(&req)
	if err != nil {
		c.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	PurchaseIntentID := req.PurchaseIntentID

	conn, err := h.GrpcClients.Get(termRecommendationPb.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		c.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	if PurchaseIntentID == "" {
		PurchaseIntentID = c.GinCtx.GetString("purchase_intent_id")
	}
	client := termRecommendationPb.NewTermRecommendationClient(conn)

	// Convert string update type to UpdateType enum
	updateType, err := helpers.StringToTermUpdateType(req.UpdateType)
	if err != nil {
		c.Logger.Error("Invalid update type", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Convert JSON value to protobuf struct value
	var structValue *structpb.Value
	if len(req.Values) > 0 {
		var jsonValue interface{}
		if err := json.Unmarshal(req.Values, &jsonValue); err != nil {
			c.Logger.Error("Error unmarshaling values", zap.Error(err))
			return nil, response.NewInternalServerError()
		}

		structValue, err = structpb.NewValue(jsonValue)
		if err != nil {
			c.Logger.Error("Error converting to protobuf value", zap.Error(err))
			return nil, response.NewInternalServerError()
		}
	}

	updatePurchaseIntentRequest := &termRecommendationPb.UpdatePurchaseIntentRequest{
		PurchaseIntentId: PurchaseIntentID,
		UpdateType:       updateType,
		Values:           structValue,
	}

	updatePurchaseIntentResponse, err := client.UpdatePurchaseIntent(grpcCtx.(context.Context), updatePurchaseIntentRequest)
	if err != nil {
		c.Logger.Error("Error updating purchase intent", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	resp := dto.UpdatePurchaseIntentResponse{}

	if updatePurchaseIntentResponse.GetSuccess() {
		resp.Success = true
		resp.Message = "Premium updated successfully"
	} else {
		message := updatePurchaseIntentResponse.GetMessage()
		resp.Message = message
		resp.Success = false
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func (h *Handler) GetPolicyIllustration(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	PurchaseIntentID := c.GetQuery("purchase_intent_id")

	conn, err := h.GrpcClients.Get(termRecommendationPb.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		c.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	if PurchaseIntentID == "" {
		PurchaseIntentID = c.GinCtx.GetString("purchase_intent_id")
	}
	client := termRecommendationPb.NewTermRecommendationClient(conn)

	getIllustrationRequest := &termRecommendationPb.GetIllustrationRequest{
		PurchaseIntentId: PurchaseIntentID,
	}

	getIllustrationResponse, err := client.GetIllustration(grpcCtx.(context.Context), getIllustrationRequest)
	if err != nil {
		c.Logger.Error("Error starting illustration workflow", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	resp := dto.GetPolicyIllustrationResponse{
		Success: getIllustrationResponse.GetSuccess(),
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
