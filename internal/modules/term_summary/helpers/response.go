package helpers

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_summary/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/utility"
	termRecommendationPb "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
)

func ConvertProtoToDTO(proto *termRecommendationPb.GetPurchaseIntentResponse) *dto.GetPurchaseIntentResponse {
	// Create the response DTO

	resp := dto.GetPurchaseIntentResponse{
		PurchaseIntentID: proto.GetPurchaseIntentId(),
		VariantID:        proto.GetVariantId(),
		SubmissionID:     proto.GetSubmissionId(),
		ProductID:        proto.GetProductId(),
		BasePremium:      utility.RoundFloat64(float64(proto.GetBasePremium()), 2),
		Riders:           []dto.Rider{},
		PlanType:         proto.GetPlanType().String(),
		CustomerName:     proto.GetCustomerName(),
		CustomerPhone:    proto.GetCustomerPhone(),
		CustomerEmail:    proto.GetCustomerEmail(),
		Features:         []dto.Feature{},
		Exclusions:       []dto.Exclusion{},
		TotalGST:         utility.RoundFloat64(float64(proto.GetTotalGst()), 2),
		VariantStaticDetails: dto.VariantStaticDetails{
			VariantName:          proto.GetVariantStaticDetails().GetVariantName(),
			InsurerName:          proto.GetVariantStaticDetails().GetInsurerName(),
			InsurerLogo:          proto.GetVariantStaticDetails().GetInsurerLogo(),
			ClaimSettlementRatio: proto.GetVariantStaticDetails().GetClaimSettlementRatio(),
			PolicyWordingUrl:     proto.GetVariantStaticDetails().GetPolicyWordingUrl(),
			BrochureUrl:          proto.GetVariantStaticDetails().GetBrochureUrl(),
		},
		TotalPremium:           utility.RoundFloat64(float64(proto.GetTotalPremium()), 2),
		SumInsured:             proto.GetSumInsured(),
		PolicyTerm:             proto.GetPolicyTerm(),
		IllustrationURL:        proto.GetIllustrationUrl(),
		IllustrationDownloaded: proto.GetIllustrationDownloaded(),
	}

	for _, rider := range proto.GetRiders() {
		responses, err := json.Marshal(rider.GetResponses().AsMap())
		if err != nil {
			fmt.Println("Error marshalling rider responses", err)
			continue
		}
		formConfigBytes, err := json.Marshal(rider.FormConfig)
		if err != nil {
			fmt.Println("Error marshalling rider static form config", err)
			continue
		}
		formConfig := json.RawMessage(formConfigBytes)
		resp.Riders = append(resp.Riders, dto.Rider{
			ID:           rider.GetId(),
			Name:         rider.GetName(),
			Description:  rider.GetDescription(),
			IsSelected:   rider.GetIsSelected(),
			IsMandatory:  rider.GetIsMandatory(),
			Responses:    json.RawMessage(responses),
			RiderPremium: utility.RoundFloat64(float64(rider.GetRiderPremium()), 2),
			Sequence:     rider.GetSequence(),
			FormConfig:   formConfig,
		})
	}

	for _, feature := range proto.GetFeatures() {
		resp.Features = append(resp.Features, dto.Feature{
			Name:           feature.GetName(),
			Description:    feature.GetDescription(),
			ListedFeatures: feature.GetListedFeatures(),
		})
	}

	for _, exclusion := range proto.GetExclusions() {
		resp.Exclusions = append(resp.Exclusions, dto.Exclusion{
			Exclusion: exclusion.GetExclusion(),
		})
	}

	for _, filter := range proto.GetFilters() {
		options := filter.GetOptions().AsInterface()
		optionsBytes, err := json.Marshal(options)
		if err != nil {
			fmt.Println("Error marshalling filter options", err)
			continue
		}
		resp.Filters = append(resp.Filters, dto.Filter{
			Tag:      filter.GetTag(),
			Name:     filter.GetName(),
			Selected: filter.GetSelected(),
			Options:  json.RawMessage(optionsBytes),
		})
	}

	for _, payoutQuestion := range proto.GetPayoutQuestions() {
		halfValue := payoutQuestion.GetHalfValue()
		perMonthValue := payoutQuestion.GetPerMonthValue()
		selectedYear := payoutQuestion.GetSelectedYear()
		value := payoutQuestion.GetValue()
		isSelected := payoutQuestion.GetIsSelected()
		filters := []dto.PayoutQuestionFilter{}
		for _, filter := range payoutQuestion.GetFilters() {
			filters = append(filters, dto.PayoutQuestionFilter{
				Key:   strconv.Itoa(int(filter.GetKey())),
				Value: filter.GetValue(),
			})
		}
		resp.PayoutQuestion = append(resp.PayoutQuestion, dto.PayoutQuestion{
			PayoutOption:  payoutQuestion.GetName(),
			HalfValue:     &halfValue,
			PerMonthValue: &perMonthValue,
			SelectedYear:  &selectedYear,
			Value:         &value,
			IsSelected:    &isSelected,
			Filters:       filters,
		})
	}

	return &resp
}

func StringToTermUpdateType(updateType string) (termRecommendationPb.UpdateType, error) {
	switch updateType {
	case "RIDER":
		return termRecommendationPb.UpdateType_RIDER, nil
	case "SUM_INSURED":
		return termRecommendationPb.UpdateType_SUM_INSURED, nil
	case "PAYMENT_FREQUENCY":
		return termRecommendationPb.UpdateType_PAYMENT_FREQUENCY, nil
	case "POLICY_TERM":
		return termRecommendationPb.UpdateType_POLICY_TERM, nil
	case "PREMIUM_PAYING_TERM":
		return termRecommendationPb.UpdateType_PREMIUM_PAYING_TERM, nil
	case "ADDITIONAL_INFO":
		return termRecommendationPb.UpdateType_ADDITIONAL_INFO, nil
	case "PAYOUT_QUESTION":
		return termRecommendationPb.UpdateType_PAYOUT_QUESTION, nil
	case "FILTER":
		return termRecommendationPb.UpdateType_FILTER, nil
	case "BI_STATUS":
		return termRecommendationPb.UpdateType_BI_STATUS, nil
	default:
		return 0, fmt.Errorf("invalid update type: %s", updateType)
	}
}
