package termsummary

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_summary/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_summary/handlers/v1"
)

type TermSummaryApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &TermSummaryApp{}
}

// SetAppName returns the name of the application
func (app *TermSummaryApp) SetAppName() string {
	return "term_summary"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *TermSummaryApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	// Register HTTP routes
	apiRouter := appContext.Router[constant.HTTP_API]
	requireAuth := false

	apiRouter.RegisterRoute(ctx, appName, "POST", "/term",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.GeneratePurchaseIntent,
				ReqBodyStruct: &dto.GeneratePurchaseIntentRequest{},
				RequireAuth:   &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"term_summary.purchase-intent.create",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/term",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.GetPurchaseIntent,
				// ReqBodyStruct: &dto.GetPurchaseIntentRequest{},
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"term_summary.purchase-intent.get",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "PATCH", "/term",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.UpdatePurchaseIntent,
				ReqBodyStruct: &dto.UpdatePurchaseIntentRequest{},
				RequireAuth:   &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"term_summary.purchase-intent.update",
					}),
				},
			}),
		},
	)
	apiRouter.RegisterRoute(ctx, appName, "GET", "/term/illustration",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.GetPolicyIllustration,
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"term_summary.get-policy-illustration",
					}),
				},
			}),
		},
	)
}
