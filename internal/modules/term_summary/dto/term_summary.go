package dto

import (
	"encoding/json"
)

type GeneratePurchaseIntentRequest struct {
	VariantID        string `json:"variant_id" validate:"required"`
	SubmissionID     string `json:"submission_id"`
	QuoteID          string `json:"quote_id" validate:"required"`
	RecommendationID string `json:"recommendation_id"`
	ProductID        string `json:"product_id" validate:"required"`
}

type GeneratePurchaseIntentResponse struct {
	PurchaseIntentID string `json:"purchase_intent_id"`
}

type Rider struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Responses    json.RawMessage `json:"responses"`
	FormConfig   json.RawMessage `json:"form_config"`
	IsSelected   bool            `json:"is_selected"`
	IsMandatory  bool            `json:"is_mandatory"`
	RiderPremium float64         `json:"rider_premium"`
	Sequence     int32           `json:"sequence"`
}

type Feature struct {
	Name           string   `json:"name"`
	Description    string   `json:"description"`
	ListedFeatures []string `json:"listed_features"`
}

type Exclusion struct {
	Exclusion string `json:"exclusion"`
}

type GetPurchaseIntentResponse struct {
	PurchaseIntentID       string               `json:"purchase_intent_id"`
	VariantID              string               `json:"variant_id"`
	SumInsured             int64                `json:"sum_insured"`
	SubmissionID           string               `json:"submission_id"`
	Filters                []Filter             `json:"filters"`
	ProductID              string               `json:"product_id"`
	BasePremium            float64              `json:"base_premium"`
	Riders                 []Rider              `json:"riders"`
	PlanType               string               `json:"plan_type"`
	PreExistingDiseases    []string             `json:"pre_existing_diseases"`
	CustomerName           string               `json:"customer_name"`
	CustomerPhone          string               `json:"customer_phone"`
	CustomerEmail          string               `json:"customer_email"`
	Features               []Feature            `json:"features"`
	Exclusions             []Exclusion          `json:"exclusions"`
	Locked                 bool                 `json:"locked"`
	TotalGST               float64              `json:"total_gst"`
	LeadID                 string               `json:"lead_id"`
	InsurerID              string               `json:"insurer_id"`
	QuoteID                string               `json:"quote_id"`
	VariantStaticDetails   VariantStaticDetails `json:"variant_static_details"`
	TotalPremium           float64              `json:"total_premium"`
	PolicyTerm             int32                `json:"policy_term"`
	PayoutQuestion         []PayoutQuestion     `json:"payout_question"`
	IllustrationURL        string               `json:"illustration_url"`
	IllustrationDownloaded bool                 `json:"illustration_downloaded"`
}

type PayoutQuestion struct {
	PayoutOption  string                 `json:"payout_option"`
	HalfValue     *string                `json:"half_value"`
	PerMonthValue *string                `json:"per_month_value"`
	SelectedYear  *string                `json:"selected_year"`
	Value         *string                `json:"value"`
	IsSelected    *bool                  `json:"is_selected"`
	Filters       []PayoutQuestionFilter `json:"filters"`
}

type PayoutQuestionFilter struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Filter struct {
	Tag      string          `json:"tag"`
	Name     string          `json:"name"`
	Selected string          `json:"selected"`
	Options  json.RawMessage `json:"options"`
}

type VariantStaticDetails struct {
	VariantName          string `json:"variant_name"`
	InsurerName          string `json:"insurer_name"`
	InsurerLogo          string `json:"insurer_logo"`
	ClaimSettlementRatio int32  `json:"claim_settlement_ratio"`
	PolicyWordingUrl     string `json:"policy_wording_url"`
	BrochureUrl          string `json:"brochure_url"`
}

type UpdatePurchaseIntentRequest struct {
	PurchaseIntentID string          `json:"purchase_intent_id"`
	Values           json.RawMessage `json:"values"`
	UpdateType       string          `json:"update_type"`
}

type UpdatePurchaseIntentResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type GetPolicyIllustrationResponse struct {
	Success bool `json:"success"`
}
