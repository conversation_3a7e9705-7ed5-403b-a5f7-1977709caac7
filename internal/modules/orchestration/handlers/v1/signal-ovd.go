package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/orchestration/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
)

func (h *Handler) SignalOvd(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Extract the key and value from the request
	var req dto.SignalOvdRequest

	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	if req.ProposalIntentID == "" {
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := proposal.NewProposalClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	documents := make([]*proposal.Document, len(req.Documents))
	for i, doc := range req.Documents {
		documents[i] = &proposal.Document{
			DocumentKey:  doc.DocumentKey,
			DocumentType: doc.DocumentType,
		}
	}

	res, err := client.SignalOVD(grpcCtx.(context.Context), &proposal.SignalOVDRequest{
		ProposalIntentId: req.ProposalIntentID,
		Documents:        documents,
	})
	if err != nil {
		ctx.Logger.Error("Error signaling OVD", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
