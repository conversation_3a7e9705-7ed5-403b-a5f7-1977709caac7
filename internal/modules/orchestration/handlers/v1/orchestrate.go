package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/orchestration/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-forms/v0/forms"

	recommendation "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
	term_recommendation "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"

	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	term_proposal "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/term_proposal"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/structpb"
)

func (h *Handler) Orchestrate(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Extract request data
	var req dto.OrchestrateRequest

	req.PurchaseIntentId = ctx.GetQuery("purchase_intent_id")
	req.SubmissionID = ctx.GetQuery("submission_id")

	// Try to get context and metadata from token
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	// Try to get submission ID and purchase intent ID from token first
	submissionID := req.SubmissionID
	submissionIDs := md.Get("submission_id")
	if len(submissionIDs) > 0 && submissionID == "" {
		submissionID = submissionIDs[0]
	}

	purchaseIntentID := req.PurchaseIntentId
	purchaseIntentIDs := md.Get("purchase_intent_id")
	if len(purchaseIntentIDs) > 0 && purchaseIntentID == "" {
		purchaseIntentID = purchaseIntentIDs[0]
	}

	if submissionID == "" || purchaseIntentID == "" {
		ctx.Logger.Error("submission_id or purchase_intent_id is missing")
		return nil, response.NewInternalServerError()
	}

	// Update the request with values from token if needed
	req.SubmissionID = submissionID
	req.PurchaseIntentId = purchaseIntentID

	// Use the prepared context in all calls

	recommendationConn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	recommendationClient := recommendation.NewRecommendationClient(recommendationConn)

	purchaseIntentResponse, err := recommendationClient.GetPurchaseIntent(grpcCtx.(context.Context), &recommendation.GetPurchaseIntentRequest{
		PurchaseIntentId: purchaseIntentID,
	})
	if err != nil {
		ctx.Logger.Error("Error getting purchase intent", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	isOffline := false
	if purchaseIntentResponse.IsOffline {
		isOffline = true
	}

	grpcCtx = metadata.NewOutgoingContext(grpcCtx.(context.Context), metadata.Pairs("enterprise_id", purchaseIntentResponse.GetEnterpriseId(), "partner_id", purchaseIntentResponse.GetPartnerId()))

	if !isOffline {
		resp, errResp := h.getDynamicPremiumCheckWithContext(ctx, grpcCtx.(context.Context), &dto.DynamicPremiumCheckRequestParams{
			SubmissionId:     submissionID,
			PurchaseIntentId: purchaseIntentID,
		})
		if errResp != nil {
			return nil, errResp
		}

		if resp.OldPremium != resp.NewPremium {
			fmt.Println("Old premium is not equal to new premium")
			return &response.SuccessResponse{
				Status: http.StatusOK,
				Payload: map[string]interface{}{
					"old_premium":        resp.OldPremium,
					"new_premium":        resp.NewPremium,
					"is_premium_changed": true,
					"difference":         resp.NewPremium - resp.OldPremium,
				},
			}, nil
		}
	}

	errResp := h.sealFormResponsesWithContext(ctx, grpcCtx.(context.Context), &req)
	if errResp != nil {
		return nil, errResp
	}

	errResp = h.sealPurchaseIntentWithContext(ctx, grpcCtx.(context.Context), &req)
	if errResp != nil {
		return nil, errResp
	}

	res, errResp := h.createProposalWithContext(ctx, grpcCtx.(context.Context), &req)
	if errResp != nil {
		return nil, errResp
	}

	workflowRes, errResp := h.startWorkflowWithContext(ctx, grpcCtx.(context.Context), res)
	if errResp != nil {
		return nil, errResp
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: workflowRes,
	}, nil
}

// getDynamicPremiumCheckWithContext performs premium check with the provided context
func (h *Handler) getDynamicPremiumCheckWithContext(ctx *handler.HandlerContext, gCtx context.Context, req *dto.DynamicPremiumCheckRequestParams) (*dto.DynamicPremiumCheckResponse, *response.ErrorResponse) {
	recommendationConn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	formsConn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting forms client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	formsClient := forms.NewFormsServiceClient(formsConn)

	resp, err := formsClient.FetchFormResponse(gCtx, &forms.FetchFormResponseRequest{
		SubmissionId: req.SubmissionId,
	})

	if err != nil {
		ctx.Logger.Error("Error getting form response", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	recommendationClient := recommendation.NewRecommendationClient(recommendationConn)

	//construct a request dynamically for dynamic premium check
	dynamicPremiumCheckRequest := &recommendation.DynamicPremiumCheckRequest{
		PurchaseIntentId: req.PurchaseIntentId,
	}

	var pincode string

	for _, obj := range resp.ResponseData {
		if obj.SectionName == "proposer" {
			//unmarshal the response data into a map[string]interface{}
			var proposer map[string]interface{}
			jsonBytes, err := json.Marshal(obj.ResponseData)
			if err != nil {
				ctx.Logger.Error("Error marshalling proposer", zap.Error(err))
				return nil, response.NewInternalServerError()
			}
			err = json.Unmarshal(jsonBytes, &proposer)
			if err != nil {
				ctx.Logger.Error("Error unmarshalling proposer", zap.Error(err))
				return nil, response.NewInternalServerError()
			}
			pincode = proposer["pincode"].(string)
		} else if obj.SectionName == "insured" {
			//unmarshal the response data into a map[string]interface{}
			var insured map[string]interface{}
			jsonBytes, err := json.Marshal(obj.ResponseData)
			if err != nil {
				ctx.Logger.Error("Error marshalling insured", zap.Error(err))
				return nil, response.NewInternalServerError()
			}
			err = json.Unmarshal(jsonBytes, &insured)
			if err != nil {
				ctx.Logger.Error("Error unmarshalling insured", zap.Error(err))
				return nil, response.NewInternalServerError()
			}

			membersMap := insured
			for relation, member := range membersMap {
				memberMap := member.(map[string]interface{})
				memberQuestions := memberMap["member_questions"].(map[string]interface{})

				dob := memberQuestions["date-of-birth"].(string)

				dynamicPremiumCheckRequest.MemberDetails = append(dynamicPremiumCheckRequest.MemberDetails, &recommendation.MemberDetails{
					Relation: relation,
					Dob:      dob,
				})
			}
		}
	}

	dynamicPremiumCheckRequest.Pincode = pincode

	//convert the whole resp.response_data into a map[string]interface{}
	responseData := make(map[string]interface{})
	for _, obj := range resp.ResponseData {
		if obj.ResponseData != nil {
			responseData[obj.SectionName] = obj.ResponseData.AsMap()
		}
	}

	//now make a structpb for this responseData
	responseDataPb, err := structpb.NewStruct(responseData)
	if err != nil {
		ctx.Logger.Error("Error creating structpb", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	dynamicPremiumCheckRequest.FormResponses = responseDataPb

	dynamicPremiumCheckResponse, err := recommendationClient.DynamicPremiumCheck(gCtx, dynamicPremiumCheckRequest)
	if err != nil {
		ctx.Logger.Error("Error getting dynamic premium check", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &dto.DynamicPremiumCheckResponse{
		OldPremium: float64(dynamicPremiumCheckResponse.OldPremium),
		NewPremium: float64(dynamicPremiumCheckResponse.NewPremium),
	}, nil
}

// getDynamicPremiumCheckWithContext performs premium check with the provided context
func (h *Handler) getTermDynamicPremiumCheckWithContext(ctx *handler.HandlerContext, gCtx context.Context, req *dto.TermDynamicPremiumCheckRequestParams) (*dto.TermDynamicPremiumCheckResponse, *response.ErrorResponse) {
	recommendationConn, err := h.GrpcClients.Get(term_recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	formsConn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting forms client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	formsClient := forms.NewFormsServiceClient(formsConn)

	resp, err := formsClient.FetchFormResponse(gCtx, &forms.FetchFormResponseRequest{
		SubmissionId: req.SubmissionId,
	})

	if err != nil {
		ctx.Logger.Error("Error getting form response", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	termRecommendationClient := term_recommendation.NewTermRecommendationClient(recommendationConn)

	//construct a request dynamically for dynamic premium check
	termDynamicPremiumCheckRequest := &term_recommendation.TermDynamicPremiumCheckRequest{
		PurchaseIntentId: req.PurchaseIntentId,
	}

	var pincode string

	fmt.Println("resp.ResponseData", resp.ResponseData)

	for _, obj := range resp.ResponseData {
		if obj.SectionName == "insured" {
			//unmarshal the response data into a map[string]interface{}
			var insured map[string]interface{}
			jsonBytes, err := json.Marshal(obj.ResponseData)
			if err != nil {
				ctx.Logger.Error("Error marshalling insured", zap.Error(err))
				return nil, response.NewInternalServerError()
			}
			err = json.Unmarshal(jsonBytes, &insured)
			if err != nil {
				ctx.Logger.Error("Error unmarshalling insured", zap.Error(err))
				return nil, response.NewInternalServerError()
			}

			pincode = insured["permanent-pincode"].(string)
			dob := insured["date-of-birth"].(string)

			termDynamicPremiumCheckRequest.MemberDetails = append(termDynamicPremiumCheckRequest.MemberDetails, &term_recommendation.MemberDetails{
				Dob: dob,
			})
			break
		}
	}

	termDynamicPremiumCheckRequest.Pincode = pincode

	//convert the whole resp.response_data into a map[string]interface{}
	responseData := make(map[string]interface{})
	for _, obj := range resp.ResponseData {
		if obj.ResponseData != nil {
			responseData[obj.SectionName] = obj.ResponseData.AsMap()
		}
	}

	//now make a structpb for this responseData
	responseDataPb, err := structpb.NewStruct(responseData)
	if err != nil {
		ctx.Logger.Error("Error creating structpb", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	termDynamicPremiumCheckRequest.FormResponses = responseDataPb

	termDynamicPremiumCheckResponse, err := termRecommendationClient.TermDynamicPremiumCheck(gCtx, termDynamicPremiumCheckRequest)
	if err != nil {
		ctx.Logger.Error("Error getting dynamic premium check", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &dto.TermDynamicPremiumCheckResponse{
		OldPremium: float64(termDynamicPremiumCheckResponse.OldPremium),
		NewPremium: float64(termDynamicPremiumCheckResponse.NewPremium),
	}, nil
}

// sealFormResponsesWithContext seals form responses with the provided context
func (h *Handler) sealFormResponsesWithContext(ctx *handler.HandlerContext, gCtx context.Context, req *dto.OrchestrateRequest) *response.ErrorResponse {
	conn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return response.NewInternalServerError()
	}

	client := forms.NewFormsServiceClient(conn)

	_, err = client.SealSubmission(gCtx, &forms.SealSubmissionRequest{
		SubmissionId: req.SubmissionID,
	})
	if err != nil {
		ctx.Logger.Error("Error sealing submission", zap.Error(err))
		return response.NewInternalServerError()
	}

	return nil
}

// sealPurchaseIntentWithContext seals purchase intent with the provided context
func (h *Handler) sealPurchaseIntentWithContext(ctx *handler.HandlerContext, gCtx context.Context, req *dto.OrchestrateRequest) *response.ErrorResponse {
	conn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return response.NewInternalServerError()
	}

	client := recommendation.NewRecommendationClient(conn)

	resp, err := client.LockPurchaseIntent(gCtx, &recommendation.LockPurchaseIntentRequest{
		PurchaseIntentId: req.PurchaseIntentId,
	})
	if err != nil {
		ctx.Logger.Error("Error locking purchase intent", zap.Error(err))
		return response.NewInternalServerError()
	}
	// Check if the purchase intent is locked
	if !resp.Success {
		return response.NewInternalServerError()
	}
	return nil
}

// createProposalWithContext creates a proposal with the provided context
func (h *Handler) createProposalWithContext(ctx *handler.HandlerContext, gCtx context.Context, req *dto.OrchestrateRequest) (*dto.CreateProposalResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := proposal.NewProposalClient(conn)

	res, err := client.CreateProposal(gCtx, &proposal.CreateProposalRequest{
		SubmissionId:     req.SubmissionID,
		PurchaseIntentId: req.PurchaseIntentId,
	})
	if err != nil {
		ctx.Logger.Error("Error creating proposal", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &dto.CreateProposalResponse{
		ProposalID:       res.ProposalId,
		ProposalIntentId: res.ProposalIntentId,
	}, nil
}

// startWorkflowWithContext starts a workflow with the provided context
func (h *Handler) startWorkflowWithContext(ctx *handler.HandlerContext, gCtx context.Context, req *dto.CreateProposalResponse) (*dto.StartWorkflowResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := proposal.NewProposalClient(conn)

	res, err := client.StartWorkflow(gCtx, &proposal.StartWorkflowRequest{
		ProposalId:       req.ProposalID,
		ProposalIntentId: req.ProposalIntentId,
	})
	if err != nil {
		ctx.Logger.Error("Error starting workflow", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &dto.StartWorkflowResponse{
		WorkflowID: res.WorkflowId,
		RunID:      res.RunId,
		Status:     res.Status,
	}, nil
}

// DynamicPremiumCheck handles the dynamic premium check endpoint
func (h *Handler) DynamicPremiumCheck(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Extract params from the Gin context parameters
	params := &dto.DynamicPremiumCheckRequestParams{}

	// Get parameters from URL
	submissionIDParam := ctx.GetQuery("submission_id")
	purchaseIntentIDParam := ctx.GetQuery("purchase_intent_id")

	// Try to get context and metadata from token
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	// Try to get submission ID and purchase intent ID from token first
	submissionID := submissionIDParam
	submissionIDs := md.Get("submission_id")
	if len(submissionIDs) > 0 {
		submissionID = submissionIDs[0]
	}

	purchaseIntentID := purchaseIntentIDParam
	purchaseIntentIDs := md.Get("purchase_intent_id")
	if len(purchaseIntentIDs) > 0 {
		purchaseIntentID = purchaseIntentIDs[0]
	}

	if submissionID == "" || purchaseIntentID == "" {
		ctx.Logger.Error("submission_id or purchase_intent_id is missing")
		return nil, response.NewInternalServerError()
	}

	params.SubmissionId = submissionID
	params.PurchaseIntentId = purchaseIntentID

	recommendationConn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	recommendationClient := recommendation.NewRecommendationClient(recommendationConn)

	purchaseIntentResponse, err := recommendationClient.GetPurchaseIntent(grpcCtx.(context.Context), &recommendation.GetPurchaseIntentRequest{
		PurchaseIntentId: purchaseIntentID,
	})
	if err != nil {
		ctx.Logger.Error("Error getting purchase intent", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	if purchaseIntentResponse.IsOffline == true {
		return &response.SuccessResponse{
			Status: http.StatusOK,
			Payload: map[string]interface{}{
				"is_premium_changed": false,
			},
		}, nil
	}

	// Call the actual implementation
	resp, errResp := h.getDynamicPremiumCheckWithContext(ctx, grpcCtx.(context.Context), params)
	if errResp != nil {
		return nil, errResp
	}

	if resp.OldPremium != resp.NewPremium {
		fmt.Println("Old premium is not equal to new premium")
		return &response.SuccessResponse{
			Status: http.StatusOK,
			Payload: map[string]interface{}{
				"old_premium":        resp.OldPremium,
				"new_premium":        resp.NewPremium,
				"is_premium_changed": true,
				"difference":         resp.NewPremium - resp.OldPremium,
			},
		}, nil
	}
	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: map[string]interface{}{
			"is_premium_changed": false,
		},
	}, nil
}

func (h *Handler) TermDynamicPremiumCheck(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Extract params from the Gin context parameters
	params := &dto.TermDynamicPremiumCheckRequestParams{}

	// Get parameters from URL
	submissionIDParam := ctx.GetQuery("submission_id")
	purchaseIntentIDParam := ctx.GetQuery("purchase_intent_id")

	// Try to get context and metadata from token
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	// Try to get submission ID and purchase intent ID from token first
	submissionID := submissionIDParam
	submissionIDs := md.Get("submission_id")
	if len(submissionIDs) > 0 {
		submissionID = submissionIDs[0]
	}

	purchaseIntentID := purchaseIntentIDParam
	purchaseIntentIDs := md.Get("purchase_intent_id")
	if len(purchaseIntentIDs) > 0 {
		purchaseIntentID = purchaseIntentIDs[0]
	}

	if submissionID == "" || purchaseIntentID == "" {
		ctx.Logger.Error("submission_id or purchase_intent_id is missing")
		return nil, response.NewInternalServerError()
	}

	params.SubmissionId = submissionID
	params.PurchaseIntentId = purchaseIntentID

	recommendationConn, err := h.GrpcClients.Get(term_recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	termRecommendationClient := term_recommendation.NewTermRecommendationClient(recommendationConn)

	purchaseIntentResponse, err := termRecommendationClient.GetPurchaseIntent(grpcCtx.(context.Context), &term_recommendation.GetPurchaseIntentRequest{
		PurchaseIntentId: purchaseIntentID,
	})
	if err != nil {
		ctx.Logger.Error("Error getting purchase intent", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	if purchaseIntentResponse.IsOffline == true {
		return &response.SuccessResponse{
			Status: http.StatusOK,
			Payload: map[string]interface{}{
				"is_premium_changed": false,
			},
		}, nil
	}

	// Call the actual implementation
	resp, errResp := h.getTermDynamicPremiumCheckWithContext(ctx, grpcCtx.(context.Context), params)
	if errResp != nil {
		return nil, errResp
	}

	if resp.OldPremium != resp.NewPremium {
		fmt.Println("Old premium is not equal to new premium")
		return &response.SuccessResponse{
			Status: http.StatusOK,
			Payload: map[string]interface{}{
				"old_premium":        resp.OldPremium,
				"new_premium":        resp.NewPremium,
				"is_premium_changed": true,
				"difference":         resp.NewPremium - resp.OldPremium,
			},
		}, nil
	}
	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: map[string]interface{}{
			"is_premium_changed": false,
		},
	}, nil
}

func (h *Handler) TermOrchestrate(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Extract request data
	var req dto.OrchestrateRequest

	req.PurchaseIntentId = ctx.GetQuery("purchase_intent_id")
	req.SubmissionID = ctx.GetQuery("submission_id")

	// Try to get context and metadata from token
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	// Try to get submission ID and purchase intent ID from token first
	submissionID := req.SubmissionID
	submissionIDs := md.Get("submission_id")
	if len(submissionIDs) > 0 && submissionID == "" {
		submissionID = submissionIDs[0]
	}

	purchaseIntentID := req.PurchaseIntentId
	purchaseIntentIDs := md.Get("purchase_intent_id")
	if len(purchaseIntentIDs) > 0 && purchaseIntentID == "" {
		purchaseIntentID = purchaseIntentIDs[0]
	}

	if submissionID == "" || purchaseIntentID == "" {
		ctx.Logger.Error("submission_id or purchase_intent_id is missing")
		return nil, response.NewInternalServerError()
	}

	// Update the request with values from token if needed
	req.SubmissionID = submissionID
	req.PurchaseIntentId = purchaseIntentID

	// Use term recommendation client instead of regular recommendation client
	termRecommendationConn, err := h.GrpcClients.Get(term_recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting term recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	termRecommendationClient := term_recommendation.NewTermRecommendationClient(termRecommendationConn)

	purchaseIntentResponse, err := termRecommendationClient.GetPurchaseIntent(grpcCtx.(context.Context), &term_recommendation.GetPurchaseIntentRequest{
		PurchaseIntentId: purchaseIntentID,
	})
	if err != nil {
		ctx.Logger.Error("Error getting term purchase intent", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	grpcCtx = metadata.NewOutgoingContext(grpcCtx.(context.Context), metadata.Pairs("enterprise_id", purchaseIntentResponse.GetEnterpriseId(), "partner_id", purchaseIntentResponse.GetPartnerId()))

	// Skip dynamic premium check for term orchestration
	ctx.Logger.Info("Skipping dynamic premium check for term orchestration")

	// Skip form sealing for term orchestration - submission may not exist yet
	ctx.Logger.Info("Skipping form sealing for term orchestration")

	errResp := h.sealTermPurchaseIntentWithContext(ctx, grpcCtx.(context.Context), &req)
	if errResp != nil {
		return nil, errResp
	}

	res, errResp := h.createTermProposalWithContext(ctx, grpcCtx.(context.Context), &req)
	if errResp != nil {
		return nil, errResp
	}

	workflowRes, errResp := h.startTermWorkflowWithContext(ctx, grpcCtx.(context.Context), res)
	if errResp != nil {
		return nil, errResp
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: workflowRes,
	}, nil
}

// sealTermPurchaseIntentWithContext seals term purchase intent with the provided context
func (h *Handler) sealTermPurchaseIntentWithContext(ctx *handler.HandlerContext, gCtx context.Context, req *dto.OrchestrateRequest) *response.ErrorResponse {
	// For term insurance, we don't have a LockPurchaseIntent method in term_recommendation
	// We'll skip this step for now or implement it when the method is available
	ctx.Logger.Info("Term purchase intent sealing - skipping as method not available")
	return nil
}

// createTermProposalWithContext creates a term proposal with the provided context
func (h *Handler) createTermProposalWithContext(ctx *handler.HandlerContext, gCtx context.Context, req *dto.OrchestrateRequest) (*dto.CreateProposalResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(term_proposal.TermProposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting term proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := term_proposal.NewTermProposalClient(conn)

	res, err := client.CreateProposalForTerm(gCtx, &term_proposal.CreateProposalForTermRequest{
		SubmissionId:     req.SubmissionID,
		PurchaseIntentId: req.PurchaseIntentId,
	})
	if err != nil {
		ctx.Logger.Error("Error creating term proposal", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &dto.CreateProposalResponse{
		ProposalID:       res.ProposalId,
		ProposalIntentId: res.ProposalIntentId,
	}, nil
}

// startTermWorkflowWithContext starts a term workflow with the provided context
func (h *Handler) startTermWorkflowWithContext(ctx *handler.HandlerContext, gCtx context.Context, req *dto.CreateProposalResponse) (*dto.StartWorkflowResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(term_proposal.TermProposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting term proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := term_proposal.NewTermProposalClient(conn)

	res, err := client.StartWorkflow(gCtx, &term_proposal.StartWorkflowRequest{
		ProposalId:       req.ProposalID,
		ProposalIntentId: req.ProposalIntentId,
	})
	if err != nil {
		ctx.Logger.Error("Error starting term workflow", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &dto.StartWorkflowResponse{
		WorkflowID: res.WorkflowId,
		RunID:      res.RunId,
		Status:     res.Status,
	}, nil
}
