package orchestration

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/orchestration/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/orchestration/handlers/v1"
)

type OrchestrationApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &OrchestrationApp{}
}

// SetAppName returns the name of the application
func (app *OrchestrationApp) SetAppName() string {
	return "orchestration"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *OrchestrationApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	apiRouter := appContext.Router[constant.HTTP_API]

	requireAuth := false
	apiRouter.RegisterRoute(ctx, appName, "PUT", "/initiate",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.Orchestrate,
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"orchestration.initiate",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/signal-ovd",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.SignalOvd,
				ReqBodyStruct:     &dto.SignalOvdRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"orchestration.signal-ovd",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/dynamic-premium-check",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.DynamicPremiumCheck,
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"orchestration.dynamic-premium-check",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/term/dynamic-premium-check",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.TermDynamicPremiumCheck,
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"orchestration.dynamic-premium-check",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "PUT", "/term/initiate",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.TermOrchestrate,
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"orchestration.initiate",
					}),
				},
			}),
		},
	)
}
