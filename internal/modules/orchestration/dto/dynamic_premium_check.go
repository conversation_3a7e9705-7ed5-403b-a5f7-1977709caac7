package dto

type DynamicPremiumCheckResponse struct {
	OldPremium float64 `json:"old_premium"`
	NewPremium float64 `json:"new_premium"`
}

type DynamicPremiumCheckRequestParams struct {
	SubmissionId     string `json:"submission_id"`
	PurchaseIntentId string `json:"purchase_intent_id"`
}

type TermDynamicPremiumCheckResponse struct {
	OldPremium float64 `json:"old_premium"`
	NewPremium float64 `json:"new_premium"`
}

type TermDynamicPremiumCheckRequestParams struct {
	SubmissionId     string `json:"submission_id"`
	PurchaseIntentId string `json:"purchase_intent_id"`
}
