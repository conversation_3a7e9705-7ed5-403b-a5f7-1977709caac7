package v0

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/creatives/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-marketing/v0/creatives"
	"go.uber.org/zap"
)

func (h *Handler) PersonalizeCreative(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	logger := logger.GetLogger()


	conn, err := h.GrpcClients.Get(creatives.CreativesService_ServiceDesc.ServiceName)
	if err != nil {
		logger.Error("Error getting Creatives Service Client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := creatives.NewCreativesServiceClient(conn)
	grpcCtx := context.Background()
	grpcReq :=&creatives.PersonalizeCreativeRequest{}


	if err := ctx.GinCtx.ShouldBindJSON(grpcReq); err != nil {
        logger.Error("Failed to bind JSON request body for SaveCreativeDetails", zap.Error(err))
        // Return a BadRequest error if the JSON is malformed or doesn't match the struct
        return nil, response.NewInternalServerError()
    }
	

	

	res, err := client.PersonalizeCreative(grpcCtx, grpcReq)
	if err != nil {
		logger.Error("Error sending Personalize Creative request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	logger.Info("Service name", zap.String("name", creatives.CreativesService_ServiceDesc.ServiceName))
	logger.Info("Response", zap.Any("response", res))

	
	

	
	
	resp := &dto.PersonalizeCreativeResponse{
		EventId:res.EventId,
		Message: res.Message,
		
	}
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
} 





