package v0

import (
	"context"
	"net/http"

	"fmt"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/creatives/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-marketing/v0/creatives"
	"go.uber.org/zap"
)

func (h *Handler) UpdateTemplate(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	logger := logger.GetLogger()


	conn, err := h.GrpcClients.Get(creatives.CreativesService_ServiceDesc.ServiceName)
	if err != nil {
		logger.Error("Error getting Creatives Service Client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := creatives.NewCreativesServiceClient(conn)
	grpcCtx := context.Background()
	grpcReq := &creatives.UpdateTemplateRequest{}

	if err := ctx.GinCtx.ShouldBindJSON(grpcReq); err != nil {
        logger.Error("Failed to bind JSON request body for SaveCreativeDetails", zap.Error(err))
        // Return a BadRequest error if the JSON is malformed or doesn't match the struct
        return nil, response.NewInternalServerError()
    }


	
	fmt.Println("logging the grpc request ",grpcReq)

	res, err := client.UpdateTemplate(grpcCtx, grpcReq)
	if err != nil {
		logger.Error("Error making update creative request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	logger.Info("Service name", zap.String("name", creatives.CreativesService_ServiceDesc.ServiceName))
	logger.Info("Response", zap.Any("response", res))

	
	

	
	
	resp := &dto.UpdateTemplateResponse{
		Message: res.Message,
		CreativeId: res.CreativeId,
		
	}
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
} 





