package v0

import (
	"context"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/creatives/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-marketing/v0/creatives"
	"go.uber.org/zap"
)

func (h *Handler) PersonalizeCreativePolling(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	
	logger := logger.GetLogger()
	event_id := ctx.GinCtx.Query("event_id")
	

	conn, err := h.GrpcClients.Get(creatives.CreativesService_ServiceDesc.ServiceName)
	if err != nil {
		logger.Error("Error getting Creatives Service Client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := creatives.NewCreativesServiceClient(conn)
	grpcCtx := context.Background()

	grpcReq := &creatives.PersonalizeCreativePollingRequest{
		EventId: event_id,
	}

	fmt.Println("logging the grpcReq inside gateway",grpcReq)



	// if err := ctx.GinCtx.ShouldBindJSON(grpcReq); err != nil {
    //     logger.Error("Failed to bind JSON request body for Personalize Creative Polling", zap.Error(err))
    //     // Return a BadRequest error if the JSON is malformed or doesn't match the struct
    //     return nil, response.NewInternalServerError()
    // }

	res, err := client.PersonalizeCreativePolling(grpcCtx, grpcReq)
	if err != nil {
		logger.Error("Error sending get upload url request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	logger.Info("Service name", zap.String("name", creatives.CreativesService_ServiceDesc.ServiceName))
	logger.Info("Response", zap.Any("response", res))

	resp := &dto.PersonalizeCreativePollingResponse{
		FinalImageS3Key: res.FinalImageS3Key,
		Message: res.Message,
		Status: res.Status,
	}
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
} 





