package v0

import (
	"context"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/creatives/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-marketing/v0/creatives"
	"go.uber.org/zap"
)

func (h *Handler) GetUploadUrl(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	logger := logger.GetLogger()
	grpcReq := &creatives.GetUploadUrlRequest{}
	

	conn, err := h.GrpcClients.Get(creatives.CreativesService_ServiceDesc.ServiceName)
	if err != nil {
		logger.Error("Error getting Creatives Service Client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	grpcCtx :=context.Background()
	client := creatives.NewCreativesServiceClient(conn)
	if err := ctx.GinCtx.ShouldBindJSON(grpcReq); err != nil {
        logger.Error("Failed to bind JSON request body for Personalize Creative Polling", zap.Error(err))
        // Return a BadRequest error if the JSON is malformed or doesn't match the struct
        return nil, response.NewInternalServerError()
    }


	

	res, err := client.GetUploadUrl(grpcCtx, grpcReq)
	if err != nil {
		logger.Error("Error sending get upload url request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	logger.Info("Service name", zap.String("name", creatives.CreativesService_ServiceDesc.ServiceName))
	logger.Info("Response", zap.Any("response", res))

	resp := &dto.GetUploadUrlResponse{
		UploadURL: res.UploadUrl,
		FileKey:   res.FileKey,
		Message:   res.Message,
	}
	fmt.Printf("logging the response object %v",resp)
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
} 





