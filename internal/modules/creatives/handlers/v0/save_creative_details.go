package v0

import (
	"context"
	"net/http"

	"fmt"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/creatives/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-marketing/v0/creatives"
	"go.uber.org/zap"
)


func (h *Handler) SaveCreativeDetails(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	logger := logger.GetLogger()

	// Step 1: Unmarshal into your DTO
	var dtoReq dto.SaveCreativeDetailsRequest
	if err := ctx.GinCtx.ShouldBindJSON(&dtoReq); err != nil {
		logger.Error("Failed to bind JSON into DTO", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Step 2: Convert to gRPC request
	grpcReq := &creatives.SaveCreativeDetailsRequest{
		ScaleX: dtoReq.ScaleX,
		ScaleY: dtoReq.ScaleY,
		CreativeId: dtoReq.CreativeID,
		Boxes:      make(map[string]*creatives.BoxCoordinateDetails),
	}

	for key, box := range dtoReq.Boxes {
		grpcReq.Boxes[key] = &creatives.BoxCoordinateDetails{
			Coordinates: &creatives.Coordinates{
				PositionX: box.Coordinates.PositionX,
				PositionY: box.Coordinates.PositionY,
				BoxWidth:  box.Coordinates.BoxWidth,
				BoxHeight: box.Coordinates.BoxHeight,
			},
			Text: box.Text,
			Style: &creatives.Style{
				Fill:       box.Style.Fill,
				FontSize:   box.Style.FontSize,
				FontFamily: box.Style.FontFamily,
				FontWeight: box.Style.FontWeight,
				FontStyle:  box.Style.FontStyle,
			},
		}
	}

	// Step 3: Call the gRPC service
	fmt.Println("logging the grpcReq",grpcReq)
	conn, err := h.GrpcClients.Get(creatives.CreativesService_ServiceDesc.ServiceName)
	if err != nil {
		logger.Error("Error getting Creatives Service Client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := creatives.NewCreativesServiceClient(conn)
	res, err := client.SaveCreativeDetails(context.Background(), grpcReq)
	if err != nil {
		logger.Error("Error from gRPC SaveCreativeDetails", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Step 4: Return HTTP response
	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.SaveCreativeDetailsResponse{
			Message:    res.Message,
			CreativeID: res.CreativeId,
		},
	}, nil
}






