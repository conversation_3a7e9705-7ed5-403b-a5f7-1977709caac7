package v0

import (
	"context"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/creatives/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-marketing/v0/creatives"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

func (h *Handler) GetPresignedUrl(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	logger := logger.GetLogger()


	conn, err := h.GrpcClients.Get(creatives.CreativesService_ServiceDesc.ServiceName)
	if err != nil {
		logger.Error("Error getting Creatives Service Client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := creatives.NewCreativesServiceClient(conn)
	grpcCtx := metadata.AppendToOutgoingContext(context.Background())


	

	var dtoReq dto.GetPresignedUrlRequest
	if err := ctx.GinCtx.ShouldBindJSON(&dtoReq); err != nil {
		logger.Error("Failed to bind JSON into DTO", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	creative_id:="placeholder"
	creative_id=dtoReq.CreativeId
	final_image_s3_key:=" "
	final_image_s3_key = dtoReq.FinalImageS3Key
	grpcReq := &creatives.GetPresignedUrlRequest{
		CreativeId: creative_id,
		FinalImageS3Key: final_image_s3_key,
		
	}
	fmt.Println("logging the grpcReq , before making a request",grpcReq)

	

	res, err := client.GetPresignedUrl(grpcCtx, grpcReq)
	if err != nil {
		logger.Error("Error sending get presigned url request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	logger.Info("Service name", zap.String("name", creatives.CreativesService_ServiceDesc.ServiceName))
	logger.Info("Response", zap.Any("response presigned url", res.PresignedUrl))

	
	

	
	
	resp := &dto.GetPresignedUrlResponse{
		PresignedUrl: res.PresignedUrl,
		Message: res.Message,
		Status:res.Status,
		
	}
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
} 





