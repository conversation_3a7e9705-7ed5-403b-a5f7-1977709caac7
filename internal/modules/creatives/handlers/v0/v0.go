package v0

import (
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-protos/go/oa-marketing/v0/creatives"
)

type Handler struct {
	handler.MustEmbedHandler
	GrpcClients *grpc.GrpcClientManager
}


type GrpcHandler struct {
	Handler *Handler // explicit reference, not embedding
	creatives.UnimplementedCreativesServiceServer
}