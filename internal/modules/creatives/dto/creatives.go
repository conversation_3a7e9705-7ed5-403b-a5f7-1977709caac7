package dto

// Step 1: Get presigned URL for direct S3 upload

type GetUploadUrlRequest struct {
	FileName string `json:"file_name"`
}

type GetUploadUrlResponse struct {
	UploadURL string `json:"upload_url"`
	FileKey   string `json:"file_key"`
	Message   string `json:"message"`
}

type CreateTemplateRequest struct {
	CreativeTitle      string `json:"title"`
	CreativeCategory   string `json:"category"`
	OriginalImageS3Key string `json:"file_key"`
}

type UpdateTemplateRequest struct {
	CreativeId string `json:"creative_id"`
}

// Step 2: Save creative details after successful upload
type Coordinates struct {
	PositionX float32 `json:"position_x"`
	PositionY float32 `json:"position_y"`
	BoxWidth  float32 `json:"box_width"`
	BoxHeight float32 `json:"box_height"`
}

type Style struct {
	FontSize   string `json:"fontSize"`
	FontFamily string `json:"fontFamily"`
	Fill       string `json:"fill"`
	FontWeight string `json:"fontWeight"`
	FontStyle  string `json:"fontStyle"`
}

type BoxCoordinateDetails struct {
	Coordinates Coordinates `json:"coordinates"`
	Text        string      `json:"text"`
	Style       Style       `json:"style"`
}

type SaveCreativeDetailsRequest struct {
	Boxes      map[string]BoxCoordinateDetails `json:"boxes"`
	CreativeID string                          `json:"creative_id"`
	ScaleX     string                          `json:"scale_x"`
	ScaleY     string                          `json:"scale_y"`
}

type SaveCreativeDetailsResponse struct {
	CreativeID string `json:"creative_id"`
	Message    string `json:"message"`
}

// Step 3 : Text overlay
type PersonalizeCreativeRequest struct {
	CreativeID string `json:"creative_id"`
	PartnerId  string `json:"partner_id"`
}

type PersonalizeCreativeResponse struct {
	EventId string `json:"event_id"`
	Message string `json:"message"`
}

// Step 4 : Get all creatives
type GetAllCreativesRequest struct {
}

type Creative struct {
	ID                  string `json:"id"`
	CreativeTitle       string `json:"creative_title"`
	CreativeDescription string `json:"creative_description"`
	CreativeCategory    string `json:"creative_category"`
	ThumbnailS3URL      string `json:"thumbnail_s3_url"`
}

type GetAllCreativesResponse struct {
	Creatives []Creative `json:"creatives"`
	Message   string     `json:"message"`
}

type PersonalizeCreativePollingRequest struct {
	EventID string
}

type PersonalizeCreativePollingResponse struct {
	FinalImageS3Key string `json:"final_image_s3_key"`
	Message         string `json:"message"`
	Status          string `json:"status"`
}

type GetPresignedUrlRequest struct {
	CreativeId      string `json:"creative_id"`
	FinalImageS3Key string `json:"final_image_s3_key"`
}

type GetPresignedUrlResponse struct {
	PresignedUrl string `json:"presigned_url"`
	Message      string `'json:"message"`
	Status       string `json:"status"`
}

type UpdateTemplateResponse struct {
	CreativeId string `json:"creative_id"`
	Message    string `json:"message"`
}

type CreateTemplateResponse struct {
	CreativeId string `json:"creative_id"`
	Message    string `json:"message"`
}
