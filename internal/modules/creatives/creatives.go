// File: oa-gateway-svc/internal/modules/creatives/creatives.go
package creatives

import (
	"context"

	// Ensure gin is imported for middleware
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler" // Assuming your middleware is here
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/creatives/dto"
	v0 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/creatives/handlers/v0" // Your handlers package
)

type CreativesApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &CreativesApp{}
}

// SetAppName returns the name of the application
func (app *CreativesApp) SetAppName() string {
	return "creatives"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *CreativesApp) Initialize(appName string, appContext *app.AppContext) {

	ctx := context.Background()

	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	h1 := &v0.Handler{
		GrpcClients: appContext.GrpcClients,
	}
	requireAuth := false // Default to false, adjust per route

	apiRouter := appContext.Router[constant.HTTP_API]

	apiRouter.RegisterRoute(ctx, appName, "POST", "get-upload-url", // Path: /creatives/get-upload-url
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetUploadUrl,
				QueryStruct:       &dto.GetUploadUrlRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: false,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "create-template",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.CreateTemplate,
				ReqBodyStruct:     &dto.CreateTemplateRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: false,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "update-template",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.UpdateTemplate,
				ReqBodyStruct:     &dto.UpdateTemplateRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: false,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "save-details",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.SaveCreativeDetails,
				RequireAuth:       &requireAuth,
				QueryStruct:       &dto.SaveCreativeDetailsRequest{},
				RequireValidation: true,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "personalize",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.PersonalizeCreative,
				QueryStruct: &dto.PersonalizeCreativeRequest{},
				RequireAuth: &requireAuth,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.GetAllCreatives,

				RequireAuth:       &requireAuth,
				RequireValidation: false,
			}),
		},
	)
	apiRouter.RegisterRoute(ctx, appName, "GET", "polling",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.PersonalizeCreativePolling,
				RequireAuth:       &requireAuth,
				RequireValidation: false,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "get-presigned-url",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.GetPresignedUrl,

				RequireAuth:       &requireAuth,
				RequireValidation: false,
			}),
		},
	)

}
