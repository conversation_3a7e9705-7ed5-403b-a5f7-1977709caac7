package policies

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/policies/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/policies/handlers/v1"
)

type PolicyApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &PolicyApp{}
}

// SetAppName returns the name of the application
func (app *PolicyApp) SetAppName() string {
	return "policies"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *PolicyApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	apiRouter := appContext.Router[constant.HTTP_API]

	requireAuth := false
	apiRouter.RegisterRoute(ctx, appName, "GET", "/",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPolicyTable,
				QueryStruct:       &dto.GetPolicyTableRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.get-policy-table",
					}),
				},
			}),
		},
	)
	apiRouter.RegisterRoute(ctx, appName, "GET", "/get-document-upload-url",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetDocumentPresignedUrl,
				QueryStruct:       &dto.GetDocumentPresignedUrlRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/initiate-policy-workflow",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.InitiatePolicyWorkflow,
				QueryStruct:       &dto.InitiatePolicyWorkflowRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.put",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/policy-data",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPolicyData,
				QueryStruct:       &dto.GetPolicyDataRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.policy-data",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "PATCH", "/:policy_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.UpdatePolicyData,
				ReqBodyStruct:     &dto.UpdatePolicyDataPayload{},
				ParamStruct:       &dto.PolicyPathParams{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.update-policy-data",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/policy-document",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPolicyDocument,
				QueryStruct:       &dto.GetPolicyDataRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.policy-document",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/customers",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetCustomerTable,
				QueryStruct:       &dto.GetCustomerTableRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.customers-table",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/customer-data",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetCustomer,
				QueryStruct:       &dto.GetCustomerRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.customer-data",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/policy-table-search",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPolicyTableSearchResult,
				QueryStruct:       &dto.GetPolicyTableSearchResultRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.get-policy-table",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/customer-table-search",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetCustomerTableSearchResult,
				QueryStruct:       &dto.GetCustomerTableSearchResultRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.customers-table",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/approve-offline-policy",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.ApproveOfflinePolicy,
				QueryStruct:       &dto.ApproveOfflinePolicyRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"policies.approve-offline-policy",
					}),
				},
			}),
		},
	)
}
