package dto

type UpdatePolicyDataPayload struct {
	UpdateData UpdateData `json:"updateData" validate:"required"`
}

type UpdateData struct {
	ProposerDetails *ProposerDetails `json:"proposerDetails"`
	PolicyDetails   *PolicyDetails   `json:"policyDetails"`
	BankDetails     *BankDetails     `json:"bankDetails"`
	MemberDetails   *MemberDetails   `json:"memberDetails"`
	NomineeDetails  *NomineeDetails  `json:"nomineeDetails"`
	PedDetails      *PedDetails      `json:"pedDetails"`
}

type Address struct {
	AddressLine1 string  `json:"address_line_1" validate:"required"`
	AddressLine2 string  `json:"address_line_2" validate:"required"`
	AddressLine3 *string `json:"address_line_3"`
	City         string  `json:"city" validate:"required"`
	State        string  `json:"state" validate:"required"`
	Country      string  `json:"country" validate:"required"`
	Pincode      string  `json:"pincode" validate:"required,numeric"`
}

type ProposerDetails struct {
	Data       ProposerData `json:"data" validate:"required"`
	IsEditable bool         `json:"is_editable"`
}

type ProposerData struct {
	Name    string `json:"name" validate:"required"`
	Email   string `json:"email" validate:"required,email"`
	Phone   string `json:"phone" validate:"required,numeric"`
	Address string `json:"address" validate:"required"`
	DOB     string `json:"dob" validate:"required"`
	Pincode string `json:"pincode" validate:"required,numeric"`
}

type PolicyDetails struct {
	PolicyNumber string  `json:"policy_number" validate:"required"`
	SumInsured   float64 `json:"sum_insured" validate:"required,gt=0"`
	Tenure       int     `json:"tenure" validate:"required,gt=0"`
	TotalPremium float64 `json:"total_premium" validate:"required,gt=0"`
	IssuanceDate string  `json:"issuance_date" validate:"required"`
	ExpiryDate   string  `json:"expiry_date" validate:"required"`
	PolicyType   string  `json:"policy_type" validate:"required"`
}

type BankDetails struct {
	BankName          string `json:"bank_name" validate:"required"`
	BankID            string `json:"bank_id" validate:"required"`
	BranchName        string `json:"branch_name" validate:"required"`
	BranchID          string `json:"branch_id" validate:"required"`
	IFSCCode          string `json:"ifsc_code" validate:"required,alphanum"`
	BankAccountNumber string `json:"bank_account_number" validate:"required,numeric"`
}

type MemberDetails struct {
	Name                string   `json:"name" validate:"required"`
	Relationship        string   `json:"relationship" validate:"required"`
	DOB                 string   `json:"dob" validate:"required"`
	Gender              string   `json:"gender" validate:"required"`
	MemberID            string   `json:"member_id" validate:"required"`
	PreExistingDiseases []string `json:"pre_existing_diseases" validate:"dive,required"`
}

type NomineeDetails struct {
	Name         string `json:"name" validate:"required"`
	Relationship string `json:"relationship" validate:"required"`
	DOB          string `json:"dob" validate:"required"`
	Gender       string `json:"gender" validate:"required"`
	Address      string `json:"address" validate:"required"`
	Pincode      string `json:"pincode" validate:"required,numeric"`
}

type PedDetails struct {
	PreExistingDiseases []string `json:"pre_existing_diseases" validate:"dive,required"`
	MedicalHistory      []string `json:"medical_history" validate:"dive,required"`
	CurrentMedications  []string `json:"current_medications" validate:"dive,required"`
}

type PolicyPathParams struct {
	PolicyID string `uri:"policy_id" validate:"required"` // Tag for Gin binding
}
