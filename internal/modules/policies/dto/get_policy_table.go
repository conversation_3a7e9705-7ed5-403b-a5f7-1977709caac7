package dto

type GetPolicyTableRequest struct {
	Limit         int32   `form:"limit" validate:"required,min=10,max=100"`
	StartDate     *string `form:"start_date"`
	EndDate       *string `form:"end_date"`
	LastPolicyId  *string `form:"last_policy_id" validate:"omitempty"`
	FirstPolicyId *string `form:"first_policy_id" validate:"omitempty"`
	Statuses      *string `form:"statuses" validate:"omitempty,oneof=PENDING ISSUED REJECTED NEEDS_REVIEW CANCELLED"`
	InsurerIds    *string `form:"insurer_ids" validate:"omitempty"`
	ProductIds    *string `form:"product_ids" validate:"omitempty"`
	ExpiryDays    *int32  `form:"expiry_days" validate:"omitempty,numeric"`
}
