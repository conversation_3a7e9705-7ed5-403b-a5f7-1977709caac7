package v1

import (
	"context"
	"net/http"
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/policies/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-gateway-svc/internal/utility"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
)

// PolicyTableResponse represents the transformed response structure
type PolicyTableResponse struct {
	Success     bool              `json:"success"`
	Policies    []PolicyTableItem `json:"policies"`
	EndCursor   string            `json:"end_cursor,omitempty"`
	StartCursor string            `json:"start_cursor,omitempty"`
	HasPrevious bool              `json:"has_previous"`
	HasNext     bool              `json:"has_next"`
	Count       int32             `json:"count"`
	InsurerList []InsurerInfo     `json:"insurer_list"`
	ProductList []ProductInfo     `json:"product_list"`
	StatusList  []StatusInfo      `json:"status_list"`
	ExpiryList  []ExpiryInfo      `json:"expiry_list"`
}

type PolicyTableItem struct {
	ID                  string `json:"id"`
	InsurerPolicyNumber string `json:"insurer_policy_number,omitempty"`
	ProposerName        string `json:"proposer_name,omitempty"`
	Phone               string `json:"phone,omitempty"`
	Status              string `json:"status,omitempty"`
	ProductName         string `json:"product_name,omitempty"`
	InsurerName         string `json:"insurer_name,omitempty"`
	PremiumAmount       string `json:"premium_amount,omitempty"`
	SumInsured          string `json:"sum_insured,omitempty"`
	IssuanceDate        string `json:"issuance_date,omitempty"`
	ExpiryDate          string `json:"expiry_date,omitempty"`
	PolicyType          string `json:"policy_type,omitempty"`
	ProductId           string `json:"product_id,omitempty"`
	InsurerId           string `json:"insurer_id,omitempty"`
	PolicySource        string `json:"policy_source,omitempty"`
	PartnerName         string `json:"partner_name,omitempty"`
	EnterpriseName      string `json:"enterprise_name,omitempty"`
	// PolicyVertical      string `json:"policy_vertical,omitempty"`
	PolicyNumber string `json:"policy_number,omitempty"`
	ReferenceId  string `json:"reference_id,omitempty"`
	ExpiryDays   int32  `json:"expiry_days,omitempty"`
}

type InsurerInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type ProductInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type StatusInfo struct {
	Value string `json:"value"`
	Count int32  `json:"count"`
}

type ExpiryInfo struct {
	Value string `json:"value"`
	Count int32  `json:"count"`
}

func (h *Handler) GetPolicyTable(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	var query dto.GetPolicyTableRequest

	err = ctx.GinCtx.ShouldBindQuery(&query)
	if err != nil {
		ctx.Logger.Error("Error binding query", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcReq := &policies.GetPolicyTableRequest{
		Limit:         &query.Limit,
		LastPolicyId:  query.LastPolicyId,
		FirstPolicyId: query.FirstPolicyId,
		StartDate:     query.StartDate,
		EndDate:       query.EndDate,
		ExpiryDays:    query.ExpiryDays,
	}

	// Handle InsurerIds - only split if not nil and not empty
	if query.InsurerIds != nil && *query.InsurerIds != "" {
		grpcReq.InsurerIds = strings.Split(*query.InsurerIds, ",")
	}

	// Handle ProductIds - only split if not nil and not empty
	if query.ProductIds != nil && *query.ProductIds != "" {
		grpcReq.ProductIds = strings.Split(*query.ProductIds, ",")
	}

	if query.Statuses != nil {
		statuses := strings.Split(*query.Statuses, ",")
		for _, status := range statuses {
			grpcReq.PolicyStatuses = append(grpcReq.PolicyStatuses, policies.PolicyStatus(policies.PolicyStatus_value[status]))
		}
	}

	res, err := client.GetPolicyTable(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetPolicyTable gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Transform the response
	transformedResponse := PolicyTableResponse{
		Success:     res.Success,
		Policies:    make([]PolicyTableItem, 0, len(res.Policies)),
		EndCursor:   res.GetEndCursor(),
		StartCursor: res.GetStartCursor(),
		HasPrevious: res.HasPrevious,
		HasNext:     res.HasNext,
		Count:       res.Count,
		InsurerList: make([]InsurerInfo, 0),
		ProductList: make([]ProductInfo, 0),
		StatusList:  make([]StatusInfo, 0),
		ExpiryList:  make([]ExpiryInfo, 0),
	}

	role := ctx.GinCtx.GetString("role")
	enterpriseType := ctx.GinCtx.GetString("enterprise_type")

	// Collect partner IDs from policies
	partnerEnterpriseIds := make([]struct {
		PartnerId    string
		EnterpriseId string
	}, 0)
	for _, policy := range res.Policies {
		if policy.PartnerId != "" {
			partnerEnterpriseIds = append(partnerEnterpriseIds, struct {
				PartnerId    string
				EnterpriseId string
			}{PartnerId: policy.PartnerId, EnterpriseId: policy.EnterpriseId})
		}
	}

	// Get partner and enterprise information using utility function
	partnerMap, err := utility.GetPartnerEnterpriseNameV1(
		grpcCtx.(context.Context),
		h.GrpcClients,
		ctx.Logger,
		role,
		enterpriseType,
		partnerEnterpriseIds,
	)
	if err != nil {
		ctx.Logger.Error("Error getting partner enterprise names", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Transform each policy
	for _, policy := range res.Policies {
		item := PolicyTableItem{
			ID:                  policy.PolicyId,
			InsurerPolicyNumber: policy.InsurerPolicyNumber,
			ProposerName:        policy.ProposerName,
			Phone:               policy.Phone,
			Status:              policy.Status,
			ProductName:         policy.ProductName,
			InsurerName:         policy.InsurerName,
			PremiumAmount:       policy.PremiumAmount,
			SumInsured:          policy.SumInsured,
			IssuanceDate:        policy.IssuanceDate,
			ExpiryDate:          policy.ExpiryDate,
			PolicyType:          policy.PolicyType,
			PolicySource:        policy.PolicySource,
			ProductId:           policy.ProductId,
			InsurerId:           policy.InsurerId,
			PolicyNumber:        policy.PolicyNumber,
			ReferenceId:         policy.ReferenceId,
			// ExpiryDays:          policy.ExpiryDays,
		}
		mapKey := policy.PartnerId
		if policy.PartnerId == "9999999999999999" {
			mapKey = policy.EnterpriseId
		}
		if partnerInfo, exists := partnerMap[mapKey]; exists {
			item.PartnerName = partnerInfo.PartnerName
			item.EnterpriseName = partnerInfo.EnterpriseName
		}
		transformedResponse.Policies = append(transformedResponse.Policies, item)
	}

	// Transform insurer list
	if res.InsurerList != nil {
		for _, insurer := range res.InsurerList.Values {
			if insurer.GetStructValue() != nil {
				fields := insurer.GetStructValue().Fields
				if id := fields["id"]; id != nil {
					if name := fields["name"]; name != nil {
						insurerInfo := InsurerInfo{
							ID:   id.GetStringValue(),
							Name: name.GetStringValue(),
						}
						transformedResponse.InsurerList = append(transformedResponse.InsurerList, insurerInfo)
					}
				}
			}
		}
	}

	// Transform product list
	if res.ProductList != nil {
		for _, product := range res.ProductList.Values {
			if product.GetStructValue() != nil {
				fields := product.GetStructValue().Fields
				if id := fields["id"]; id != nil {
					if name := fields["name"]; name != nil {
						productInfo := ProductInfo{
							ID:   id.GetStringValue(),
							Name: name.GetStringValue(),
						}
						transformedResponse.ProductList = append(transformedResponse.ProductList, productInfo)
					}
				}
			}
		}
	}

	// Transform status list
	if res.StatusList != nil {
		for _, status := range res.StatusList.Values {
			if status.GetStructValue() != nil {
				fields := status.GetStructValue().Fields
				if value := fields["value"]; value != nil {
					statusValue := value.GetStringValue()
					transformedResponse.StatusList = append(transformedResponse.StatusList, StatusInfo{
						Value: statusValue,
						Count: 0,
					})
				}
			}
		}
	}

	// Transform expiry list
	if res.ExpiryList != nil {
		for _, expiry := range res.ExpiryList.Values {
			if expiry.GetStructValue() != nil {
				fields := expiry.GetStructValue().Fields
				if value := fields["value"]; value != nil {
					expiryValue := value.GetStringValue()
					transformedResponse.ExpiryList = append(transformedResponse.ExpiryList, ExpiryInfo{
						Value: expiryValue,
						Count: int32(fields["count"].GetNumberValue()),
					})
				}
			}
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: transformedResponse,
	}, nil
}
