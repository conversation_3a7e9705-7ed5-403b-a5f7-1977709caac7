package v1

import (
	"context"
	"net/http"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	proposal "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
)

func (h *Handler) GetCustomerTable(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Parse query parameters
	limitStr := ctx.GinCtx.DefaultQuery("limit", "10")
	parsedLimit, err := strconv.ParseInt(limitStr, 10, 32)
	if err != nil {
		ctx.Logger.Warn("Invalid limit format, using default", zap.String("limit", limitStr), zap.Error(err))
		parsedLimit = 10 // Use default if parsing fails
	}
	limitInt := int32(parsedLimit)

	lastCustomerId := ctx.GinCtx.Query("last_customer_id")
	firstCustomerId := ctx.GinCtx.Query("first_customer_id")
	// Removed unused query params: status, firstPolicyId

	// Build the gRPC request using updated proto fields
	grpcReq := &policies.GetCustomerTableRequest{
		Limit: &limitInt,
		// EnterpriseId field removed from here (it's in metadata)
	}
	if lastCustomerId != "" {
		grpcReq.LastCustomerId = &lastCustomerId
	}
	if firstCustomerId != "" {
		grpcReq.FirstCustomerId = &firstCustomerId
	}
	// Removed StatusFilter and FirstPolicyId assignments

	// Make the gRPC call
	res, err := client.GetCustomerTable(grpcCtx.(context.Context), grpcReq) // Pass context with metadata
	if err != nil {
		// Consider handling specific gRPC errors (e.g., NotFound, InvalidArgument)
		ctx.Logger.Error("Error calling GetCustomerTable gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError() // Or translate gRPC error to HTTP error
	}

	// Return the response from the downstream service
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
