package v1

import (
	"context"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	oa_errors "github.com/oneassure-tech/oa-utilities/go/oa-errors"
	"go.uber.org/zap"
)

func (h *Handler) GetCustomerTableSearchResult(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	searchQuery := ctx.GetQuery("search_query")

	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting policies client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	// Get enterprise_id from token context
	enterpriseID := ctx.GinCtx.GetString("enterprise_id")
	if enterpriseID == "" {
		ctx.Logger.Error("Enterprise ID missing from context")
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusUnauthorized,
			},
			Problem: &response.Problem{
				Type:   oa_errors.ErrInvalidInput.GetCode(),
				Title:  oa_errors.ErrInvalidInput.GetMessage(),
				Detail: "Enterprise ID is required and missing from the request context.",
			},
		}
	}

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	fmt.Println("Search query", searchQuery)

	// Create the request for gRPC service
	grpcReq := &policies.GetCustomerTableSearchResultsRequest{
		SearchQuery: searchQuery,
	}

	// Call the gRPC service
	res, err := client.GetCustomerTableSearchResults(grpcCtx.(context.Context), grpcReq)

	if err != nil {
		ctx.Logger.Error("Error sending search results", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	ctx.Logger.Info("Service name", zap.String("name", policies.Policies_ServiceDesc.ServiceName))

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
