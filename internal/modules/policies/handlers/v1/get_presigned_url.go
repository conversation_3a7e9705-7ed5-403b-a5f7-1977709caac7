package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
)

// GetPresignedUrl generates a presigned URL for document uploads
func (h *Handler) GetPresignedUrl(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Get gRPC client
	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting policies client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Create the request for gRPC service
	grpcReq := &policies.GetDocumentPresignedUrlRequest{}

	// Call the gRPC service
	res, err := client.GetDocumentPresignedUrl(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error getting presigned URL", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
