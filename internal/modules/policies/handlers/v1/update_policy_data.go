package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
)

func (h *Handler) UpdatePolicyData(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Extract path parameter for policy ID
	policyID := ctx.GetParam("policy_id")
	if policyID == "" {
		ctx.Logger.Error("Policy ID is missing from path parameters")
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusBadRequest},
			Problem: &response.Problem{Detail: "Policy ID is required"},
		}
	}

	// Parse request body into a map
	var reqBody map[string]interface{}
	if err := ctx.ExtractHttpRequest(&reqBody); err != nil {
		ctx.Logger.Error("Failed to extract request data from context", zap.Error(err))
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusBadRequest},
			Problem: &response.Problem{Detail: "Invalid request format: " + err.Error()},
		}
	}

	// Log the parsed request body
	reqBodyJSON, _ := json.Marshal(reqBody)
	ctx.Logger.Info("Parsed request body", zap.String("request", string(reqBodyJSON)))

	// Get the updateData from the request
	updateData, ok := reqBody["updateData"].(map[string]interface{})
	if !ok {
		ctx.Logger.Error("Invalid updateData format in request")
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusBadRequest},
			Problem: &response.Problem{Detail: "Invalid updateData format"},
		}
	}

	// Create a new map for the update data
	filteredData := make(map[string]interface{})

	// Only include non-null sections
	for key, value := range updateData {
		if value != nil {
			filteredData[key] = value
		}
	}

	// Verify we have data to update
	if len(filteredData) == 0 {
		ctx.Logger.Error("Request body is empty or contains no fields")
		return nil, &response.ErrorResponse{
			Status:  &response.Status{HttpStatus: http.StatusBadRequest},
			Problem: &response.Problem{Detail: "Request body cannot be empty for update operation"},
		}
	}

	// Log the update payload
	updateDataJSON, _ := json.Marshal(filteredData)
	ctx.Logger.Info("Updating policy data",
		zap.String("policy_id", policyID),
		zap.String("update_data", string(updateDataJSON)))

	// Get gRPC client
	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting policies client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Convert the map payload to google.protobuf.Struct for gRPC
	updateStruct, err := structpb.NewStruct(filteredData)
	if err != nil {
		ctx.Logger.Error("Error converting payload to proto struct", zap.Error(err), zap.Any("update_data", filteredData))
		return nil, response.NewInternalServerError()
	}

	// Create the gRPC request for updating policy data
	grpcReq := &policies.UpdatePolicyDataRequest{
		PolicyId:         policyID,
		PolicyDataStruct: updateStruct,
	}

	// Log the gRPC request
	grpcReqJSON, _ := json.Marshal(grpcReq)
	ctx.Logger.Info("Sending gRPC request", zap.String("request", string(grpcReqJSON)))

	// Call the gRPC service to update policy data
	res, err := client.UpdatePolicyData(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		// Extract detailed error information from gRPC status
		if st, ok := status.FromError(err); ok {
			ctx.Logger.Error("gRPC service error",
				zap.String("policy_id", policyID),
				zap.String("grpc_code", st.Code().String()),
				zap.String("grpc_message", st.Message()),
				zap.Error(err))

			// Map gRPC error codes to HTTP status codes and return detailed errors
			switch st.Code() {
			case codes.NotFound:
				return nil, &response.ErrorResponse{
					Status: &response.Status{HttpStatus: http.StatusNotFound},
					Problem: &response.Problem{
						Title:  "Policy Not Found",
						Detail: st.Message(),
					},
				}
			case codes.InvalidArgument:
				return nil, &response.ErrorResponse{
					Status: &response.Status{HttpStatus: http.StatusBadRequest},
					Problem: &response.Problem{
						Title:  "Invalid Request",
						Detail: st.Message(),
					},
				}
			case codes.PermissionDenied:
				return nil, &response.ErrorResponse{
					Status: &response.Status{HttpStatus: http.StatusForbidden},
					Problem: &response.Problem{
						Title:  "Permission Denied",
						Detail: st.Message(),
					},
				}
			case codes.Internal:
				return nil, &response.ErrorResponse{
					Status: &response.Status{HttpStatus: http.StatusInternalServerError},
					Problem: &response.Problem{
						Title:  "Internal Server Error",
						Detail: st.Message(),
					},
				}
			default:
				return nil, &response.ErrorResponse{
					Status: &response.Status{HttpStatus: http.StatusInternalServerError},
					Problem: &response.Problem{
						Title:  "Service Error",
						Detail: fmt.Sprintf("gRPC error [%s]: %s", st.Code().String(), st.Message()),
					},
				}
			}
		}

		// Fallback for non-gRPC errors
		ctx.Logger.Error("Error calling UpdatePolicyData gRPC service",
			zap.Error(err),
			zap.String("policy_id", policyID),
			zap.Any("request", grpcReq))
		return nil, &response.ErrorResponse{
			Status: &response.Status{HttpStatus: http.StatusInternalServerError},
			Problem: &response.Problem{
				Title:  "Service Communication Error",
				Detail: fmt.Sprintf("Failed to communicate with policy service: %v", err),
			},
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
