package v1

import (
	"context"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/policies/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
)

func (h *Handler) InitiatePolicyWorkflow(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	fmt.Println("InitiatePolicyWorkflow in handler")
	var req dto.InitiatePolicyWorkflowRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	if req.FileUuid == "" || req.Email == "" || req.PhoneNumber == "" {
		ctx.Logger.Error("Missing required fields")
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Detail: "Document key (file_uuid), email, and phone number are required",
			},
		}
	}

	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting policies client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	grpcReq := &policies.InitiatePolicyWorkflowRequest{
		FileUuid:    req.FileUuid,
		Email:       &req.Email,
		PhoneNumber: req.PhoneNumber,
		ProposalId:  &req.ProposalId,
	}

	if req.LeadId != "" {
		grpcReq.LeadId = &req.LeadId
	}

	if req.ProposalId != "" {
		grpcReq.ProposalId = &req.ProposalId
	}

	if req.PartnerId != "" {
		grpcReq.PartnerId = &req.PartnerId
	}

	if req.EnterpriseId != "" {
		grpcReq.EnterpriseId = &req.EnterpriseId
	}

	res, err := client.InitiatePolicyWorkflow(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error initiating policy workflow", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}

// // InitiatePolicyWorkflowFromSendDocument is a helper method to initiate a policy workflow after sending a document
// func (h *Handler) InitiatePolicyWorkflowFromSendDocument(ctx *handler.HandlerContext, documentKey, email, phoneNumber string, proposalId string) (*response.SuccessResponse, *response.ErrorResponse) {
// 	// Create a request with the document key and customer details
// 	req := dto.InitiatePolicyWorkflowRequest{
// 		FileUuid:    documentKey,
// 		Email:       email,
// 		PhoneNumber: phoneNumber,
// 	}

// 	if proposalId != "" {
// 		req.ProposalId = proposalId
// 	}

// 	// Get gRPC client
// 	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
// 	if err != nil {
// 		ctx.Logger.Error("Error getting policies client", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}

// 	client := policies.NewPoliciesClient(conn)

// 	// Prepare metadata
// 	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", "18TzlaIOrzcGFHY7") // TODO: Get from context
// 	grpcCtx = metadata.AppendToOutgoingContext(grpcCtx, "partner_id", "18WRepUqi737YExp")                  // TODO: Get from context

// 	// Create the request for gRPC service
// 	grpcReq := &policies.InitiatePolicyWorkflowRequest{
// 		FileUuid:    documentKey,
// 		Email:       &email,
// 		PhoneNumber: &phoneNumber,
// 	}

// 	if proposalId != "" {
// 		grpcReq.ProposalId = &proposalId
// 	}

// 	// Call the gRPC service
// 	res, err := client.InitiatePolicyWorkflow(grpcCtx, grpcReq)
// 	if err != nil {
// 		ctx.Logger.Error("Error initiating policy workflow", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}

// 	return &response.SuccessResponse{
// 		Status:  http.StatusOK,
// 		Payload: res,
// 	}, nil
// }
