package v1

import (
	"context"
	"net/http"
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-common/v0/common"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
	"google.golang.org/grpc/status"
)

func (h *Handler) GetPolicyData(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Extract the request data from query parameters
	policyId := ctx.GetQuery("policy_id")

	// Validate required fields
	if policyId == "" {
		ctx.Logger.Error("Missing required fields")
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Detail: "Policy ID is required",
			},
		}
	}

	// Get gRPC client
	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting policies client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Call the gRPC service
	res, err := client.GetPolicyData(grpcCtx.(context.Context), &policies.GetPolicyDataRequest{
		PolicyId: policyId,
	})
	if err != nil {
		statusCode := extractStatusCode(err)
		if statusCode == "Unavailable" {
			// Return a 202 status code for processing state with a meaningful message
			return &response.SuccessResponse{
				Status: http.StatusAccepted,
				Payload: map[string]interface{}{
					"status":    "PROCESSING",
					"message":   "The policy data is being processed. Please try again later.",
					"policy_id": policyId,
				},
			}, nil
		}

		if statusCode == "AlreadyExists" {
			// Extract existing policy ID from gRPC status details
			var existingPolicyID string
			var detailMessage string

			// Try to extract from gRPC status details first
			if st, ok := status.FromError(err); ok {
				for _, detail := range st.Details() {
					if commonErr, ok := detail.(*common.Error); ok {
						detailMessage = commonErr.GetDetail()
						break
					}
				}
			}

			// If no detailed message found, fall back to error string
			if detailMessage == "" {
				detailMessage = err.Error()
			}

			// Look for pattern like [EXISTING_POLICY_ID: abcedef] in the detail message
			if strings.Contains(detailMessage, "[EXISTING_POLICY_ID:") {
				// Find the start of the existing policy ID
				startIndex := strings.Index(detailMessage, "[EXISTING_POLICY_ID:")
				if startIndex != -1 {
					// Move past "[EXISTING_POLICY_ID:"
					idStart := startIndex + len("[EXISTING_POLICY_ID:")
					// Find the end of the ID (closing bracket)
					idEnd := strings.Index(detailMessage[idStart:], "]")
					if idEnd != -1 {
						existingPolicyID = strings.TrimSpace(detailMessage[idStart : idStart+idEnd])
					}
				}
			}

			response := &response.SuccessResponse{
				Status: http.StatusAlreadyReported,
				Payload: map[string]interface{}{
					"status":    "REJECTED",
					"message":   "A policy with this ID already exists.",
					"policy_id": existingPolicyID,
				},
			}

			// Add existing policy ID to response if found
			if existingPolicyID != "" {
				response.Payload.(map[string]interface{})["existing_policy_id"] = existingPolicyID
			}

			return response, nil
		}

		ctx.Logger.Error("Error getting policy data", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	ctx.Logger.Info("Successfully retrieved policy data")
	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}

// extractStatusCode extracts the status code from a gRPC error
func extractStatusCode(err error) string {
	if err == nil {
		return ""
	}

	// The error message format is typically: "rpc error: code = Unavailable desc = Policy is being processed"
	errMsg := err.Error()

	// Use a more reliable method to extract the status code
	// Look for "code = " followed by a word
	codePrefix := "code = "
	codeIndex := strings.Index(errMsg, codePrefix)
	if codeIndex == -1 {
		return ""
	}

	// Extract the status code
	codeStart := codeIndex + len(codePrefix)
	codeEnd := strings.Index(errMsg[codeStart:], " ")
	if codeEnd == -1 {
		// If there's no space after the code, take the rest of the string
		return errMsg[codeStart:]
	}

	return errMsg[codeStart : codeStart+codeEnd]
}
