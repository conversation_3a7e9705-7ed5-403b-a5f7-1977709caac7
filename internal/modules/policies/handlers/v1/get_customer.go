package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
)

func (h *Handler) GetCustomer(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	customerId := ctx.GetQuery("customer_id")

	if customerId == "" {
		ctx.Logger.Error("Missing required fields")
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Detail: "Customer ID is required",
			},
		}
	}

	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting policies client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	grpcReq := &policies.GetCustomerRequest{
		CustomerId: customerId,
	}

	res, err := client.GetCustomer(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error getting customer data", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
