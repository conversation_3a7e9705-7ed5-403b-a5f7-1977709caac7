package v1

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/payouts/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/payouts"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"
)

func (h *Handler) GetCommissionConfig(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	log := ctx.Logger

	var req dto.GetCommissionConfigRequest
	err := ctx.GinCtx.ShouldBindQuery(&req)
	if err != nil {
		log.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(payouts.Payouts_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Error getting payouts client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	payoutsClient := payouts.NewPayoutsClient(conn)

	grpcRequest := &payouts.GetCommisionConfigRequest{
		PartnerId:    req.PartnerID,
		EnterpriseId: req.EnterpriseID,
		InsurerId:    req.InsurerID,
		ProductId:    req.ProductID,
	}

	grpcResponse, err := payoutsClient.GetCommisionConfig(ctx.GinCtx, grpcRequest)
	if err != nil {
		log.Error("Error getting commission config", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	configs := make([]dto.CommissionConfig, 0)
	for _, config := range grpcResponse.Configs {
		additionalCriteria := make(map[string]interface{})
		if config.AdditionalCriteria != nil {
			additionalCriteria = config.AdditionalCriteria.AsMap()
		}

		configs = append(configs, dto.CommissionConfig{
			ID:                 config.Id,
			BusinessType:       config.BusinessType.String(),
			CommissionRate:     config.CommissionRate,
			AdditionalCriteria: additionalCriteria,
		})
	}

	res := &dto.GetCommissionConfigResponse{
		PartnerID:    grpcResponse.PartnerId,
		EnterpriseID: grpcResponse.EnterpriseId,
		InsurerID:    grpcResponse.InsurerId,
		ProductID:    grpcResponse.ProductId,
		Configs:      configs,
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}

func (h *Handler) SaveCommissionConfig(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	log := ctx.Logger

	var req dto.SaveCommissionConfigRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		log.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(payouts.Payouts_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Error getting payouts client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	payoutsClient := payouts.NewPayoutsClient(conn)

	// Convert business type string to enum
	var businessType payouts.BusinessType
	switch req.BusinessType {
	case "RENEWAL":
		businessType = payouts.BusinessType_RENEWAL
	case "FRESH":
		businessType = payouts.BusinessType_FRESH
	case "PORTING":
		businessType = payouts.BusinessType_PORTING
	default:
		log.Error("Invalid business type", zap.String("business_type", req.BusinessType))
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Type:   "INVALID_INPUT",
				Title:  "Bad Request",
				Detail: "Invalid business type. Must be one of: RENEWAL, FRESH, PORTING",
			},
		}
	}

	// Convert additional criteria to protobuf struct
	var additionalCriteria *structpb.Struct
	if req.AdditionalCriteria != nil {
		additionalCriteria, err = structpb.NewStruct(req.AdditionalCriteria)
		if err != nil {
			log.Error("Error converting additional criteria to protobuf struct", zap.Error(err))
			return nil, response.NewInternalServerError()
		}
	}

	grpcRequest := &payouts.SaveCommisionConfigRequest{
		PartnerId:          req.PartnerID,
		EnterpriseId:       req.EnterpriseID,
		InsurerId:          req.InsurerID,
		ProductId:          req.ProductID,
		BusinessType:       businessType,
		CommissionRate:     req.CommissionRate,
		AdditionalCriteria: additionalCriteria,
	}

	grpcResponse, err := payoutsClient.SaveCommisionConfig(ctx.GinCtx, grpcRequest)
	if err != nil {
		log.Error("Error saving commission config", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	res := &dto.SaveCommissionConfigResponse{
		Success:      grpcResponse.Success,
		ErrorMessage: grpcResponse.ErrorMessage,
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
