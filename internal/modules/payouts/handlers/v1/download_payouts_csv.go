package v1

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/payouts/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/payouts"
	"go.uber.org/zap"
)

func (h *Hand<PERSON>) DownloadPartnerPayoutCSV(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	log := ctx.Logger

	var req dto.DownloadPartnerPayoutCSVRequest
	err := ctx.GinCtx.ShouldBindQuery(&req)
	if err != nil {
		log.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(payouts.Payouts_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Error getting payouts client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	payoutsClient := payouts.NewPayoutsClient(conn)

	downloadResponse, err := payoutsClient.DownloadPartnerPayoutCSV(ctx.GinCtx, &payouts.DownloadPartnerPayoutCSVRequest{
		PayoutBatchId: req.PayoutBatchID,
	})

	if err != nil {
		log.Error("Error downloading partner payout CSV", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	res := &dto.DownloadPartnerPayoutCSVResponse{
		URL:          downloadResponse.Url,
		ErrorMessage: downloadResponse.ErrorMessage,
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}

func (h *Handler) GetPartnerSummaryCSV(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	log := ctx.Logger

	var req dto.GetPartnerSummaryCSVRequest
	err := ctx.GinCtx.ShouldBindQuery(&req)
	if err != nil {
		log.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(payouts.Payouts_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Error getting payouts client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	payoutsClient := payouts.NewPayoutsClient(conn)

	grpcResponse, err := payoutsClient.GetPartnerSummaryCSV(ctx.GinCtx, &payouts.GetPartnerSummaryCSVRequest{
		PayoutBatchId: req.PayoutBatchID,
	})

	if err != nil {
		log.Error("Error getting partner summary", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	res := &dto.GetPartnerSummaryCSVResponse{
		Success:      grpcResponse.Success,
		URL:          grpcResponse.Url,
		ErrorMessage: grpcResponse.ErrorMessage,
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
