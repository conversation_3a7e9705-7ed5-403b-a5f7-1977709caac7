package v1

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/payouts/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/payouts"
	"go.uber.org/zap"
)

func (h *Handler) LockPayoutBatch(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	log := ctx.Logger

	conn, err := h.GrpcClients.Get(payouts.Payouts_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Error getting payouts client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	payoutsClient := payouts.NewPayoutsClient(conn)

	var req dto.LockPayoutBatchRequest
	err = ctx.GinCtx.ShouldBindQuery(&req)
	if err != nil {
		log.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	resp, err := payoutsClient.LockPayoutBatch(ctx.GinCtx, &payouts.LockPayoutBatchRequest{
		PayoutBatchId: req.PayoutBatchID,
	})

	if err != nil {
		log.Error("Error locking payout batch", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
