package v1

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/payouts/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/payouts"
	"go.uber.org/zap"
)

func (h *Handler) GetPayoutBatches(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	log := ctx.Logger

	var req dto.GetPayoutBatchesRequest
	err := ctx.GinCtx.ShouldBindQuery(&req)
	if err != nil {
		log.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(payouts.Payouts_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Error getting payouts client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	payoutsClient := payouts.NewPayoutsClient(conn)

	var payoutRequest *payouts.GetPayoutBatchesRequest
	if req.Page != nil && req.PageSize != nil {
		payoutRequest = &payouts.GetPayoutBatchesRequest{
			Page:     req.Page,
			PageSize: req.PageSize,
		}
	}

	if req.StartDate != nil || req.EndDate != nil {
		if payoutRequest == nil {
			payoutRequest = &payouts.GetPayoutBatchesRequest{}
		}
		if req.StartDate != nil {
			payoutRequest.StartDate = req.StartDate
		}
		if req.EndDate != nil {
			payoutRequest.EndDate = req.EndDate
		}
	}

	payoutBatches, err := payoutsClient.GetPayoutBatches(ctx.GinCtx, payoutRequest)

	log.Info("Payout batche===============s", zap.Any("payoutBatches", payoutBatches))
	if err != nil {
		log.Error("Error getting payout batches", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	res := &dto.GetPayoutBatchesResponse{
		Batches:    make([]dto.PayoutBatch, 0),
		TotalPages: int32(payoutBatches.TotalPages),
		PageNumber: int32(payoutBatches.PageNumber),
	}

	for _, batch := range payoutBatches.Batches {
		res.Batches = append(res.Batches, dto.PayoutBatch{
			ID:          batch.Id,
			StartDate:   batch.StartDate,
			EndDate:     batch.EndDate,
			GeneratedAt: *batch.GeneratedAt,
			Locked:      batch.Locked,
			Status:      batch.Status,
		})
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}

func (h *Handler) GetPartnerPayoutsForBatch(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	log := ctx.Logger

	var req dto.GetPartnerPayoutsForBatchRequest
	err := ctx.GinCtx.ShouldBindQuery(&req)
	if err != nil {
		log.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(payouts.Payouts_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Error getting payouts client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	payoutsClient := payouts.NewPayoutsClient(conn)

	partnerPayouts, err := payoutsClient.GetPartnerPayoutsForBatch(ctx.GinCtx, &payouts.GetPartnerPayoutsForBatchRequest{
		PayoutBatchId: req.PayoutBatchID,
		Page:          req.Page,
		PageSize:      req.PageSize,
	})

	if err != nil {
		log.Error("Error getting partner payouts for batch", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	res := &dto.GetPartnerPayoutsForBatchResponse{
		Summaries: make([]dto.PartnerPayoutSummary, 0),
	}

	for _, summary := range partnerPayouts.Summaries {
		res.Summaries = append(res.Summaries, dto.PartnerPayoutSummary{
			PartnerID:      *summary.PartnerId,
			PartnerName:    summary.PartnerName,
			EnterpriseName: summary.EnterpriseName,
			TotalCredit:    summary.TotalCredit,
			TotalDebit:     summary.TotalDebit,
			NetCommission:  summary.NetCommission,
			CarryForward:   summary.CarryForward,
		})
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
