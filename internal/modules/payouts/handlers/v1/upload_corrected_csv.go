package v1

import (
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/payouts/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/payouts"
	"go.uber.org/zap"
)

func (h *Handler) UploadCorrectedPayoutCSV(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	log := ctx.Logger

	var req dto.UploadCorrectedPayoutCSVRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		log.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(payouts.Payouts_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Error getting payouts client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	payoutsClient := payouts.NewPayoutsClient(conn)

	resp, err := payoutsClient.UploadCorrectedPayoutCSV(ctx.GinCtx, &payouts.UploadCorrectedPayoutCSVRequest{
		PayoutBatchId: req.PayoutBatchID,
		CsvFile:       req.CSVFile,
	})

	if err != nil {
		log.Error("Error uploading corrected payout CSV", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
