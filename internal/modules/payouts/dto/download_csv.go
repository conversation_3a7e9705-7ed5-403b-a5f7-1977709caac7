package dto

type DownloadPartnerPayoutCSVRequest struct {
	PayoutBatchID string `form:"payout_batch_id" binding:"required"`
}

type DownloadPartnerPayoutCSVResponse struct {
	URL          string  `json:"url"`
	ErrorMessage *string `json:"error_message"`
}

type GetPartnerSummaryCSVRequest struct {
	PayoutBatchID string `form:"payout_batch_id" binding:"required"`
}

type GetPartnerSummaryCSVResponse struct {
	Success      bool    `json:"success"`
	URL          string  `json:"url"`
	ErrorMessage *string `json:"error_message"`
}
