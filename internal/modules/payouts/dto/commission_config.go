package dto

type GetCommissionConfigRequest struct {
	PartnerID    *string `form:"partner_id"`
	EnterpriseID string  `form:"enterprise_id" binding:"required"`
	InsurerID    string  `form:"insurer_id" binding:"required"`
	ProductID    *string `form:"product_id"`
}

type SaveCommissionConfigRequest struct {
	PartnerID          *string                `json:"partner_id"`
	EnterpriseID       string                 `json:"enterprise_id" binding:"required"`
	InsurerID          string                 `json:"insurer_id" binding:"required"`
	ProductID          *string                `json:"product_id"`
	BusinessType       string                 `json:"business_type" binding:"required"`
	CommissionRate     *float64               `json:"commission_rate"`
	AdditionalCriteria map[string]interface{} `json:"additional_criteria"`
}

type CommissionConfig struct {
	ID                 string                 `json:"id"`
	BusinessType       string                 `json:"business_type"`
	CommissionRate     *float64               `json:"commission_rate"`
	AdditionalCriteria map[string]interface{} `json:"additional_criteria"`
}

type GetCommissionConfigResponse struct {
	PartnerID    *string            `json:"partner_id"`
	EnterpriseID string             `json:"enterprise_id"`
	InsurerID    string             `json:"insurer_id"`
	ProductID    *string            `json:"product_id"`
	Configs      []CommissionConfig `json:"configs"`
}

type SaveCommissionConfigResponse struct {
	Success      bool    `json:"success"`
	ErrorMessage *string `json:"error_message,omitempty"`
}
