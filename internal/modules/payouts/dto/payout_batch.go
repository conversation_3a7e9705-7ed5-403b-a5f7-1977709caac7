package dto

type GetPayoutBatchesRequest struct {
	Page      *int32  `form:"page"`
	PageSize  *int32  `form:"page_size"`
	StartDate *string `form:"start_date"`
	EndDate   *string `form:"end_date"`
}

type PayoutBatch struct {
	ID          string `json:"id"`
	StartDate   string `json:"start_date"`
	EndDate     string `json:"end_date"`
	GeneratedAt string `json:"generated_at"`
	Locked      bool   `json:"locked"`
	Status      string `json:"status"`
}

type GetPayoutBatchesResponse struct {
	Batches    []PayoutBatch `json:"batches"`
	TotalPages int32         `json:"total_pages"`
	PageNumber int32         `json:"page_number"`
	TotalCount int32         `json:"total_count"`
}

type GetPartnerPayoutsForBatchRequest struct {
	PayoutBatchID string `form:"payout_batch_id"`
	Page          *int32 `form:"page"`
	PageSize      *int32 `form:"page_size"`
}

type PartnerPayoutSummary struct {
	PartnerID      string  `json:"partner_id"`
	PartnerName    string  `json:"partner_name"`
	EnterpriseName string  `json:"enterprise_name"`
	TotalCredit    float64 `json:"total_credit"`
	TotalDebit     float64 `json:"total_debit"`
	NetCommission  float64 `json:"net_commission"`
	CarryForward   bool    `json:"carry_forward"`
}

type GetPartnerPayoutsForBatchResponse struct {
	Summaries []PartnerPayoutSummary `json:"summaries"`
}
