package payouts

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/payouts/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/payouts/handlers/v1"
)

type PayoutsApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &PayoutsApp{}
}

// SetAppName returns the name of the application
func (app *PayoutsApp) SetAppName() string {
	return "payouts"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *PayoutsApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	apiRouter := appContext.Router[constant.HTTP_API]

	requireAuth := false
	apiRouter.RegisterRoute(ctx, appName, "GET", "/",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPayoutBatches,
				RequireAuth:       &requireAuth,
				QueryStruct:       &dto.GetPayoutBatchesRequest{},
				RequireValidation: false,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{"payouts.get-payout-batches"}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/partner-payouts",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPartnerPayoutsForBatch,
				QueryStruct:       &dto.GetPartnerPayoutsForBatchRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{"payouts.get-partner-payouts-for-batch"}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/partner-summary-csv",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPartnerSummaryCSV,
				QueryStruct:       &dto.GetPartnerSummaryCSVRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{"payouts.get-partner-summary-csv"}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/lock-payout-batch",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.LockPayoutBatch,
				QueryStruct:       &dto.LockPayoutBatchRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{"payouts.lock-payout-batch"}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "PATCH", "/upload-corrected-csv",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.UploadCorrectedPayoutCSV,
				ReqBodyStruct:     &dto.UploadCorrectedPayoutCSVRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{"payouts.upload-corrected-csv"}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/partner-payouts-csv",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.DownloadPartnerPayoutCSV,
				QueryStruct:       &dto.DownloadPartnerPayoutCSVRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{"payouts.get-partner-payouts-csv"}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/commission-config",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetCommissionConfig,
				QueryStruct:       &dto.GetCommissionConfigRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: false,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{"payouts.get-commission-config"}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/commission-config",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.SaveCommissionConfig,
				ReqBodyStruct:     &dto.SaveCommissionConfigRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{"payouts.save-commission-config"}),
				},
			}),
		},
	)
}
