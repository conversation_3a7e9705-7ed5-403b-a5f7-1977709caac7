package forms

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/forms/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/forms/handlers/v1"
)

type FormsApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &FormsApp{}
}

// SetAppName returns the name of the application
func (app *FormsApp) SetAppName() string {
	return "forms"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *FormsApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	requireAuth := false

	// Register HTTP routes
	apiRouter := appContext.Router[constant.HTTP_API]

	apiRouter.RegisterRoute(ctx, appName, "GET", "/:form_id/schema",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetFormSchema,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/submission",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.SubmitFormResponse,
				ReqBodyStruct:     &dto.SubmitFormRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"forms.create",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/submission",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.FetchFormResponse,
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"forms.get-form-response",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "validate/ifsc",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.VerifyBankDetails,
				ReqBodyStruct: &dto.VerifyBankDetailsRequest{},
				RequireAuth:   &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"forms.create",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "validate/pincode",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.VerifyPinCode,
				ReqBodyStruct: &dto.VerifyPinCodeRequest{},
				RequireAuth:   &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "enterprise_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"forms.create", "recommendation.quotes.create",
					}),
				},
			}),
		},
	)
}
