package v1

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/forms/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-forms/v0/forms"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
)

func (h *Handler) GetFormSchema(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	formID := ctx.GetParam("form_id")

	conn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting forms client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	client := forms.NewFormsServiceClient(conn)

	schema, err := client.GetFormSchema(grpcCtx.(context.Context), &forms.GetFormSchemaRequest{
		FormId: formID,
	})

	if err != nil {
		ctx.Logger.Error("Error getting form schema", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	formSchema := &dto.GetFormSchemaResponse{
		FormID:     schema.GetFormId(),
		FormSchema: json.RawMessage(protojson.Format(schema.GetFormSchema())),
		FormType:   dto.FormTypeNames[schema.GetFormType().String()],
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: formSchema,
	}, nil
}

func (h *Handler) FetchFormResponse(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	// Get include file tokens from query parameter
	includeFileTokens := false
	if ctx.GinCtx.Query("file_tokens") != "" {
		includeFileTokens = true
	}

	// Get submission ID from URL parameter
	submissionIDParam := ctx.GetQuery("submission_id")

	// Try to get context and metadata from token
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	// Try to get submission ID from token first
	submissionID := submissionIDParam
	submissionIDs := md.Get("submission_id")
	if len(submissionIDs) > 0 {
		submissionID = submissionIDs[0]
	}

	conn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting forms client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := forms.NewFormsServiceClient(conn)

	submission, err := client.FetchFormResponse(grpcCtx.(context.Context), &forms.FetchFormResponseRequest{
		SubmissionId:      submissionID,
		IncludeFormSchema: true,
		IncludeFileTokens: includeFileTokens,
	})

	if err != nil {
		ctx.Logger.Error("Error getting submission", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	responseData := make([]dto.ResponseData, len(submission.GetResponseData()))
	for i, data := range submission.GetResponseData() {
		responseData[i] = dto.ResponseData{
			SectionName:  data.GetSectionName(),
			ResponseData: data.GetResponseData().AsMap(),
		}
	}

	resp := &dto.GetFormResponseDataResponse{
		FormID:       submission.GetFormId(),
		SubmissionID: submission.GetSubmissionId(),
		Status:       dto.FormStatusNames[submission.GetStatus().String()],
		ResponseData: responseData,
	}

	if submission.GetStatus() == forms.FormStatus_DRAFT {
		resp.FormSchema = json.RawMessage(protojson.Format(submission.GetFormSchema()))
		resp.FormType = dto.FormTypeNames[submission.GetFormType().String()]
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func (h *Handler) SubmitFormResponse(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Get submission ID from URL parameter
	submissionIDParam := ctx.GetQuery("submission_id")

	var req dto.SubmitFormRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Try to get context and metadata from token
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	// Try to get submission ID from token first
	submissionID := submissionIDParam
	submissionIDs := md.Get("submission_id")
	if len(submissionIDs) > 0 {
		submissionID = submissionIDs[0]
	}

	conn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting forms client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := forms.NewFormsServiceClient(conn)

	pbStruct := make([]*forms.FormResponseData, len(req.ResponseDatas))
	for i, data := range req.ResponseDatas {
		responseData, err := structpb.NewStruct(data.ResponseData)
		if err != nil {
			ctx.Logger.Error("Error converting response data to protobuf struct", zap.Error(err))
			return nil, response.NewInternalServerError()
		}

		pbStruct[i] = &forms.FormResponseData{
			SectionName:  data.SectionName,
			ResponseData: responseData,
		}
	}

	_, err = client.SubmitFormResponse(grpcCtx.(context.Context), &forms.SubmitFormResponseRequest{
		SubmissionId: submissionID,
		ResponseData: pbStruct,
	})

	if err != nil {
		ctx.Logger.Error("Error submitting form response", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
	}, nil
}
