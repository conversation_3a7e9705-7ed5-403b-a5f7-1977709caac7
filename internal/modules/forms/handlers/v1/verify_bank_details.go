package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/forms/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	catalog_metadata "github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/metadata"
	"go.uber.org/zap"
)

func (h *Handler) VerifyBankDetails(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.VerifyBankDetailsRequest

	err := ctx.GinCtx.ShouldBindJSON(&req)
	if err != nil {
		ctx.Logger.Error("Error binding uri", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(catalog_metadata.MetadataService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting catalog client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := catalog_metadata.NewMetadataServiceClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	bankDetails, err := client.GetBankByIFSC(grpcCtx.(context.Context), &catalog_metadata.GetBankByIFSCRequest{
		Ifsc: req.Ifsc,
	})

	if err != nil {
		ctx.Logger.Error("Error getting bank details from catalog service", zap.Error(err))
		return &response.SuccessResponse{
			Status: http.StatusOK,
			Payload: &dto.VerifyBankDetailsResponse{
				Valid:    false,
				Metadata: map[string]interface{}{},
			},
		}, nil
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.VerifyBankDetailsResponse{
			Valid: true,
			Metadata: map[string]interface{}{
				"branch": bankDetails.BranchName,
				"ifsc":   bankDetails.Ifsc,
				"city":   bankDetails.City,
				"state":  bankDetails.State,
			},
		},
	}, nil
}
