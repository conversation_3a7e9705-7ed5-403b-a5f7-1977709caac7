package dto

import "encoding/json"

type GetFormSchemaRequest struct {
	FormID string `json:"form_id" validate:"required"`
}

var FormTypeNames = map[string]string{
	"RJSF":   "RJSF",
	"CUSTOM": "CUSTOM",
}

var FormStatusNames = map[string]string{
	"DRAFT":     "DRAFT",
	"SUBMITTED": "SUBMITTED",
}

type GetFormSchemaResponse struct {
	FormID     string          `json:"form_id"`
	FormSchema json.RawMessage `json:"form_schema"`
	UiSchema   json.RawMessage `json:"ui_schema"`
	FormType   string          `json:"form_type"`
}

type SubmitFormRequest struct {
	ResponseDatas []ResponseData `json:"response_datas" validate:"required"`
}

type ResponseData struct {
	SectionName  string                 `json:"section_name"`
	ResponseData map[string]interface{} `json:"response_data"`
}

type SubmitFormResponse struct {
}

type GetFormResponseDataResponse struct {
	FormID       string          `json:"form_id"`
	SubmissionID string          `json:"submission_id"`
	Status       string          `json:"status"`
	ResponseData []ResponseData  `json:"response_data"`
	FormSchema   json.RawMessage `json:"form_schema"`
	UiSchema     json.RawMessage `json:"ui_schema"`
	FormType     string          `json:"form_type"`
}
