package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"go.uber.org/zap"
)

func (h *Handler) CreateClient(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.CreateClientRequest

	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := auth.NewAuthClient(conn)

	var authScopes []auth.Scope

	for _, scope := range req.Scopes {
		authScopes = append(authScopes, getScope(scope))
	}

	tokenResponse, err := client.CreateClient(context.Background(), &auth.CreateClientRequest{
		EnterpriseId:  req.EnterpriseID,
		AllowedScopes: authScopes,
	})

	if err != nil {
		ctx.Logger.Error("Error creating client Id", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	var scopes []string

	for _, scope := range tokenResponse.GetAllowedScopes() {
		scopes = append(scopes, scope.String())
	}

	resp := &dto.CreateClientResponse{
		EnterpriseID: tokenResponse.GetEnterpriseId(),
		ClientID:     tokenResponse.GetClientId(),
		ClientSecret: tokenResponse.GetClientSecret(),
		IsEnabled:    tokenResponse.GetIsEnabled(),
		Scopes:       scopes,
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func getScope(scope string) auth.Scope {
	switch scope {
	case "embedded":
		return auth.Scope_EMBEDDED
	case "embedded.proposal.create":
		return auth.Scope_EMBEDDED_PROPOSAL_CREATE
	case "embedded.proposal.update":
		return auth.Scope_EMBEDDED_PROPOSAL_UPDATE
	case "embedded.proposal.status.read":
		return auth.Scope_EMBEDDED_PROPOSAL_STATUS_READ
	case "embedded.quote.create":
		return auth.Scope_EMBEDDED_QUOTE_CREATE
	default:
		return auth.Scope_SCOPE_UNSPECIFIED
	}
}
