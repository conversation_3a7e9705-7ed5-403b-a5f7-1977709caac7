package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-enterprise/v0/enterprise"
	"go.uber.org/zap"
)

func (h *Handler) GetPartnersBySearch(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(enterprise.Enterprise_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := enterprise.NewEnterpriseClient(conn)

	searchQuery := ctx.GetQuery("search")
	getSeperateEnterprises := ctx.GetQuery("get_seperate_enterprises") == "true"
	user, err := client.GetPartnersBySearch(grpcCtx.(context.Context), &enterprise.GetPartnersBySearchRequest{
		Search: searchQuery,
	})

	if err != nil {
		ctx.Logger.Error("Error getting partners by search", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	var partners []dto.PartnerDetails
	seenEnterprises := make(map[string]bool)

	for _, partner := range user.Partners {
		// Add partner entry
		partners = append(partners, dto.PartnerDetails{
			Id:           partner.Id,
			Name:         partner.FirstName + " " + partner.LastName + " (" + partner.EnterpriseDetails.BrandName + ")",
			EnterpriseId: partner.EnterpriseDetails.Id,
		})
		if getSeperateEnterprises {
			// Add enterprise entry if not already added
			if !seenEnterprises[partner.EnterpriseDetails.BrandName] {
				partners = append(partners, dto.PartnerDetails{
					Id:           "9999999999999999",
					Name:         partner.EnterpriseDetails.BrandName,
					EnterpriseId: partner.EnterpriseDetails.Id,
				})
				seenEnterprises[partner.EnterpriseDetails.BrandName] = true
			}
		}
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: dto.GetPartnersBySearchResponse{
			Partners: partners,
		},
	}, nil
}
