package v1

import (
	"context"
	"net/http"
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"go.uber.org/zap"
)

func (h *Handler) CreateToken(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.CreateTokenRequest

	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := auth.NewAuthClient(conn)

	scopes := []auth.Scope{}

	for _, scope := range req.Scopes {
		s := auth.Scope(auth.Scope_value[strings.ReplaceAll(strings.ToUpper(scope), ".", "_")])

		if s == auth.Scope_SCOPE_UNSPECIFIED {
			continue
		}

		scopes = append(scopes, s)
	}

	tokenResponse, err := client.CreateToken(context.Background(), &auth.CreateTokenRequest{
		ClientId:     req.ClientId,
		ClientSecret: req.ClientSecret,
		Scopes:       scopes,
	})

	if err != nil {
		ctx.Logger.Error("Error creating token", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	resp := &dto.CreateTokenResponse{
		AccessToken: tokenResponse.GetToken(),
		ExpiresIn:   int(tokenResponse.GetExpiresIn()),
		TokenType:   "Bearer",
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
