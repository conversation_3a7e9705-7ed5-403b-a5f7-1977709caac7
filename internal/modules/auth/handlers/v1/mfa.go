package v1

import (
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"go.uber.org/zap"
)

func (h *Handler) VerifyMfa(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.MfaRequest

	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := auth.NewAuthClient(conn)

	tokenResponse, err := client.VerifyTwoFactor(context.Background(), &auth.VerifyTwoFactorRequest{
		Identifier: req.Identifier,
		Code:       req.Code,
		Method:     auth.MfaType(auth.MfaType_value[strings.ToUpper(req.Method)]),
		MfaToken:   req.MfaToken,
		Entity:     auth.Entity_PARTNER,
	})

	if err != nil {
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	accessTokenExpiresInMs := time.Duration(15 * time.Minute).Milliseconds()
	refreshTokenExpiresInMs := time.Duration(30 * 24 * time.Hour).Milliseconds()

	resp := &dto.MfaResponse{
		AccessToken:             tokenResponse.GetAccessToken(),
		AccessTokenExpiresInMs:  int(accessTokenExpiresInMs),
		RefreshToken:            tokenResponse.GetRefreshToken(),
		RefreshTokenExpiresInMs: int(refreshTokenExpiresInMs),
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
