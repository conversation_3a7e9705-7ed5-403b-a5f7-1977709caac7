package v1

import (
	"context"
	"net/http"
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"go.uber.org/zap"
)

func (h *<PERSON><PERSON>) Login(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	var req dto.LoginRequest

	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := auth.NewAuthClient(conn)

	// Determine if the identifier is an email or phone number
	var email string
	var phone string
	if strings.Contains(req.Identifier, "@") {
		email = req.Identifier
	} else {
		phone = req.Identifier
	}

	optedMfa := []auth.MfaType{}
	availableMfas := []auth.MfaType{}

	var mfaToken string

	if email != "" {
		response, err := client.LoginWithEmail(context.Background(), &auth.LoginWithEmailRequest{
			Email: email,
			// Password: req.Password,
		})

		if err != nil {
			// Implement conditional logging over here
			return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
		}

		// _, err = client.InitiateHeadlessMfa(grpcCtx, &auth.InitiateHeadlessMfaRequest{
		// 	Identifier: "policy_verify",
		// 	Method:     auth.MfaType_TEXT_OTP,
		// 	ExpiresAt:  timestamppb.New(time.Now().Add(time.Duration(15 * time.Minute))),
		// })

		// if err != nil {
		// 	return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
		// }

		mfaToken = response.GetMfaToken()
		optedMfa = append(optedMfa, response.GetOptedMfa())
		availableMfas = append(availableMfas, response.GetAvailableMfas()...)
	} else if phone != "" {

		tokenResponse, err := client.LoginWithPhone(context.Background(), &auth.LoginWithPhoneRequest{
			Phone: phone,
		})

		if err != nil {
			return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
		}

		mfaToken = tokenResponse.GetMfaToken()
		optedMfa = append(optedMfa, tokenResponse.GetOptedMfa())
		availableMfas = append(availableMfas, tokenResponse.GetAvailableMfas()...)
	}

	var optedMfasResponse []string
	var availableMfasResponse []string

	// Convert the mfa types to strings
	for _, mfa := range optedMfa {
		optedMfasResponse = append(optedMfasResponse, mfa.String())
	}

	for _, mfa := range availableMfas {
		availableMfasResponse = append(availableMfasResponse, mfa.String())
	}

	resp := &dto.LoginResponse{
		OptedMfas:     optedMfasResponse,
		AvailableMfas: availableMfasResponse,
		MfaToken:      mfaToken,
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
