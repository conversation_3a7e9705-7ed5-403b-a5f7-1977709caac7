package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-enterprise/v0/enterprise"
	"go.uber.org/zap"
)

func (h *Handler) Me(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(enterprise.Enterprise_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := enterprise.NewEnterpriseClient(conn)

	userId, ok := ctx.GinCtx.Get("sub")
	if !ok {
		ctx.Logger.Error("User ID not found in context")
		return nil, response.NewInternalServerError()
	}

	user, err := client.GetMe(grpcCtx.(context.Context), &enterprise.GetMeRequest{
		UserId: userId.(string),
	})

	if err != nil {
		ctx.Logger.Error("Error getting me", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: dto.MeResponse{
			Id:             user.UserId,
			Name:           user.Name,
			Email:          user.Email,
			Role:           ctx.GinCtx.GetString("role"),
			EnterpriseType: ctx.GinCtx.GetString("enterprise_type"),
		},
	}, nil
}
