package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"go.uber.org/zap"

	"google.golang.org/grpc/metadata"
)

func (h *Handler) Logout(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Get the gRPC metadata context from gin context
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Extract the metadata to get the token
	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	// Get token from metadata
	tokenValues := md.Get("token")
	if len(tokenValues) == 0 {
		ctx.Logger.Error("Token not found in metadata")
		return nil, response.NewInternalServerError()
	}
	token := tokenValues[0]

	// Continue with the existing enterprise_id metadata
	metadataObj := metadata.New(map[string]string{
		"enterprise_id": ctx.GinCtx.GetString("enterprise_id"),
	})

	grpcRequestCtx := metadata.NewOutgoingContext(context.TODO(), metadataObj)

	conn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := auth.NewAuthClient(conn)

	resp, err := client.RevokeJWTToken(grpcRequestCtx, &auth.RevokeJWTTokenRequest{
		Token: token,
	})

	if err != nil {
		ctx.Logger.Error("Failed to revoke JWT token", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolHTTP, err)
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}
