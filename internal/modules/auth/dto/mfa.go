package dto

// CreateTokenRequest represents the request structure for creating a token
// This matches the fields needed to create a proper gRPC request
type MfaRequest struct {
	Identifier string `json:"identifier" validate:"required"`
	// Password   string `json:"password"`
	MfaToken string `json:"mfa_token" validate:"required"`
	Code     string `json:"code" validate:"required"`
	Method   string `json:"method" validate:"required,oneof=text_otp email_otp authenticator whatsapp_otp"`
}

type MfaResponse struct {
	AccessToken             string `json:"access_token"`
	AccessTokenExpiresInMs  int    `json:"access_token_expires_in_ms"`
	RefreshToken            string `json:"refresh_token"`
	RefreshTokenExpiresInMs int    `json:"refresh_token_expires_in_ms"`
}
