package dto

// CreateTokenRequest represents the request structure for creating a token
// This matches the fields needed to create a proper gRPC request
type CreateTokenRequest struct {
	ClientId     string   `json:"client_id" validate:"required"`
	ClientSecret string   `json:"client_secret" validate:"required"`
	Scopes       []string `json:"scopes" validate:"required"`
}

type CreateTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	TokenType   string `json:"token_type"`
}
