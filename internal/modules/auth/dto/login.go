package dto

// CreateTokenRequest represents the request structure for creating a token
// This matches the fields needed to create a proper gRPC request
type LoginRequest struct {
	Identifier string `json:"identifier" validate:"required"`
	Password   string `json:"password"`
}

type LoginResponse struct {
	OptedMfas     []string `json:"opted_mfas"`
	AvailableMfas []string `json:"available_mfas"`
	MfaToken      string   `json:"mfa_token"`
}

// type LoginResponse struct {
// 	AccessToken         string `json:"access_token"`
// 	AccessTokenExpires  int    `json:"access_token_expires"`
// 	RefreshToken        string `json:"refresh_token"`
// 	RefreshTokenExpires int    `json:"refresh_token_expires"`
// }
