package dto

type PartnerDetails struct {
	Id           string `json:"id"`
	Name         string `json:"name"`
	EnterpriseId string `json:"enterprise_id"`
}

type GetPartnersBySearchRequest struct {
	Search                 string `json:"search" validate:"required"`
	GetSeperateEnterprises bool   `json:"get_seperate_enterprises"`
}

type GetPartnersBySearchResponse struct {
	Partners []PartnerDetails `json:"partners"`
}
