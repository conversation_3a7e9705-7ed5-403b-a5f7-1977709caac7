package auth

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth/dto"
	v0 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth/handlers/v0"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth/handlers/v1"
)

type AuthApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &AuthApp{}
}

// SetAppName returns the name of the application
func (app *AuthApp) SetAppName() string {
	return "auth"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *AuthApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	h0 := &v0.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	// Register HTTP routes
	adminRouter := appContext.Router[constant.HTTP_ADMIN]
	apiRouter := appContext.Router[constant.HTTP_API]

	requireAuth := false

	// Attach the routes with validation
	apiRouter.RegisterRoute(ctx, appName, "POST", "/token",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				RequireValidation: true,
				Handler:           h1.CreateToken,
				ReqBodyStruct:     &dto.CreateTokenRequest{},
				RequireAuth:       &requireAuth,
			}),
		},
	)

	adminRouter.RegisterRoute(ctx, appName, "POST", "/client",
		map[uint8]*handler.OptionStruct{
			0: h0.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				RequireValidation: true,
				Handler:           h0.CreateClient,
				ReqBodyStruct:     &dto.CreateClientRequest{},
				RequireAuth:       &requireAuth,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/login",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				RequireValidation: true,
				Handler:           h1.Login,
				ReqBodyStruct:     &dto.LoginRequest{},
				RequireAuth:       &requireAuth,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/mfa/verify",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				RequireValidation: true,
				Handler:           h1.VerifyMfa,
				ReqBodyStruct:     &dto.MfaRequest{},
				RequireAuth:       &requireAuth,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/me",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				RequireValidation: false,
				Handler:           h1.Me,
				AuthMiddleware:    []func(c *gin.Context) bool{middleware.CookieAuthMiddleware([]string{"sub"}, "gateway_portal", []string{})},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "DELETE", "/logout",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				RequireValidation: false,
				Handler:           h1.Logout,
				AuthMiddleware:    []func(c *gin.Context) bool{middleware.CookieAuthMiddleware([]string{}, "gateway_portal", []string{})},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/partners/search",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				RequireValidation: false,
				Handler:           h1.GetPartnersBySearch,
				AuthMiddleware:    []func(c *gin.Context) bool{middleware.CookieAuthMiddleware([]string{"sub"}, "gateway_portal", []string{})},
			}),
		},
	)

	// publicRouter.RegisterRoute(ctx, appName, "POST", "/mfa/resend",
	// 	map[uint8]*handler.OptionStruct{
	// 		1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
	// 			RequireValidation: true,
	// 			Handler:           h1.ResendMfa,
	// 		}),
	// 	},
	// )
}
