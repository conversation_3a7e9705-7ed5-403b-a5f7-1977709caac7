package business

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/business/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/business/handlers/v1"
)

type BusinessApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &BusinessApp{}
}

// SetAppName returns the name of the application
func (app *BusinessApp) SetAppName() string {
	return "business"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *BusinessApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	apiRouter := appContext.Router[constant.HTTP_API]

	requireAuth := true
	apiRouter.RegisterRoute(ctx, appName, "GET", "/health/policies",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetHealthPolicies,
				QueryStruct:       &dto.GetHealthPoliciesRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{}, "gateway_api"),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/health/quotes",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetHealthQuotes,
				QueryStruct:       &dto.GetQuotesRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{}, "gateway_api"),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/health/proposals",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetHealthProposals,
				QueryStruct:       &dto.GetHealthProposalsRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{}, "gateway_api"),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/health/quotes/:quote_id",

		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetHealthQuoteById,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{}, "gateway_api"),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/health/proposals/:proposal_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetHealthProposalById,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{}, "gateway_api"),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/health/policies/:policy_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetHealthPolicyByID,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{}, "gateway_api"),
				},
			}),
		},
	)
}
