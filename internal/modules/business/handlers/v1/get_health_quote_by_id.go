package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/business/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/health"
	recommendation "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
	"go.uber.org/zap"
)

func (h *Handler) GetHealthQuoteById(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	recommendationClient := recommendation.NewRecommendationClient(conn)

	conn, err = h.GrpcClients.Get(health.HealthService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting health client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	healthClient := health.NewHealthServiceClient(conn)

	// Get enterprise_id from token context
	// enterpriseID := ctx.GinCtx.GetString("enterprise_id")
	// if enterpriseID == "" {
	// 	ctx.Logger.Error("Enterprise ID missing from context")
	// 	return nil, &response.ErrorResponse{
	// 		Status: &response.Status{
	// 			HttpStatus: http.StatusUnauthorized,
	// 		},
	// 		Problem: &response.Problem{
	// 			Type:   oa_errors.ErrInvalidInput.GetCode(),
	// 			Title:  oa_errors.ErrInvalidInput.GetMessage(),
	// 			Detail: "Enterprise ID is required and missing from the request context.",
	// 		},
	// 	}
	// }

	// // Create new gRPC context with the metadata
	// gCtx := metadata.AppendToOutgoingContext(context.Background(),
	// 	"enterprise_id", "18TzlaIOrzcGFHY7",
	// 	"partner_id", "18lB4jjKyhOETWXw",
	// )

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	quoteID := ctx.GinCtx.Param("quote_id")

	grpcReq := &recommendation.GetQuoteByIdRequest{
		QuoteId: quoteID,
	}

	quoteRes, err := recommendationClient.GetQuoteById(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetQuotes gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	catalogRes, err := healthClient.GetAllProducts(grpcCtx.(context.Context), &health.GetAllProductsRequest{})
	if err != nil {
		ctx.Logger.Error("Error calling GetAllProducts gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	productMap := make(map[string]*health.Product)
	for _, product := range catalogRes.Product {
		productMap[product.Id] = product
	}

	var quoteResponse dto.QuoteDefForExport

	var memberDetails []dto.MemberDetail
	for _, member := range quoteRes.Quote.MemberDetails {
		memberDetails = append(memberDetails, dto.MemberDetail{
			Relation: member.Relation,
			Age:      member.Age,
		})
	}

	var recommendations []dto.RecommendationDefForExport
	for _, recommendation := range quoteRes.Quote.Recommendations {
		// Find matching product in catalog response
		matchingProduct, ok := productMap[recommendation.ProductId]
		if !ok {
			ctx.Logger.Error("Product not found in catalog", zap.String("product_id", recommendation.ProductId))
			continue
		}

		var companyName, companyLogo, productName, variantName string
		var networkHospitalCount, claimSettlementRatio int32

		if matchingProduct != nil {
			companyName = matchingProduct.Insurer.Name
			companyLogo = matchingProduct.Insurer.Logo
			productName = matchingProduct.Name
			networkHospitalCount = matchingProduct.Insurer.NetworkHospitalCount
			claimSettlementRatio = matchingProduct.Insurer.ClaimSettlementRatio

			// Find matching variant
			for _, variant := range matchingProduct.Variants {
				if variant.Id == recommendation.VariantId {
					variantName = variant.Name
					break
				}
			}
		}
		recommendations = append(recommendations, dto.RecommendationDefForExport{
			RecommendationID: recommendation.RecommendationId,
			CompanyID:        recommendation.CompanyId,
			ProductID:        recommendation.ProductId,
			VariantID:        recommendation.VariantId,
			PremiumDetails: []dto.PremiumDetailDefForExport{
				{
					Tenure:  1,
					Premium: recommendation.PremiumDetails[0].Premium,
				},
				{
					Tenure:  2,
					Premium: recommendation.PremiumDetails[1].Premium,
				},
				{
					Tenure:  3,
					Premium: recommendation.PremiumDetails[2].Premium,
				},
			},
			Recommended:                 recommendation.Recommended,
			CompanyName:                 companyName,
			CompanyLogo:                 companyLogo,
			ProductName:                 productName,
			VariantName:                 variantName,
			CompanyNetworkHospitalCount: networkHospitalCount,
			CompanyClaimSettlementRatio: claimSettlementRatio,
		})
	}

	quoteDef := dto.QuoteDefForExport{
		SessionID:         "",
		QuoteID:           quoteRes.Quote.QuoteId,
		SumInsured:        quoteRes.Quote.SumInsured,
		Pincode:           quoteRes.Quote.Pincode,
		Deductible:        quoteRes.Quote.Deductible,
		TotalVariants:     quoteRes.Quote.TotalVariants,
		CompletedVariants: quoteRes.Quote.CompletedVariants,
		MemberDetails:     memberDetails,
		PlanType:          quoteRes.Quote.PlanType.String(),
		CustomerName:      quoteRes.Quote.CustomerName,
		CustomerPhone:     quoteRes.Quote.CustomerPhone,
		CustomerGender:    quoteRes.Quote.CustomerGender,
		CustomerEmail:     quoteRes.Quote.CustomerEmail,
		Recommendations:   recommendations,
	}

	quoteResponse = quoteDef

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetQuoteByIdResponse{
			Quote: quoteResponse,
		},
	}, nil
}
