package v1

import (
	"context"
	"net/http"
	"net/url"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/business/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
)

func (h *Handler) GetHealthPolicyByID(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	// Get enterprise_id from token context
	// enterpriseID := ctx.GinCtx.GetString("enterprise_id")
	// if enterpriseID == "" {
	// 	ctx.Logger.Error("Enterprise ID missing from context")
	// 	return nil, &response.ErrorResponse{
	// 		Status: &response.Status{
	// 			HttpStatus: http.StatusUnauthorized,
	// 		},
	// 		Problem: &response.Problem{
	// 			Type:   oa_errors.ErrInvalidInput.GetCode(),
	// 			Title:  oa_errors.ErrInvalidInput.GetMessage(),
	// 			Detail: "Enterprise ID is required and missing from the request context.",
	// 		},
	// 	}
	// }

	// Create new gRPC context with the metadata
	// gCtx := metadata.AppendToOutgoingContext(context.Background(),
	// 	"enterprise_id", "18TzlaIOrzcGFHY7",
	// )
	// gCtx := metadata.AppendToOutgoingContext(context.Background(),
	// 	"enterprise_id", enterpriseID,
	// )

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	policyID := ctx.GinCtx.Param("policy_id")

	// Decode url encoded policy id
	decodedPolicyID, err := url.QueryUnescape(policyID)
	if err != nil {
		ctx.Logger.Error("Error decoding policy ID", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcReq := &policies.GetPolicyByIdRequest{
		PolicyId: decodedPolicyID,
	}

	res, err := client.GetPolicyById(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetPolicies gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	var policyResponse dto.PolicyDefForExport

	policy := res.Policy

	var proposerDetails dto.ProposerDetails
	if policy.ProposerDetails != nil {
		proposerDetails = dto.ProposerDetails{
			AddressLine1:      policy.ProposerDetails.Address.AddressLine_1,
			AddressLine2:      policy.ProposerDetails.Address.AddressLine_2,
			City:              policy.ProposerDetails.Address.City,
			State:             policy.ProposerDetails.Address.State,
			Country:           policy.ProposerDetails.Address.Country,
			Pincode:           policy.ProposerDetails.Address.Pincode,
			DOB:               policy.ProposerDetails.Dob,
			Email:             policy.ProposerDetails.Email,
			Name:              policy.ProposerDetails.Name,
			Phone:             policy.ProposerDetails.Phone,
			BankID:            policy.ProposerDetails.BankDetails.BankId,
			BankAccountNumber: policy.ProposerDetails.BankDetails.BankAccountNumber,
			BankName:          policy.ProposerDetails.BankDetails.BankName,
			BranchID:          policy.ProposerDetails.BankDetails.BranchId,
			BranchName:        policy.ProposerDetails.BankDetails.BranchName,
			IFSCCode:          policy.ProposerDetails.BankDetails.IfscCode,
		}
	}

	insuredMembers := []dto.InsuredMember{}
	if policy.InsuredMembers != nil {
		for _, member := range policy.InsuredMembers {
			if member != nil {
				insuredMembers = append(insuredMembers, dto.InsuredMember{
					MemberID:            member.MemberId,
					DOB:                 member.Dob,
					Gender:              member.Gender,
					Name:                member.Name,
					PreExistingDiseases: &member.PreExistingDiseases,
					Relationship:        member.Relationship,
				})
			}
		}
	}

	riders := []dto.Rider{}
	if policy.Riders != nil {
		for _, rider := range policy.Riders {
			if rider != nil {
				riders = append(riders, dto.Rider{
					ID:   rider.Id,
					Name: rider.Name,
				})
			}
		}
	}

	nominees := []dto.Nominee{}
	if policy.Nominees != nil {
		for _, nominee := range policy.Nominees {
			if nominee != nil {
				nominees = append(nominees, dto.Nominee{
					NomineeID:    nominee.NomineeId,
					NomineeName:  nominee.NomineeName,
					NomineePhone: &nominee.NomineePhone,
					Relationship: nominee.Relationship,
				})
			}
		}
	}

	policyResponse = dto.PolicyDefForExport{
		SessionID:           policy.SessionId,
		PolicyID:            policy.PolicyId,
		InsurerPolicyNumber: policy.InsurerPolicyNumber,
		Status:              policy.Status.String(),
		IssuanceDate:        policy.IssuanceDate,
		ExpiryDate:          policy.ExpiryDate,
		SumInsured:          policy.SumInsured,
		PolicyType:          policy.PolicyType.String(),
		Tenure:              policy.Tenure,
		GrossPremium:        policy.GrossPremium,
		InsurerID:           policy.InsurerId,
		InsurerName:         policy.InsurerName,
		InsurerLogo:         policy.InsurerLogo,
		ProductID:           policy.ProductId,
		ProductName:         policy.ProductName,
		VariantID:           policy.VariantId,
		VariantName:         policy.VariantName,
		PreviousPolicyID:    policy.PreviousPolicyId,
		ProposerDetails:     proposerDetails,
		InsuredMembers:      insuredMembers,
		Nominees:            nominees,
		Riders:              riders,
		ProposalID:          policy.ProposalId,
		QuoteID:             policy.QuoteId,
		PolicyURL:           &policy.PolicyUrl,
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetPolicyByIdResponse{
			Policy: policyResponse,
		},
	}, nil
}
