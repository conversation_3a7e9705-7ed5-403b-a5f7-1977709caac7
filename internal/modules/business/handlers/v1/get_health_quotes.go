package v1

import (
	"context"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/business/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/health"
	recommendation "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
	oa_errors "github.com/oneassure-tech/oa-utilities/go/oa-errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (h *Handler) GetHealthQuotes(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(recommendation.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	recommendationClient := recommendation.NewRecommendationClient(conn)

	conn, err = h.GrpcClients.Get(health.HealthService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting health client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	healthClient := health.NewHealthServiceClient(conn)

	// Get enterprise_id from token context
	// enterpriseID := ctx.GinCtx.GetString("enterprise_id")
	// if enterpriseID == "" {
	// 	ctx.Logger.Error("Enterprise ID missing from context")
	// 	return nil, &response.ErrorResponse{
	// 		Status: &response.Status{
	// 			HttpStatus: http.StatusUnauthorized,
	// 		},
	// 		Problem: &response.Problem{
	// 			Type:   oa_errors.ErrInvalidInput.GetCode(),
	// 			Title:  oa_errors.ErrInvalidInput.GetMessage(),
	// 			Detail: "Enterprise ID is required and missing from the request context.",
	// 		},
	// 	}
	// }

	// Check if the start_date and end_date are within a range of 7 days if the difference is more return error
	startDate, err := time.Parse("2006-01-02", ctx.GinCtx.Query("start_date"))
	if err != nil {
		ctx.Logger.Error("Error parsing start date", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	endDate, err := time.Parse("2006-01-02", ctx.GinCtx.Query("end_date"))
	if err != nil {
		ctx.Logger.Error("Error parsing end date", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	if endDate.Sub(startDate) > 7*24*time.Hour {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Type:   oa_errors.ErrInvalidInput.GetCode(),
				Title:  oa_errors.ErrInvalidInput.GetMessage(),
				Detail: "Start date and end date must be within 7 days",
			},
		}
	}

	// // Create new gRPC context with the metadata
	// gCtx := metadata.AppendToOutgoingContext(context.Background(),
	// 	"enterprise_id", "18TzlaIOrzcGFHY7",
	// 	"partner_id", "18lB4jjKyhOETWXw",
	// )

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	next := ctx.GinCtx.Query("next")
	prev := ctx.GinCtx.Query("prev")

	var nextPtr *string
	var prevPtr *string

	if next == "" {
		nextPtr = nil
	}

	if prev == "" {
		prevPtr = nil
	}

	grpcReq := &recommendation.GetQuotesRequest{
		StartDate: timestamppb.New(startDate),
		EndDate:   timestamppb.New(endDate),
		Next:      nextPtr,
		Prev:      prevPtr,
	}

	quoteRes, err := recommendationClient.GetQuotes(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetQuotes gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// fmt.Println("quoteRes", quoteRes.Quotes)

	catalogRes, err := healthClient.GetAllProducts(grpcCtx.(context.Context), &health.GetAllProductsRequest{})
	if err != nil {
		ctx.Logger.Error("Error calling GetAllProducts gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	productMap := make(map[string]*health.Product)
	for _, product := range catalogRes.Product {
		productMap[product.Id] = product
	}

	var quotesResponse []dto.QuoteDefForExport

	for _, quote := range quoteRes.Quotes {
		var memberDetails []dto.MemberDetail
		for _, member := range quote.MemberDetails {
			memberDetails = append(memberDetails, dto.MemberDetail{
				Relation: member.Relation,
				Age:      member.Age,
			})
		}

		var recommendations []dto.RecommendationDefForExport
		for _, recommendation := range quote.Recommendations {
			// Find matching product in catalog response
			matchingProduct, ok := productMap[recommendation.ProductId]
			if !ok {
				ctx.Logger.Error("Product not found in catalog", zap.String("product_id", recommendation.ProductId))
				continue
			}

			var companyName, companyLogo, productName, variantName string
			var networkHospitalCount, claimSettlementRatio int32

			if matchingProduct != nil {
				companyName = matchingProduct.Insurer.Name
				companyLogo = matchingProduct.Insurer.Logo
				productName = matchingProduct.Name
				networkHospitalCount = matchingProduct.Insurer.NetworkHospitalCount
				claimSettlementRatio = matchingProduct.Insurer.ClaimSettlementRatio

				// Find matching variant
				for _, variant := range matchingProduct.Variants {
					if variant.Id == recommendation.VariantId {
						variantName = variant.Name
						break
					}
				}
			}
			recommendations = append(recommendations, dto.RecommendationDefForExport{
				RecommendationID: recommendation.RecommendationId,
				CompanyID:        recommendation.CompanyId,
				ProductID:        recommendation.ProductId,
				VariantID:        recommendation.VariantId,
				PremiumDetails: []dto.PremiumDetailDefForExport{
					{
						Tenure:  1,
						Premium: recommendation.PremiumDetails[0].Premium,
					},
					{
						Tenure:  2,
						Premium: recommendation.PremiumDetails[1].Premium,
					},
					{
						Tenure:  3,
						Premium: recommendation.PremiumDetails[2].Premium,
					},
				},
				Recommended:                 recommendation.Recommended,
				CompanyName:                 companyName,
				CompanyLogo:                 companyLogo,
				ProductName:                 productName,
				VariantName:                 variantName,
				CompanyNetworkHospitalCount: networkHospitalCount,
				CompanyClaimSettlementRatio: claimSettlementRatio,
			})
		}

		quoteDef := dto.QuoteDefForExport{
			SessionID:         "",
			QuoteID:           quote.QuoteId,
			SumInsured:        quote.SumInsured,
			Pincode:           quote.Pincode,
			Deductible:        quote.Deductible,
			TotalVariants:     quote.TotalVariants,
			CompletedVariants: quote.CompletedVariants,
			MemberDetails:     memberDetails,
			PlanType:          quote.PlanType.String(),
			CustomerName:      quote.CustomerName,
			CustomerPhone:     quote.CustomerPhone,
			CustomerGender:    quote.CustomerGender,
			CustomerEmail:     quote.CustomerEmail,
			Recommendations:   recommendations,
		}

		quotesResponse = append(quotesResponse, quoteDef)
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetQuotesResponse{
			Quotes:      quotesResponse,
			HasNext:     quoteRes.HasNext,
			HasPrev:     quoteRes.HasPrev,
			StartCursor: quoteRes.StartCursor,
			EndCursor:   quoteRes.EndCursor,
		},
	}, nil
}
