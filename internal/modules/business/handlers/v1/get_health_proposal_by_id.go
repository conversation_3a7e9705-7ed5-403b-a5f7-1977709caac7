package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/business/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
)

func (h *Handler) GetHealthProposalById(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	proposalClient := proposal.NewProposalClient(conn)

	// Get enterprise_id from token context
	// enterpriseID := ctx.GinCtx.GetString("enterprise_id")
	// if enterpriseID == "" {
	// 	ctx.Logger.Error("Enterprise ID missing from context")
	// 	return nil, &response.ErrorResponse{
	// 		Status: &response.Status{
	// 			HttpStatus: http.StatusUnauthorized,
	// 		},
	// 		Problem: &response.Problem{
	// 			Type:   oa_errors.ErrInvalidInput.GetCode(),
	// 			Title:  oa_errors.ErrInvalidInput.GetMessage(),
	// 			Detail: "Enterprise ID is required and missing from the request context.",
	// 		},
	// 	}
	// }

	// Create new gRPC context with the metadata
	// gCtx := metadata.AppendToOutgoingContext(context.Background(),
	// 	"enterprise_id", "18TzlaIOrzcGFHY7",
	// 	"partner_id", "18lB4jjKyhOETWXw",
	// )

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	proposalID := ctx.GinCtx.Param("proposal_id")

	grpcReq := &proposal.GetProposalByIdRequest{
		ProposalId: proposalID,
	}

	res, err := proposalClient.GetProposalById(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetProposals gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	var proposalResponse dto.ProposalDefForExport

	proposalResponse = dto.ProposalDefForExport{
		ProposalID:        res.Proposal.ProposalId,
		SessionID:         res.Proposal.SessionId,
		Name:              res.Proposal.Name,
		Phone:             res.Proposal.Phone,
		Email:             res.Proposal.Email,
		InsurerName:       res.Proposal.InsurerName,
		InsurerID:         res.Proposal.InsurerId,
		ProductName:       res.Proposal.ProductName,
		ProductID:         res.Proposal.ProductId,
		VariantName:       res.Proposal.VariantName,
		VariantID:         res.Proposal.VariantId,
		Tenure:            res.Proposal.Tenure,
		NetPremium:        res.Proposal.NetPremium,
		GrossPremium:      res.Proposal.GrossPremium,
		Status:            dto.ProposalStatus(res.Proposal.Status.String()),
		InsurerProposalID: res.Proposal.InsurerProposalId,
		CancelledReason:   res.Proposal.CancelledReason,
		RejectionReason:   res.Proposal.RejectionReason,
		QuoteID:           res.Proposal.QuoteId,
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetProposalByIdResponse{
			Proposal: proposalResponse,
		},
	}, nil
}
