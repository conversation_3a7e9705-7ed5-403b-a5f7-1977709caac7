package v1

import (
	"context"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/business/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	policies "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	oa_errors "github.com/oneassure-tech/oa-utilities/go/oa-errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (h *Handler) GetHealthPolicies(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	// // Get enterprise_id from token context
	// enterpriseID := ctx.GinCtx.GetString("enterprise_id")
	// if enterpriseID == "" {
	// 	ctx.Logger.Error("Enterprise ID missing from context")
	// 	return nil, &response.ErrorResponse{
	// 		Status: &response.Status{
	// 			HttpStatus: http.StatusUnauthorized,
	// 		},
	// 		Problem: &response.Problem{
	// 			Type:   oa_errors.ErrInvalidInput.GetCode(),
	// 			Title:  oa_errors.ErrInvalidInput.GetMessage(),
	// 			Detail: "Enterprise ID is required and missing from the request context.",
	// 		},
	// 	}
	// }

	// // Create new gRPC context with the metadata
	// gCtx := metadata.AppendToOutgoingContext(context.Background(),
	// 	"enterprise_id", "18TzlaIOrzcGFHY7",
	// 	"partner_id", "18lB4jjKyhOETWXw",
	// )

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Check if the start_date and end_date are within a range of 7 days if the difference is more return error
	startDate, err := time.Parse("2006-01-02", ctx.GinCtx.Query("start_date"))
	if err != nil {
		ctx.Logger.Error("Error parsing start date", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	endDate, err := time.Parse("2006-01-02", ctx.GinCtx.Query("end_date"))
	if err != nil {
		ctx.Logger.Error("Error parsing end date", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	if endDate.Sub(startDate) > 7*24*time.Hour {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Type:   oa_errors.ErrInvalidInput.GetCode(),
				Title:  oa_errors.ErrInvalidInput.GetMessage(),
				Detail: "Start date and end date must be within 7 days",
			},
		}
	}

	next := ctx.GinCtx.Query("next")
	prev := ctx.GinCtx.Query("prev")

	var nextPtr *string
	var prevPtr *string

	if next == "" {
		nextPtr = nil
	}

	if prev == "" {
		prevPtr = nil
	}

	grpcReq := &policies.GetPoliciesRequest{
		StartDate: timestamppb.New(startDate),
		EndDate:   timestamppb.New(endDate),
		Next:      nextPtr,
		Prev:      prevPtr,
	}

	// fmt.Println("REQUEST", grpcReq)

	res, err := client.GetPolicies(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetPolicies gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// fmt.Println("res", res.Policies)

	var policiesResponse []dto.PolicyDefForExport

	for _, policy := range res.Policies {
		proposerDetails := dto.ProposerDetails{
			AddressLine1:      policy.ProposerDetails.Address.AddressLine_1,
			AddressLine2:      policy.ProposerDetails.Address.AddressLine_2,
			City:              policy.ProposerDetails.Address.City,
			State:             policy.ProposerDetails.Address.State,
			Country:           policy.ProposerDetails.Address.Country,
			Pincode:           policy.ProposerDetails.Address.Pincode,
			DOB:               policy.ProposerDetails.Dob,
			Email:             policy.ProposerDetails.Email,
			Name:              policy.ProposerDetails.Name,
			Phone:             policy.ProposerDetails.Phone,
			BankID:            policy.ProposerDetails.BankDetails.BankId,
			BankAccountNumber: policy.ProposerDetails.BankDetails.BankAccountNumber,
			BankName:          policy.ProposerDetails.BankDetails.BankName,
			BranchID:          policy.ProposerDetails.BankDetails.BranchId,
			BranchName:        policy.ProposerDetails.BankDetails.BranchName,
			IFSCCode:          policy.ProposerDetails.BankDetails.IfscCode,
		}

		insuredMembers := []dto.InsuredMember{}
		for _, member := range policy.InsuredMembers {
			insuredMembers = append(insuredMembers, dto.InsuredMember{
				MemberID:            member.MemberId,
				DOB:                 member.Dob,
				Gender:              member.Gender,
				Name:                member.Name,
				PreExistingDiseases: &member.PreExistingDiseases,
				Relationship:        member.Relationship,
			})
		}

		riders := []dto.Rider{}
		for _, rider := range policy.Riders {
			riders = append(riders, dto.Rider{
				ID:   rider.Id,
				Name: rider.Name,
			})
		}

		nominees := []dto.Nominee{}
		for _, nominee := range policy.Nominees {
			nominees = append(nominees, dto.Nominee{
				NomineeID:    nominee.NomineeId,
				NomineeName:  nominee.NomineeName,
				NomineePhone: &nominee.NomineePhone,
				Relationship: nominee.Relationship,
			})
		}

		policiesResponse = append(policiesResponse, dto.PolicyDefForExport{
			PolicyID:            policy.PolicyId,
			InsurerPolicyNumber: policy.InsurerPolicyNumber,
			Status:              policy.Status.String(),
			IssuanceDate:        policy.IssuanceDate,
			ExpiryDate:          policy.ExpiryDate,
			SumInsured:          policy.SumInsured,
			PolicyType:          policy.PolicyType.String(),
			Tenure:              policy.Tenure,
			GrossPremium:        policy.GrossPremium,
			InsurerID:           policy.InsurerId,
			InsurerName:         policy.InsurerName,
			InsurerLogo:         policy.InsurerLogo,
			ProductID:           policy.ProductId,
			ProductName:         policy.ProductName,
			VariantID:           policy.VariantId,
			VariantName:         policy.VariantName,
			PreviousPolicyID:    policy.PreviousPolicyId,
			ProposerDetails:     proposerDetails,
			InsuredMembers:      insuredMembers,
			Nominees:            nominees,
			Riders:              riders,
			ProposalID:          policy.ProposalId,
			QuoteID:             policy.QuoteId,
			PolicyURL:           &policy.PolicyUrl,
		})
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetHealthPoliciesResponse{
			Policies:    policiesResponse,
			HasNext:     res.HasNext,
			HasPrevious: res.HasPrevious,
			StartCursor: res.StartCursor,
			EndCursor:   res.EndCursor,
		},
	}, nil
}
