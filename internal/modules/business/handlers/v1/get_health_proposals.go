package v1

import (
	"context"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/business/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	oa_errors "github.com/oneassure-tech/oa-utilities/go/oa-errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (h *Handler) GetHealthProposals(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	proposalClient := proposal.NewProposalClient(conn)

	// Get enterprise_id from token context
	// enterpriseID := ctx.GinCtx.GetString("enterprise_id")
	// if enterpriseID == "" {
	// 	ctx.Logger.Error("Enterprise ID missing from context")
	// 	return nil, &response.ErrorResponse{
	// 		Status: &response.Status{
	// 			HttpStatus: http.StatusUnauthorized,
	// 		},
	// 		Problem: &response.Problem{
	// 			Type:   oa_errors.ErrInvalidInput.GetCode(),
	// 			Title:  oa_errors.ErrInvalidInput.GetMessage(),
	// 			Detail: "Enterprise ID is required and missing from the request context.",
	// 		},
	// 	}
	// }

	// // Create new gRPC context with the metadata
	// gCtx := metadata.AppendToOutgoingContext(context.Background(),
	// 	"enterprise_id", "18TzlaIOrzcGFHY7",
	// 	"partner_id", "18lB4jjKyhOETWXw",
	// )

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Check if the start_date and end_date are within a range of 7 days if the difference is more return error
	startDate, err := time.Parse("2006-01-02", ctx.GinCtx.Query("start_date"))
	if err != nil {
		ctx.Logger.Error("Error parsing start date", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	endDate, err := time.Parse("2006-01-02", ctx.GinCtx.Query("end_date"))
	if err != nil {
		ctx.Logger.Error("Error parsing end date", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	if endDate.Sub(startDate) > 7*24*time.Hour {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Type:   oa_errors.ErrInvalidInput.GetCode(),
				Title:  oa_errors.ErrInvalidInput.GetMessage(),
				Detail: "Start date and end date must be within 7 days",
			},
		}
	}

	next := ctx.GinCtx.Query("next")
	prev := ctx.GinCtx.Query("prev")

	var nextPtr *string
	var prevPtr *string

	if next == "" {
		nextPtr = nil
	}

	if prev == "" {
		prevPtr = nil
	}

	grpcReq := &proposal.GetProposalsRequest{
		StartDate: timestamppb.New(startDate),
		EndDate:   timestamppb.New(endDate),
		Next:      nextPtr,
		Prev:      prevPtr,
	}

	res, err := proposalClient.GetProposals(grpcCtx.(context.Context), grpcReq)
	if err != nil {
		ctx.Logger.Error("Error calling GetProposals gRPC service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	var proposalsResponse []dto.ProposalDefForExport

	for _, proposal := range res.Proposals {
		proposalsResponse = append(proposalsResponse, dto.ProposalDefForExport{
			ProposalID:        proposal.ProposalId,
			SessionID:         "",
			Name:              proposal.Name,
			Phone:             proposal.Phone,
			Email:             proposal.Email,
			InsurerName:       proposal.InsurerName,
			InsurerID:         proposal.InsurerId,
			ProductName:       proposal.ProductName,
			ProductID:         proposal.ProductId,
			VariantName:       proposal.VariantName,
			VariantID:         proposal.VariantId,
			Tenure:            proposal.Tenure,
			NetPremium:        proposal.NetPremium,
			GrossPremium:      proposal.GrossPremium,
			Status:            dto.ProposalStatus(proposal.Status.String()),
			InsurerProposalID: proposal.InsurerProposalId,
			CancelledReason:   proposal.CancelledReason,
			RejectionReason:   proposal.RejectionReason,
			QuoteID:           proposal.QuoteId,
		})
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetProposalsResponse{
			Proposals: proposalsResponse,
			HasNext:   res.HasNext,
			HasPrev:   res.HasPrev,
			Count:     res.Count,
		},
	}, nil
}
