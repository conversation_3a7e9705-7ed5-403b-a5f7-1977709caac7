package dto

type GetHealthProposalsRequest struct {
	StartDate string  `form:"start_date" validate:"required,datetime=2006-01-02"`
	EndDate   string  `form:"end_date" validate:"required,datetime=2006-01-02"`
	Next      *string `form:"next,omitempty"`
	Prev      *string `form:"prev,omitempty"`
}

type ProposalStatus string

const (
	PROPOSAL_STATUS_INITIATED    ProposalStatus = "INITIATED"
	PROPOSAL_STATUS_PENDING      ProposalStatus = "PENDING"
	PROPOSAL_STATUS_UNDERWRITING ProposalStatus = "UNDERWRITING"
	PROPOSAL_STATUS_ACCEPTED     ProposalStatus = "ACCEPTED"
	PROPOSAL_STATUS_REJECTED     ProposalStatus = "REJECTED"
)

type ProposalDefForExport struct {
	ProposalID        string         `json:"proposal_id"`
	SessionID         string         `json:"session_id"`
	Name              string         `json:"name"`
	Phone             string         `json:"phone"`
	Email             string         `json:"email"`
	InsurerName       string         `json:"insurer_name"`
	InsurerID         string         `json:"insurer_id"`
	ProductName       string         `json:"product_name"`
	ProductID         string         `json:"product_id"`
	VariantName       string         `json:"variant_name"`
	VariantID         string         `json:"variant_id"`
	Tenure            string         `json:"tenure"`
	NetPremium        string         `json:"net_premium"`
	GrossPremium      string         `json:"gross_premium"`
	Status            ProposalStatus `json:"status"`
	InsurerProposalID string         `json:"insurer_proposal_id"`
	CancelledReason   *string        `json:"cancelled_reason,omitempty"`
	RejectionReason   *string        `json:"rejection_reason,omitempty"`
	QuoteID           string         `json:"quote_id"`
}

type GetProposalsResponse struct {
	Proposals   []ProposalDefForExport `json:"proposals"`
	EndCursor   string                 `json:"end_cursor"`
	StartCursor string                 `json:"start_cursor"`
	HasNext     bool                   `json:"has_next"`
	HasPrev     bool                   `json:"has_prev"`
	Count       int32                  `json:"count"`
}

type GetProposalByIdResponse struct {
	Proposal ProposalDefForExport `json:"proposal"`
}
