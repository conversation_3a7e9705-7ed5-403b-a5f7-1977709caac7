package dto

type GetHealthPoliciesRequest struct {
	StartDate string  `form:"start_date" validate:"required,datetime=2006-01-02"`
	EndDate   string  `form:"end_date" validate:"required,datetime=2006-01-02"`
	Next      *string `form:"next,omitempty"`
	Prev      *string `form:"prev,omitempty"`
}

type Address struct {
	AddressLine1 string  `json:"address_line_1"`
	AddressLine2 *string `json:"address_line_2"`
	City         string  `json:"city"`
	State        string  `json:"state"`
	Country      string  `json:"country"`
	Pincode      string  `json:"pincode"`
}

type BankDetails struct {
	BankID            string `json:"bank_id"`
	BankAccountNumber string `json:"bank_account_number"`
	BankName          string `json:"bank_name"`
	BranchID          string `json:"branch_id"`
	BranchName        string `json:"branch_name"`
	IFSCCode          string `json:"ifsc_code"`
}

type ProposerDetails struct {
	AddressLine1      string  `json:"address_line_1"`
	AddressLine2      *string `json:"address_line_2"`
	City              string  `json:"city"`
	State             string  `json:"state"`
	Country           string  `json:"country"`
	Pincode           string  `json:"pincode"`
	DOB               string  `json:"dob"`
	Email             *string `json:"email"`
	Name              string  `json:"name"`
	Phone             string  `json:"phone"`
	BankID            string  `json:"bank_id"`
	BankAccountNumber string  `json:"bank_account_number"`
	BankName          string  `json:"bank_name"`
	BranchID          string  `json:"branch_id"`
	BranchName        string  `json:"branch_name"`
	IFSCCode          string  `json:"ifsc_code"`
}

type InsuredMember struct {
	MemberID            string    `json:"member_id"`
	DOB                 string    `json:"dob"`
	Gender              string    `json:"gender"`
	Name                string    `json:"name"`
	PreExistingDiseases *[]string `json:"pre_existing_diseases"`
	Relationship        string    `json:"relationship"`
}

type Nominee struct {
	NomineeID    string  `json:"nominee_id"`
	NomineeName  string  `json:"nominee_name"`
	NomineePhone *string `json:"nominee_phone"`
	Relationship string  `json:"relationship"`
}

type Rider struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type PolicyDefForExport struct {
	SessionID           *string         `json:"session_id"`
	PolicyID            string          `json:"policy_id"`
	InsurerPolicyNumber string          `json:"insurer_policy_number"`
	Status              string          `json:"status"`
	IssuanceDate        string          `json:"issuance_date"`
	ExpiryDate          string          `json:"expiry_date"`
	SumInsured          string          `json:"sum_insured"`
	PolicyType          string          `json:"policy_type"`
	Tenure              string          `json:"tenure"`
	GrossPremium        string          `json:"gross_premium"`
	InsurerID           string          `json:"insurer_id"`
	InsurerName         string          `json:"insurer_name"`
	InsurerLogo         string          `json:"insurer_logo"`
	ProductID           string          `json:"product_id"`
	ProductName         string          `json:"product_name"`
	VariantID           string          `json:"variant_id"`
	VariantName         string          `json:"variant_name"`
	PreviousPolicyID    *string         `json:"previous_policy_id"`
	ProposerDetails     ProposerDetails `json:"proposer_details"`
	InsuredMembers      []InsuredMember `json:"insured_members,omitempty"`
	Nominees            []Nominee       `json:"nominees,omitempty"`
	Riders              []Rider         `json:"riders,omitempty"`
	ProposalID          *string         `json:"proposal_id"`
	QuoteID             *string         `json:"quote_id"`
	PolicyURL           *string         `json:"policy_url"`
}

type GetHealthPoliciesResponse struct {
	Policies    []PolicyDefForExport `json:"policies"`
	EndCursor   *string              `json:"end_cursor,omitempty"`
	StartCursor *string              `json:"start_cursor,omitempty"`
	HasPrevious bool                 `json:"has_previous"`
	HasNext     bool                 `json:"has_next"`
	TotalCount  int32                `json:"total_count"`
}

type GetPolicyByIdResponse struct {
	Policy PolicyDefForExport `json:"policy"`
}
