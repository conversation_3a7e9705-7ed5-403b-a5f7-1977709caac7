package dto

type GetQuotesRequest struct {
	StartDate string  `form:"start_date" validate:"required,datetime=2006-01-02"`
	EndDate   string  `form:"end_date" validate:"required,datetime=2006-01-02"`
	Next      *string `form:"next,omitempty"`
	Prev      *string `form:"prev,omitempty"`
}

type GetQuotesResponse struct {
	Quotes      []QuoteDefForExport `json:"quotes"`
	EndCursor   *string             `json:"end_cursor,omitempty"`
	StartCursor *string             `json:"start_cursor,omitempty"`
	HasNext     bool                `json:"has_next"`
	HasPrev     bool                `json:"has_prev"`
	Count       int32               `json:"count"`
}

type QuoteDefForExport struct {
	SessionID         string                       `json:"session_id"`
	QuoteID           string                       `json:"quote_id"`
	SumInsured        string                       `json:"sum_insured"`
	Pincode           string                       `json:"pincode"`
	Deductible        string                       `json:"deductible"`
	TotalVariants     int32                        `json:"total_variants"`
	CompletedVariants int32                        `json:"completed_variants"`
	MemberDetails     []MemberDetail               `json:"member_details"`
	PlanType          string                       `json:"plan_type"`
	CustomerName      string                       `json:"customer_name"`
	CustomerPhone     string                       `json:"customer_phone"`
	CustomerGender    string                       `json:"customer_gender"`
	CustomerEmail     string                       `json:"customer_email"`
	Recommendations   []RecommendationDefForExport `json:"recommendations"`
}

type MemberDetail struct {
	Relation string `json:"relation"`
	Age      int32  `json:"age"`
}

type RecommendationDefForExport struct {
	RecommendationID            string                      `json:"recommendation_id"`
	CompanyID                   string                      `json:"company_id"`
	CompanyName                 string                      `json:"company_name"`
	CompanyLogo                 string                      `json:"company_logo"`
	CompanyDescription          string                      `json:"company_description"`
	CompanyNetworkHospitalCount int32                       `json:"company_network_hospital_count"`
	CompanyClaimSettlementRatio int32                       `json:"company_claim_settlement_ratio"`
	ProductID                   string                      `json:"product_id"`
	ProductName                 string                      `json:"product_name"`
	VariantID                   string                      `json:"variant_id"`
	VariantName                 string                      `json:"variant_name"`
	PremiumDetails              []PremiumDetailDefForExport `json:"premium_details"`
	Recommended                 bool                        `json:"recommended"`
}

type PremiumDetailDefForExport struct {
	Tenure  int32  `json:"tenure"`
	Premium string `json:"premium"`
}

type GetQuoteByIdResponse struct {
	Quote QuoteDefForExport `json:"quote"`
}
