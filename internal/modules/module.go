package module

import (
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/http_router"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/application"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/auth"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/business"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/callback"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/catalog"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/compare"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/creatives"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/embedded"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/enterprise"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/forms"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/hasura"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/health"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/leads"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/orchestration"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/payouts"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/policies"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/proposal"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/rapidshort"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/recommendation"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/site"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/storage"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/summary"
	termrecommendation "github.com/oneassure-tech/oa-gateway-svc/internal/modules/term-recommendation"
	termproposal "github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_proposal"
	termsummary "github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_summary"
)

type ModuleIface interface {
	Initalize()
	RegisterApps()
	CreateContext(map[string]http_router.RouterIface, *grpc.GrpcClientManager)
}

type Module struct {
	apps   []app.AppInitFn
	appCtx *app.AppContext
}

func New() ModuleIface {
	return &Module{}
}

// Register your apps here
func (m *Module) RegisterApps() {
	m.apps = []app.AppInitFn{
		// affinity_recommendation.New,
		embedded.New,
		health.New,
		auth.New,
		recommendation.New,
		proposal.New,
		termproposal.New,
		orchestration.New,
		summary.New,
		leads.New,
		forms.New,
		application.New,
		callback.New,
		termrecommendation.New,
		termsummary.New,
		compare.New,
		policies.New,
		rapidshort.New,
		site.New,
		storage.New,
		business.New,
		catalog.New,
		enterprise.New,
		payouts.New,
		creatives.New,
		hasura.New,
	}
}

// Create the app context to be injected into apps
func (m *Module) CreateContext(routerMap map[string]http_router.RouterIface, grpcClients *grpc.GrpcClientManager) {

	m.appCtx = &app.AppContext{
		Config:      config.GetConfig(),
		Router:      routerMap,
		Logger:      logger.GetLogger().GetScopedLogger("initialization"),
		GrpcClients: grpcClients,
	}
}

// Initialize your apps
func (m *Module) Initalize() {

	if m.apps == nil {
		panic("App initializers were not added")
	}

	// Loop over apps and initialize them
	for _, app := range m.apps {
		newApp := app()
		name := newApp.SetAppName()

		app().Initialize(name, m.appCtx)
	}
}
