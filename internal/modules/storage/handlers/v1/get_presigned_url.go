package v1

import (
	"context"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/storage/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-utilities/go/storage"

	// "github.com/oneassure-tech/oa-gateway-svc/storage"
	"go.uber.org/zap"
)

// GetPresignedUrls generates presigned URLs for file uploads
func (h *Handler) GetPresignedUrls(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.GetPresignedUrlsQueryRequest

	// Bind query parameters to request struct
	err := ctx.GinCtx.BindQuery(&req)
	if err != nil {
		ctx.Logger.Error("Failed to bind query parameters", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	fmt.Println("default bucket", config.GetConfig().Storage.DefaultBucket)

	// Get storage manager instance
	storageManager, err := storage.NewStorageManager(context.Background(), storage.StorageConfig{
		Provider:      storage.AWS,
		DefaultBucket: config.GetConfig().Storage.DefaultBucket,
		RootPrefix:    config.GetConfig().Storage.RootPrefix,
	})

	if err != nil {
		ctx.Logger.Error("Failed to get storage manager", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Get presigned URL
	presignedUrl, err := storageManager.GetAdapter().GetPresignedDownloadUrl(
		context.Background(),
		&storage.PresignedDownloadUrlRequest{
			Key: req.Key,
		},
	)

	if err != nil {
		ctx.Logger.Error("Failed to get presigned URL", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetPresignedUrlsResponse{
			Url: presignedUrl.Url,
		},
	}, nil
}
