package v1

import (
	"fmt"
	"io"
	"mime"
	"mime/multipart"
	"net/http"
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/objectly/v0/files"

	//
	"go.uber.org/zap"
)

// Upload handles direct file uploads (alternative to presigned URLs)
func (h *<PERSON><PERSON>) UploadNew(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(files.Files_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Failed to get file service connection", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	filesClient := files.NewFilesClient(conn)

	stream, err := filesClient.UploadFile(ctx.GinCtx.Request.Context())
	if err != nil {
		ctx.Logger.Error("Failed to get upload file stream", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// get the content type
	contentType := ctx.GinCtx.Request.Header.Get("Content-Type")
	fmt.Println("contentType", contentType)

	_, params, err := mime.ParseMediaType(contentType)
	if err != nil {
		return nil, response.NewInternalServerError()
	}
	boundary := params["boundary"]

	// Get the MultipartReader from the request body.
	reader := multipart.NewReader(ctx.GinCtx.Request.Body, boundary)

	for {
		part, err := reader.NextPart()
		if err == io.EOF {
			break // End of multipart data.
		}
		if err != nil {
			ctx.Logger.Error("Failed to read form part", zap.Error(err))
			return nil, response.NewInternalServerError()
		}

		// Check if the part is a file.
		if part.FileName() != "" {
			// Use a smaller chunk size to stay under gRPC message limit (4MB)
			const maxChunkSize = 256 * 1024 // 256KB chunks

			for {
				// Create a new buffer for each chunk
				buffer := make([]byte, maxChunkSize)
				n, err := part.Read(buffer)

				if n > 0 {
					// Only send the actual data read (buffer[:n])
					// Create a copy to avoid any potential buffer reuse issues
					chunkData := make([]byte, n)
					copy(chunkData, buffer[:n])

					// Send the chunk
					if err := stream.Send(&files.UploadFileRequest{
						FileName: part.FileName(),
						File:     chunkData,
						Token:    strings.TrimPrefix(ctx.GinCtx.GetHeader("Authorization"), "Bearer "),
						MimeType: part.Header.Get("Content-Type"),
					}); err != nil {
						ctx.Logger.Error("Failed to send file chunk", zap.Error(err))
						return nil, response.NewInternalServerError()
					}
				}

				if err == io.EOF {
					break // No more data, CloseAndRecv will handle stream end
				}
				if err != nil {
					ctx.Logger.Error("Failed to read file chunk", zap.Error(err))
					return nil, response.NewInternalServerError()
				}

			}
		}
	}

	// Close the stream and receive the server's final response
	res, err := stream.CloseAndRecv()
	if err != nil {
		ctx.Logger.Error("Failed to receive response from server", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: map[string]string{
			"key": res.FileKey,
		},
	}, nil
}
