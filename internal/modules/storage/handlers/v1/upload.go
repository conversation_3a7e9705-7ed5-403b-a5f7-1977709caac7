package v1

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"github.com/google/uuid"
	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-utilities/go/storage"

	// "github.com/oneassure-tech/oa-gateway-svc/storage"
	"go.uber.org/zap"
)

// // GetPresignedUrls generates presigned URLs for file uploads
// func (h *Handler) GetPresignedUrls(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
// 	var req dto.GetPresignedUrlsRequest
// 	if err := ctx.GinCtx.ShouldBindJSON(&req); err != nil {
// 		ctx.Logger.Error("Failed to bind JSON request", zap.Error(err))
// 		return nil, response.NewBadRequestError("Invalid request format")
// 	}

// 	// Validate that file names and content types match
// 	if len(req.FileNames) != len(req.ContentTypes) {
// 		return nil, response.NewBadRequestError("Number of file names must match number of content types")
// 	}

// 	// Get enterprise ID from JWT claims (assuming it's available in context)
// 	enterpriseID := ctx.GetString("enterprise_id")
// 	if enterpriseID == "" {
// 		return nil, response.NewUnauthorizedError("Enterprise ID not found in token")
// 	}

// 	userID := ctx.GetString("user_id") // Optional

// 	// Get storage manager instance (this would be injected or configured)
// 	storageManager, err := getStorageManager(ctx.GinCtx.Request.Context())
// 	if err != nil {
// 		ctx.Logger.Error("Failed to get storage manager", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}
// 	defer storageManager.Close()

// 	// Create upload configuration
// 	uploadConfig := storage.UploadConfig{
// 		MaxFileSize:    req.MaxFileSize,
// 		AllowedTypes:   req.AllowedTypes,
// 		MaxFileCount:   req.MaxFileCount,
// 		ExpirationTime: time.Now().Add(time.Duration(req.ExpirationMins) * time.Minute).Unix(),
// 		BucketName:     req.BucketName,
// 		KeyPrefix:      req.KeyPrefix,
// 	}

// 	// Create presigned URL request
// 	presignedReq := &storage.PresignedUrlRequest{
// 		Config:       uploadConfig,
// 		FileNames:    req.FileNames,
// 		ContentTypes: req.ContentTypes,
// 		EnterpriseID: enterpriseID,
// 		UserID:       userID,
// 	}

// 	// Generate presigned URLs
// 	result, err := storageManager.GetAdapter().GetPresignedUrl(ctx.GinCtx.Request.Context(), presignedReq)
// 	if err != nil {
// 		ctx.Logger.Error("Failed to generate presigned URLs", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}

// 	// Convert to response format
// 	uploadUrls := make([]dto.FileUploadInfoResponse, len(result.UploadUrls))
// 	for i, uploadInfo := range result.UploadUrls {
// 		uploadUrls[i] = dto.FileUploadInfoResponse{
// 			FileName:  uploadInfo.FileName,
// 			UploadUrl: uploadInfo.UploadUrl,
// 			Fields:    uploadInfo.Fields,
// 			Headers:   uploadInfo.Headers,
// 			Method:    uploadInfo.Method,
// 			ObjectKey: uploadInfo.ObjectKey,
// 		}
// 	}

// 	response := dto.GetPresignedUrlsResponse{
// 		Token:      result.Token,
// 		UploadUrls: uploadUrls,
// 		ExpiresAt:  result.ExpiresAt,
// 		Instructions: dto.UploadInstructionsResponse{
// 			MultipartUpload: result.Instructions.MultipartUpload,
// 			MaxRetries:      result.Instructions.MaxRetries,
// 			TimeoutSeconds:  result.Instructions.TimeoutSeconds,
// 			ValidationUrl:   result.Instructions.ValidationUrl,
// 		},
// 	}

// 	return &response.SuccessResponse{
// 		Status:  http.StatusOK,
// 		Payload: response,
// 	}, nil
// }

// VerifyUpload verifies that files were uploaded successfully using a token
// func (h *Handler) Upload(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
// 	var req dto.VerifyUploadRequest
// 	if err := ctx.GinCtx.ShouldBindJSON(&req); err != nil {
// 		ctx.Logger.Error("Failed to bind JSON request", zap.Error(err))
// 		return nil, response.NewBadRequestError("Invalid request format")
// 	}

// 	// Get storage manager instance
// 	storageManager, err := getStorageManager(ctx.GinCtx.Request.Context())
// 	if err != nil {
// 		ctx.Logger.Error("Failed to get storage manager", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}
// 	defer storageManager.Close()

// 	// Verify uploads
// 	results, err := storageManager.GetAdapter().VerifyUpload(ctx.GinCtx.Request.Context(), req.Token, req.FileNames)
// 	if err != nil {
// 		ctx.Logger.Error("Failed to verify uploads", zap.Error(err))
// 		return nil, response.NewBadRequestError(fmt.Sprintf("Upload verification failed: %v", err))
// 	}

// 	// Convert to response format
// 	resultResponses := make([]dto.UploadResultResponse, len(results))
// 	for i, result := range results {
// 		resultResponses[i] = dto.UploadResultResponse{
// 			FileName:   result.FileName,
// 			ObjectKey:  result.ObjectKey,
// 			Size:       result.Size,
// 			ETag:       result.ETag,
// 			UploadedAt: result.UploadedAt,
// 		}
// 	}

// 	response := dto.VerifyUploadResponse{
// 		Results: resultResponses,
// 		Success: true,
// 		Message: "All files verified successfully",
// 	}

// 	return &response.SuccessResponse{
// 		Status:  http.StatusOK,
// 		Payload: response,
// 	}, nil
// }

// Upload handles direct file uploads (alternative to presigned URLs)
func (h *Handler) Upload(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Parse multipart form
	err := ctx.GinCtx.Request.ParseMultipartForm(32 << 20) // 32MB max memory
	if err != nil {
		ctx.Logger.Error("Failed to parse multipart form", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// fmt.Println("form", form)
	maxFileSize, _ := ctx.GinCtx.Get("max_file_size")
	allowedTypes, _ := ctx.GinCtx.Get("allowed_types")
	maxFiles, _ := ctx.GinCtx.Get("max_files")
	minFiles, _ := ctx.GinCtx.Get("min_files")
	altFileName, _ := ctx.GinCtx.Get("alt_file_name")
	keepOriginalName, _ := ctx.GinCtx.Get("keep_original_name")
	storagePartition, _ := ctx.GinCtx.Get("storage_partition")

	fmt.Println("maxFileSize", maxFileSize)
	fmt.Println("allowedTypes", allowedTypes)
	fmt.Println("maxFiles", maxFiles)
	fmt.Println("minFiles", minFiles)
	fmt.Println("altFileName", altFileName)
	fmt.Println("keepOriginalName", keepOriginalName)
	fmt.Println("storagePartition", storagePartition)

	form, _ := ctx.GinCtx.MultipartForm()
	files := form.File["file"]

	var fileName string

	fmt.Println("files", files)

	// Convert keepOriginalName string to bool
	keepOriginalNameBool, _ := strconv.ParseBool(keepOriginalName.(string))

	if !keepOriginalNameBool {
		fileName = uuid.New().String()
	} else {
		fileName = files[0].Filename
	}

	storageManager, err := storage.NewStorageManager(ctx.GinCtx.Request.Context(), storage.StorageConfig{
		Provider:      storage.AWS,
		DefaultBucket: config.GetConfig().Storage.DefaultBucket,
		RootPrefix:    config.GetConfig().Storage.RootPrefix,
	})

	if err != nil {
		ctx.Logger.Error("Failed to get storage manager", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	f, _ := files[0].Open()
	defer f.Close()

	fmt.Println("fileName", fileName)
	result, err := storageManager.GetAdapter().UploadFile(
		context.Background(),
		&storage.UploadConfig{
			BucketName: config.GetConfig().Storage.DefaultBucket,
			Key:        fmt.Sprintf("%s/%s", storagePartition, fileName),
			MimeType:   files[0].Header.Get("Content-Type"),
			FileName:   fileName,
		},
		f,
	)

	fmt.Println("result", result)

	if err != nil {
		ctx.Logger.Error("Failed to upload file", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Upload the file to specific dst.

	// Get form values
	// maxFileSizeStr := ctx.GinCtx.Request.FormValue("max_file_size")
	// allowedTypesStr := ctx.GinCtx.Request.FormValue("allowed_types")
	// bucketName := ctx.GinCtx.Request.FormValue("bucket_name")
	// keyPrefix := ctx.GinCtx.Request.FormValue("key_prefix")

	// Parse max file size
	// maxFileSize, err := strconv.ParseInt(maxFileSizeStr, 10, 64)
	// if err != nil || maxFileSize <= 0 {
	// 	return nil, response.NewBadRequestError("Invalid max_file_size")
	// }

	// // Parse allowed types
	// var allowedTypes []string
	// if allowedTypesStr != "" {
	// 	allowedTypes = strings.Split(allowedTypesStr, ",")
	// }

	// // Get enterprise ID from JWT claims
	// enterpriseID := ctx.GetString("enterprise_id")
	// if enterpriseID == "" {
	// 	return nil, response.NewUnauthorizedError("Enterprise ID not found in token")
	// }

	// Get file from form
	// file, _, err := ctx.GinCtx.Request.FormFile("file")
	// // fmt.Println("file", file)
	// // fmt.Println("header", header)
	// if err != nil {
	// 	ctx.Logger.Error("Failed to get file from form", zap.Error(err))
	// 	return nil, response.NewInternalServerError()
	// }

	// // Validate file
	// if err := validateFileUpload(file, header, maxFileSize, allowedTypes); err != nil {
	// 	return nil, response.NewBadRequestError(err.Error())
	// }

	// // Get storage manager instance
	// storageManager, err := getStorageManager(ctx.GinCtx.Request.Context())
	// if err != nil {
	// 	ctx.Logger.Error("Failed to get storage manager", zap.Error(err))
	// 	return nil, response.NewInternalServerError()
	// }
	// defer storageManager.Close()

	// // Generate object key
	// objectKey := storage.GenerateObjectKey(keyPrefix, header.Filename, enterpriseID)

	// // Upload file
	// result, err := storageManager.GetAdapter().UploadFile(
	// 	ctx.GinCtx.Request.Context(),
	// 	bucketName,
	// 	objectKey,
	// 	file,
	// 	header.Header.Get("Content-Type"),
	// )
	// if err != nil {
	// 	ctx.Logger.Error("Failed to upload file", zap.Error(err))
	// 	return nil, response.NewInternalServerError()
	// }

	// response := dto.UploadFileResponse{
	// 	Result: dto.UploadResultResponse{
	// 		FileName:   result.FileName,
	// 		ObjectKey:  result.ObjectKey,
	// 		Size:       result.Size,
	// 		ETag:       result.ETag,
	// 		UploadedAt: result.UploadedAt,
	// 	},
	// 	Success: true,
	// 	Message: "File uploaded successfully",
	// }

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: map[string]string{
			"key": fmt.Sprintf("%s/%s", storagePartition, fileName),
		},
	}, nil
}

// DeleteFile deletes a file from storage
// func (h *Handler) DeleteFile(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
// 	bucketName := ctx.GetQuery("bucket_name")
// 	objectKey := ctx.GetQuery("object_key")

// 	if objectKey == "" {
// 		return nil, response.NewBadRequestError("object_key is required")
// 	}

// 	// Get storage manager instance
// 	storageManager, err := getStorageManager(ctx.GinCtx.Request.Context())
// 	if err != nil {
// 		ctx.Logger.Error("Failed to get storage manager", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}
// 	defer storageManager.Close()

// 	// Delete file
// 	err = storageManager.GetAdapter().DeleteFile(ctx.GinCtx.Request.Context(), bucketName, objectKey)
// 	if err != nil {
// 		ctx.Logger.Error("Failed to delete file", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}

// 	response := dto.DeleteFileResponse{
// 		Success: true,
// 		Message: "File deleted successfully",
// 	}

// 	return &response.SuccessResponse{
// 		Status:  http.StatusOK,
// 		Payload: response,
// 	}, nil
// }

// // ValidateToken validates a storage JWT token
// func (h *Handler) ValidateToken(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
// 	var req dto.ValidateTokenRequest
// 	if err := ctx.GinCtx.ShouldBindJSON(&req); err != nil {
// 		ctx.Logger.Error("Failed to bind JSON request", zap.Error(err))
// 		return nil, response.NewBadRequestError("Invalid request format")
// 	}

// 	// Get storage manager instance
// 	storageManager, err := getStorageManager(ctx.GinCtx.Request.Context())
// 	if err != nil {
// 		ctx.Logger.Error("Failed to get storage manager", zap.Error(err))
// 		return nil, response.NewInternalServerError()
// 	}
// 	defer storageManager.Close()

// 	// Validate token
// 	claims, err := storageManager.GetAdapter().ValidateToken(ctx.GinCtx.Request.Context(), req.Token)
// 	if err != nil {
// 		response := dto.ValidateTokenResponse{
// 			Valid:   false,
// 			Message: fmt.Sprintf("Token validation failed: %v", err),
// 		}

// 		return &response.SuccessResponse{
// 			Status:  http.StatusOK,
// 			Payload: response,
// 		}, nil
// 	}

// 	// Convert claims to response format
// 	claimsResponse := &dto.StorageTokenClaimsResponse{
// 		MaxFileSize:    claims.MaxFileSize,
// 		AllowedTypes:   claims.AllowedTypes,
// 		MaxFileCount:   claims.MaxFileCount,
// 		ExpirationTime: claims.ExpirationTime,
// 		BucketName:     claims.BucketName,
// 		KeyPrefix:      claims.KeyPrefix,
// 		TokenID:        claims.TokenID,
// 		EnterpriseID:   claims.EnterpriseID,
// 		UserID:         claims.UserID,
// 		IssuedAt:       claims.IssuedAt,
// 		ExpiresAt:      claims.ExpiresAt,
// 	}

// 	response := dto.ValidateTokenResponse{
// 		Valid:   true,
// 		Claims:  claimsResponse,
// 		Message: "Token is valid",
// 	}

// 	return &response.SuccessResponse{
// 		Status:  http.StatusOK,
// 		Payload: response,
// 	}, nil
// }

// Helper functions

// getStorageManager creates a storage manager instance
// In a real implementation, this would be injected or configured based on environment
// func getStorageManager(ctx context.Context) (*storage.StorageManager, error) {
// 	// This is a placeholder implementation. In practice, you would:
// 	// 1. Read configuration from environment variables or config files
// 	// 2. Initialize the appropriate storage adapter based on the environment
// 	// 3. Possibly use dependency injection or a service registry

// 	config := storage.StorageConfig{
// 		Provider:      storage.CloudProvider(getEnvOrDefault("STORAGE_PROVIDER", "aws")),
// 		Region:        getEnvOrDefault("STORAGE_REGION", "us-east-1"),
// 		DefaultBucket: getEnvOrDefault("STORAGE_DEFAULT_BUCKET", "default-bucket"),
// 		JWTSecret:     getEnvOrDefault("STORAGE_JWT_SECRET", "your-secret-key"),
// 		JWTIssuer:     getEnvOrDefault("STORAGE_JWT_ISSUER", "oa-gateway"),
// 		JWTAudience:   getEnvOrDefault("STORAGE_JWT_AUDIENCE", "storage"),

// 		// AWS specific
// 		AWSAccessKeyID:     os.Getenv("AWS_ACCESS_KEY_ID"),
// 		AWSSecretAccessKey: os.Getenv("AWS_SECRET_ACCESS_KEY"),
// 		AWSSessionToken:    os.Getenv("AWS_SESSION_TOKEN"),

// 		// GCP specific
// 		GCPCredentialsPath: os.Getenv("GCP_CREDENTIALS_PATH"),
// 		GCPProjectID:       os.Getenv("GCP_PROJECT_ID"),

// 		// Azure specific
// 		AzureAccountName:      os.Getenv("AZURE_ACCOUNT_NAME"),
// 		AzureAccountKey:       os.Getenv("AZURE_ACCOUNT_KEY"),
// 		AzureConnectionString: os.Getenv("AZURE_CONNECTION_STRING"),
// 	}

// 	return storage.NewStorageManager(ctx, config)
// }

// // validateFileUpload validates a file upload against constraints
// func validateFileUpload(file multipart.File, header *multipart.FileHeader, maxFileSize int64, allowedTypes []string) error {
// 	// Check file size
// 	if header.Size > maxFileSize {
// 		return fmt.Errorf("file size %d exceeds maximum allowed size %d", header.Size, maxFileSize)
// 	}

// 	// Check content type if restrictions are specified
// 	if len(allowedTypes) > 0 {
// 		contentType := header.Header.Get("Content-Type")
// 		allowed := false
// 		for _, allowedType := range allowedTypes {
// 			if contentType == allowedType {
// 				allowed = true
// 				break
// 			}
// 		}
// 		if !allowed {
// 			return fmt.Errorf("content type %s is not allowed", contentType)
// 		}
// 	}

// 	return nil
// }

// // getEnvOrDefault gets an environment variable or returns a default value
// func getEnvOrDefault(key, defaultValue string) string {
// 	if value := os.Getenv(key); value != "" {
// 		return value
// 	}
// 	return defaultValue
// }
