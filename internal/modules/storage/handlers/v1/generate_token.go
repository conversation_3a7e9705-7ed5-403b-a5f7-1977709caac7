package v1

import (
	"context"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/storage/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/objectly/v0/files"
	"google.golang.org/protobuf/types/known/timestamppb"

	// "github.com/oneassure-tech/oa-gateway-svc/storage"
	"go.uber.org/zap"
)

// GetPresignedUrls generates presigned URLs for file uploads
func (h *Handler) GenerateToken(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(files.Files_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Failed to get files client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	filesClient := files.NewFilesClient(conn)

	res, err := filesClient.CreateToken(context.Background(), &files.CreateTokenRequest{
		MaxFileSize:      "20Gi",
		AllowedMimeTypes: []string{"image/jpeg", "image/png", "image/gif", "image/webp"},
		StoragePartition: "/",
		ExpiresAt:        timestamppb.New(time.Now().Add(time.Hour * 24)),
	})

	if err != nil {
		ctx.Logger.Error("Failed to create token", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.CreateTokenResponse{
			Token:            res.Token,
			UploadIdentifier: res.UploadIdentifier,
		},
	}, nil

}
