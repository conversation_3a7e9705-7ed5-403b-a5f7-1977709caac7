package dto

import "time"

// GetPresignedUrlsRequest represents a request for generating presigned URLs
type GetPresignedUrlsQueryRequest struct {
	Key string `form:"key" validate:"required"`
}

// GetPresignedUrlsResponse represents the response containing presigned URLs
type GetPresignedUrlsResponse struct {
	Url string `json:"url"`
}

// FileUploadInfoResponse contains upload information for a single file
type FileUploadInfoResponse struct {
	FileName  string            `json:"file_name"`
	UploadUrl string            `json:"upload_url"`
	Fields    map[string]string `json:"fields,omitempty"`
	Headers   map[string]string `json:"headers,omitempty"`
	Method    string            `json:"method"`
	ObjectKey string            `json:"object_key"`
}

// UploadInstructionsResponse provides guidance on how to use the presigned URLs
type UploadInstructionsResponse struct {
	MultipartUpload bool   `json:"multipart_upload"`
	MaxRetries      int    `json:"max_retries"`
	TimeoutSeconds  int    `json:"timeout_seconds"`
	ValidationUrl   string `json:"validation_url,omitempty"`
}

// VerifyUploadRequest represents a request to verify file uploads
type VerifyUploadRequest struct {
	Token     string   `json:"token" validate:"required"`
	FileNames []string `json:"file_names" validate:"required,min=1"`
}

// VerifyUploadResponse represents the response for upload verification
type VerifyUploadResponse struct {
	Results []UploadResultResponse `json:"results"`
	Success bool                   `json:"success"`
	Message string                 `json:"message,omitempty"`
}

// UploadResultResponse represents the result of a file upload
type UploadResultResponse struct {
	FileName   string    `json:"file_name"`
	ObjectKey  string    `json:"object_key"`
	Size       int64     `json:"size"`
	ETag       string    `json:"etag,omitempty"`
	UploadedAt time.Time `json:"uploaded_at"`
}

// UploadFileRequest represents a request for direct file upload (multipart form)
type UploadFileRequest struct {
	MaxFileSize  int64    `form:"max_file_size" validate:"required,min=1"`
	AllowedTypes []string `form:"allowed_types"`
	BucketName   string   `form:"bucket_name,omitempty"`
	KeyPrefix    string   `form:"key_prefix,omitempty"`
}

// UploadFileResponse represents the response for direct file upload
type UploadFileResponse struct {
	Result  UploadResultResponse `json:"result"`
	Success bool                 `json:"success"`
	Message string               `json:"message,omitempty"`
}

// DeleteFileRequest represents a request to delete a file
type DeleteFileRequest struct {
	BucketName string `json:"bucket_name,omitempty"`
	ObjectKey  string `json:"object_key" validate:"required"`
}

// DeleteFileResponse represents the response for file deletion
type DeleteFileResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

// ValidateTokenRequest represents a request to validate a storage token
type ValidateTokenRequest struct {
	Token string `json:"token" validate:"required"`
}

// ValidateTokenResponse represents the response for token validation
type ValidateTokenResponse struct {
	Valid   bool                        `json:"valid"`
	Claims  *StorageTokenClaimsResponse `json:"claims,omitempty"`
	Message string                      `json:"message,omitempty"`
}

// StorageTokenClaimsResponse represents storage token claims in API response
type StorageTokenClaimsResponse struct {
	MaxFileSize    int64    `json:"max_file_size"`
	AllowedTypes   []string `json:"allowed_types"`
	MaxFileCount   int      `json:"max_file_count"`
	ExpirationTime int64    `json:"expiration_time"`
	BucketName     string   `json:"bucket_name"`
	KeyPrefix      string   `json:"key_prefix"`
	TokenID        string   `json:"token_id"`
	EnterpriseID   string   `json:"enterprise_id"`
	UserID         string   `json:"user_id,omitempty"`
	IssuedAt       int64    `json:"issued_at"`
	ExpiresAt      int64    `json:"expires_at"`
}

type CreateTokenResponse struct {
	Token            string
	UploadIdentifier string
}
