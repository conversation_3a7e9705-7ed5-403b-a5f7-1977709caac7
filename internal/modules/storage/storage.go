package storage

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/storage/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/storage/handlers/v1"
)

type StorageApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the storage application
func New() app.AppIface {
	return &StorageApp{}
}

// SetAppName returns the name of the application
func (app *StorageApp) SetAppName() string {
	return "storage"
}

// Initialize sets up the storage module with routes, services, and handlers
func (app *StorageApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	// Register HTTP routes
	apiRouter := appContext.Router[constant.HTTP_API]

	// Route to generate presigned URLs for file downloads
	apiRouter.RegisterRoute(ctx, appName, "GET", "/url",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.GetPresignedUrls,
				QueryStruct: &dto.GetPresignedUrlsQueryRequest{},
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{}, "gateway_portal", []string{
						"storage.url.get",
					}),
				},
			}),
		},
	)

	// Route to verify file uploads
	// apiRouter.RegisterRoute(ctx, appName, "POST", "/verify-upload",
	// 	map[uint8]*handler.OptionStruct{
	// 		1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
	// 			Handler:       h1.VerifyUpload,
	// 			RequireAuth:   &requireAuth,
	// 			ReqBodyStruct: &dto.VerifyUploadRequest{},
	// 			AuthMiddleware: []func(c *gin.Context) bool{
	// 				middleware.BearerAuthMiddleware([]string{"access"}, "gateway_storage"),
	// 			},
	// 		}),
	// 	},
	// )

	// Route to upload files directly (alternative to presigned URLs)
	apiRouter.RegisterRoute(ctx, appName, "POST", "/upload",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.Upload,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"max_file_size",
						"allowed_types",
						"max_files",
						"min_files",
						"alt_file_name",
						"keep_original_name",
						"storage_partition",
					}, "gateway_storage"),
				},
			}),
		},
	)

	// Route to delete files
	// apiRouter.RegisterRoute(ctx, appName, "DELETE", "/file",
	// 	map[uint8]*handler.OptionStruct{
	// 		1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
	// 			Handler:     h1.DeleteFile,
	// 			RequireAuth: &requireAuth,
	// 			AuthMiddleware: []func(c *gin.Context) bool{
	// 				middleware.BearerAuthMiddleware([]string{"access"}, "gateway_storage"),
	// 			},
	// 		}),
	// 	},
	// )

	// Route to validate storage tokens
	// apiRouter.RegisterRoute(ctx, appName, "POST", "/validate-token",
	// 	map[uint8]*handler.OptionStruct{
	// 		1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
	// 			Handler:       h1.ValidateToken,
	// 			RequireAuth:   &requireAuth,
	// 			ReqBodyStruct: &dto.ValidateTokenRequest{},
	// 			AuthMiddleware: []func(c *gin.Context) bool{
	// 				middleware.BearerAuthMiddleware([]string{"access"}, "gateway_storage"),
	// 			},
	// 		}),
	// 	},
	// )
}
