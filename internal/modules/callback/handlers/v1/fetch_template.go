package v1

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	catalog_metadata "github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/metadata"
)

func (h *<PERSON><PERSON>) fetchTemplate(ctx context.Context, productId string, keys []string) (string, *response.ErrorResponse) {

	metadata_conn, err := h.GrpcClients.Get(catalog_metadata.MetadataService_ServiceDesc.ServiceName)
	if err != nil {
		return "", &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed to connect to metadata service",
			},
		}
	}

	metadata_client := catalog_metadata.NewMetadataServiceClient(metadata_conn)

	productIdPtr := &productId
	req := catalog_metadata.GetMetadataRequest{
		ProductId: productIdPtr,
		Keys:      keys,
	}

	resp, err := metadata_client.GetMetadata(ctx, &req)
	if err != nil {
		return "", &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed Get Metadata from Metadata Service",
			},
		}
	}

	if len(resp.Metadata) == 0 {
		return "", &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusNotFound,
			},
			Problem: &response.Problem{
				Title:  "Not Found",
				Detail: "No metadata found",
			},
		}
	}

	metadata := resp.Metadata[0]

	value := metadata.Value.AsMap()

	templateKey := keys[0] + "_template"
	unescapedTemplate, ok := value[templateKey].(string)
	if !ok {
		return "", &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusNotFound,
			},
			Problem: &response.Problem{
				Title:  "Not Found",
				Detail: "No payment callback template found",
			},
		}
	}

	unescapedTemplate = strings.ReplaceAll(unescapedTemplate, "\\n", "\n")
	unescapedTemplate = strings.ReplaceAll(unescapedTemplate, "\\\"", "\"")
	unescapedTemplate = strings.ReplaceAll(unescapedTemplate, "\\\\", "\\")

	var tmpl string

	json.Unmarshal([]byte(unescapedTemplate), &tmpl)

	// for key, val := range value {
	// 	res[key] = val.(string)
	// }

	return unescapedTemplate, nil
}
