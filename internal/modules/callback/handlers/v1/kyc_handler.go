package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/callback/template"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"
)

func (h *<PERSON><PERSON>) KycHandler(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Process the request and apply templates

	var productId string
	productId, ok := ctx.GinCtx.Params.Get("product_id")
	if !ok {
		productId = ctx.GinCtx.Query("product_id")
		if productId == "" {
			return nil, &response.ErrorResponse{
				Status: &response.Status{
					HttpStatus: http.StatusInternalServerError,
				},
				Problem: &response.Problem{
					Title:  "Internal Server Error",
					Detail: "Product ID is required",
				},
			}
		}
	}

	resp, err := h.fetchTemplate(context.Background(), productId, []string{"kyc_callback"})
	if err != nil {
		return nil, err
	}

	data := template.ProcessTemplateRequest(ctx, resp)

	// Extract Signal and Source from the data

	signal := data.Signal

	if signal == "true" {
		// Call the Kyc Endpoint
		_, err = h.signalKyc(ctx, data)
		if err != nil {
			return nil, err
		}
	}

	return h.RedirectHandler(ctx, data.ProposalID)
}

func (h *Handler) signalKyc(ctx *handler.HandlerContext, data *template.TemplateMetadata) (*proposal.SignalKycResponse, *response.ErrorResponse) {

	log := ctx.Logger
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		log.Error("Failed to connect to proposal service: %v", zap.Error(err))
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed to connect to proposal service",
			},
		}
	}

	client := proposal.NewProposalClient(conn)

	kycData, err := structpb.NewStruct(data.Metadata)
	if err != nil {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed to convert data to structpb.Struct",
			},
		}
	}

	var referenceId string
	if data.KycId != nil {
		referenceId = *data.KycId
	}

	req := proposal.SignalKycRequest{
		InsurerId:      data.InsurerID,
		ProductId:      data.ProductID,
		ProposalId:     data.ProposalID,
		ReferenceKycId: referenceId,
		KycData:        kycData,
	}

	result, err := client.SignalKyc(context.Background(), &req)
	if err != nil {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed to signal kyc",
			},
		}
	}

	return result, nil
}
