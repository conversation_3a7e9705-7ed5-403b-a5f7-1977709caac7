package v1

import (
	"context"
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/callback/template"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"google.golang.org/protobuf/types/known/structpb"
)

func (h *Handler) PaymentHandler(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	// Try To Get Product Id From query params or url params

	var productId string
	productId, ok := ctx.GinCtx.Params.Get("product_id")
	if !ok {
		productId = ctx.GinCtx.Query("product_id")
		if productId == "" {
			return nil, &response.ErrorResponse{
				Status: &response.Status{
					HttpStatus: http.StatusInternalServerError,
				},
				Problem: &response.Problem{
					Title:  "Internal Server Error",
					Detail: "Product ID is required",
				},
			}
		}
	}

	resp, fetchErr := h.fetchTemplate(context.Background(), productId, []string{"payment_callback"})
	if fetchErr != nil {
		return nil, fetchErr
	}

	data := template.ProcessTemplateRequest(ctx, resp)

	fmt.Printf("Data: %+v\n", data)

	// Use signal from template, but fallback to "true" if empty or invalid
	signal := data.Signal
	if signal == "" {
		signal = "true" // Default fallback to maintain existing behavior
	}
	fmt.Printf("Signal from template: %s (using: %s)\n", data.Signal, signal)

	if signal == "true" {
		// Call the Payment Endpoint using the extracted data from template processing
		_, signalErr := h.signalPayment(ctx, data)
		if signalErr != nil {
			return nil, signalErr
		}
	}

	return h.RedirectHandler(ctx, data.ProposalID)
}

func (h *Handler) signalPayment(ctx *handler.HandlerContext, data *template.TemplateMetadata) (*proposal.PaymentCallbackResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed to connect to proposal service",
			},
		}
	}

	client := proposal.NewProposalClient(conn)

	// Create payment data that includes metadata plus transaction ID and success info
	paymentDataMap := make(map[string]interface{})

	// Copy existing metadata
	for k, v := range data.Metadata {
		paymentDataMap[k] = v
	}

	// Determine if we have a transaction ID available
	hasTransactionID := data.InsurerTransactionID != nil && *data.InsurerTransactionID != ""

	// Add transaction ID back to payment data for the sales service if available
	if hasTransactionID {
		paymentDataMap["transaction_id"] = *data.InsurerTransactionID
		fmt.Printf("Transaction ID available: %s\n", *data.InsurerTransactionID)
	} else {
		fmt.Println("No transaction ID available - workflow will fetch latest payment record")
	}

	// Add signal information as success status for the sales service
	paymentDataMap["success"] = data.Signal == "true"

	// Add a structured callback payload for the workflow signal
	callbackPayload := map[string]interface{}{
		"success": data.Signal == "true",
	}

	// Only add transaction_id to callback payload if we have one
	if hasTransactionID {
		callbackPayload["transaction_id"] = *data.InsurerTransactionID
	}

	// Add indication that workflow should be idempotent (fetch latest record if no transaction ID)
	callbackPayload["fetch_latest_payment"] = !hasTransactionID

	paymentDataMap["callback_payload"] = callbackPayload

	// For backward compatibility, ensure success is always true when signal processing occurs
	// This maintains compatibility with existing flows that expect success:true
	if data.Signal == "true" {
		paymentDataMap["success"] = true
		callbackPayload["success"] = true
	}

	fmt.Printf("Payment data being sent to sales service: %+v\n", paymentDataMap)
	fmt.Printf("Callback payload structure: %+v\n", callbackPayload)
	fmt.Printf("Signal value: %s, Success will be: %t, Has Transaction ID: %t\n",
		data.Signal, data.Signal == "true", hasTransactionID)

	paymentData, err := structpb.NewStruct(paymentDataMap)
	if err != nil {
		fmt.Printf("Error converting payment data to structpb: %v\n", err)
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed to convert data to structpb.Struct",
			},
		}
	}

	req := proposal.PaymentCallbackRequest{
		InsurerId:            data.InsurerID,
		ProductId:            data.ProductID,
		ProposalId:           data.ProposalID,
		PaymentData:          paymentData,
		InsurerTransactionId: data.InsurerTransactionID, // This might be nil, which is fine
	}

	fmt.Printf("gRPC request being sent: InsurerId=%s, ProductId=%s, ProposalId=%s, InsurerTransactionId=%v\n",
		req.InsurerId, req.ProductId, req.ProposalId, req.InsurerTransactionId)

	result, err := client.PaymentCallback(context.Background(), &req)
	if err != nil {
		fmt.Printf("Error in signal payment: %v\n", err)
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusInternalServerError,
			},
			Problem: &response.Problem{
				Title:  "Internal Server Error",
				Detail: "Failed to signal payment",
			},
		}
	}

	return result, nil
}
