package v1

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/proposal"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (h *Handler) RedirectHandler(ctx *handler.HandlerContext, proposalId string) (*response.SuccessResponse, *response.ErrorResponse) {

	token, err := h.GenerateJWTToken(ctx, proposalId)
	if err != nil {
		return nil, err
	}

	baseURL := config.GetConfig().PUBLIC_BASE_URL
	redirectURL := fmt.Sprintf("%scustomer/health/summary/%s?payment=completed", baseURL, token)

	fmt.Println("Redirecting to:", redirectURL)

	ctx.GinCtx.Redirect(http.StatusMovedPermanently, redirectURL)
	return &response.SuccessResponse{
		Status: http.StatusMovedPermanently,
	}, nil
}

func (h *Handler) GetPublicTokenData(ctx *handler.HandlerContext, proposalId string) (*proposal.GetPublicTokenDataResponse, *response.ErrorResponse) {
	conn, err := h.GrpcClients.Get(proposal.Proposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := proposal.NewProposalClient(conn)

	resp, err := client.GetPublicTokenData(context.Background(), &proposal.GetPublicTokenDataRequest{
		ProposalId: proposalId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting public token data", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return resp, nil
}

func (h *Handler) GenerateJWTToken(ctx *handler.HandlerContext, proposalId string) (string, *response.ErrorResponse) {
	// Get public token data from the proposal service
	tokenData, errResp := h.GetPublicTokenData(ctx, proposalId)
	if errResp != nil {
		return "", errResp
	}

	// Connect to authentication service
	conn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting authentication client", zap.Error(err))
		return "", response.NewInternalServerError()
	}

	// Create authentication client
	authClient := auth.NewAuthClient(conn)

	// Create token request with appropriate client credentials
	customClaims := map[string]string{
		"purchase_intent_id": tokenData.GetPurchaseIntentId(),
		"submission_id":      tokenData.GetSubmissionId(),
		"enterprise_id":      tokenData.GetEnterpriseId(),
		"sub":                tokenData.GetPartnerId(),
		"proposal_intent_id": tokenData.GetProposalIntentId(),
	}

	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", tokenData.GetEnterpriseId())

	// Call authentication service to create JWT token
	tokenResponse, err := authClient.GenerateJWTToken(grpcCtx, &auth.GenerateJWTTokenRequest{
		Audience:     "gateway_public",
		CustomClaims: customClaims,
		ExpiresAt:    timestamppb.New(time.Now().Add(time.Hour * 24)),
	})
	if err != nil {
		ctx.Logger.Error("Error creating JWT token", zap.Error(err))
		return "", response.NewInternalServerError()
	}

	return tokenResponse.GetAccessToken(), nil
}
