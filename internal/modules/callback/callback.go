package callback

import (
	"context"

	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/callback/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/callback/handlers/v1"
)

type CallbackApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &CallbackApp{}
}

// SetAppName returns the name of the application
func (app *CallbackApp) SetAppName() string {
	return "callback"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *CallbackApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	callbackRouter := appContext.Router[constant.HTTP_CALLBACK]

	requireAuth := false

	// Kyc Callback
	callbackRouter.RegisterRoute(ctx, appName, "GET", "/kyc/:insurer_id/:product_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.KycHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.KycCallbackRequest{},
			}),
		},
	)
	callbackRouter.RegisterRoute(ctx, appName, "POST", "/kyc/:insurer_id/:product_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.KycHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.KycCallbackRequest{},
			}),
		},
	)

	callbackRouter.RegisterRoute(ctx, appName, "GET", "/kyc/:insurer_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.KycHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.KycCallbackWithoutProductIDRequest{},
			}),
		},
	)
	callbackRouter.RegisterRoute(ctx, appName, "POST", "/kyc/:insurer_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.KycHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.KycCallbackWithoutProductIDRequest{},
			}),
		},
	)

	// Payment Callbacks
	callbackRouter.RegisterRoute(ctx, appName, "GET", "/payment/:insurer_id/:product_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.PaymentHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.PaymentCallbackRequest{},
			}),
		},
	)
	callbackRouter.RegisterRoute(ctx, appName, "POST", "/payment/:insurer_id/:product_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.PaymentHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.PaymentCallbackRequest{},
			}),
		},
	)

	callbackRouter.RegisterRoute(ctx, appName, "GET", "/payment/:insurer_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.PaymentHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.PaymentCallbackWithoutProductIDRequest{},
			}),
		},
	)
	callbackRouter.RegisterRoute(ctx, appName, "POST", "/payment/:insurer_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.PaymentHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.PaymentCallbackWithoutProductIDRequest{},
			}),
		},
	)

	callbackRouter.RegisterRoute(ctx, appName, "GET", "/payment/:insurer_id/:product_id/:proposal_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.PaymentHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.PaymentCallbackWithProposalIDRequest{},
			}),
		},
	)
	callbackRouter.RegisterRoute(ctx, appName, "POST", "/payment/:insurer_id/:product_id/:proposal_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.PaymentHandler,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				ParamStruct:       &dto.PaymentCallbackWithProposalIDRequest{},
			}),
		},
	)
}
