package template

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"
	"text/template"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
)

// processTemplateRequest handles the entire template processing workflow:
// 1. Extracts data from the request
// 2. Defines templates
// 3. Applies templates to the data
// 4. Returns the processed results

type TemplateMetadata struct {
	Signal               string                 `json:"signal"`
	Source               string                 `json:"source"`
	InsurerID            string                 `json:"insurer_id"`
	ProductID            string                 `json:"product_id"`
	ProposalID           string                 `json:"proposal_id"`
	KycId                *string                `json:"kyc_id"`
	Metadata             map[string]interface{} `json:"metadata"`
	InsurerTransactionID *string                `protobuf:"bytes,5,opt,name=insurer_transaction_id,json=insurerTransactionId,proto3,oneof" json:"insurer_transaction_id,omitempty"`
}

func ProcessTemplateRequest(ctx *handler.HandlerContext, templateString string) *TemplateMetadata {
	// Extract URL query parameters
	queryParams := make(map[string]interface{})
	for key, values := range ctx.GinCtx.Request.URL.Query() {
		if len(values) > 0 {
			queryParams[key] = values[0]
		} else {
			queryParams[key] = ""
		}
	}

	// Extract URL path parameters
	pathParams := make(map[string]interface{})
	for _, param := range ctx.GinCtx.Params {
		pathParams[param.Key] = param.Value
	}

	// Extract the body for POST/PUT requests
	var requestBody map[string]interface{}
	requestBody = make(map[string]interface{})

	// Add a separate map for form data
	formData := make(map[string]interface{})

	if ctx.GinCtx.Request.Method == "POST" || ctx.GinCtx.Request.Method == "PUT" {
		contentType := ctx.GinCtx.Request.Header.Get("Content-Type")
		// split the contentType by ; and check if the first part is application/x-www-form-urlencoded or multipart/form-data
		contentTypeParts := strings.Split(contentType, ";")

		if strings.Contains(contentTypeParts[0], "application/x-www-form-urlencoded") {
			// Handle form-encoded data
			if err := ctx.GinCtx.Request.ParseForm(); err != nil {
				fmt.Println("Error parsing form data:", err)
			} else {
				for key, values := range ctx.GinCtx.Request.Form {
					if len(values) > 0 {
						formData[key] = values[0]
					} else {
						formData[key] = ""
					}
				}
				fmt.Printf("Parsed form data: %+v\n", formData) // Log form data
			}
		} else if strings.Contains(contentTypeParts[0], "multipart/form-data") {
			// Handle form-encoded data
			if form, err := ctx.GinCtx.MultipartForm(); err != nil {
				fmt.Println("Error parsing form data:", err)
			} else {
				for key, values := range form.Value {
					if len(values) > 0 {
						formData[key] = values[0]
					} else {
						formData[key] = ""
					}
				}
				fmt.Printf("Parsed multipart form data: %+v\n", formData) // Log form data
			}
		} else {
			// For JSON and other body types
			err := ctx.ExtractHttpRequest(&requestBody)
			if err != nil {
				fmt.Println("Error Extracting request body:", err)
			} else {
				fmt.Printf("Parsed request body: %+v\n", requestBody) // Log request body
			}
		}
	}

	// Combine all data into a single template data structure
	data := map[string]interface{}{
		"url": map[string]interface{}{
			"query":  queryParams,
			"params": pathParams,
		},
		"body":    requestBody,
		"form":    formData,
		"headers": ctx.GinCtx.Request.Header,
		"method":  ctx.GinCtx.Request.Method,
	}

	fmt.Printf("Template data prepared: %+v\n", data) // Log template data

	// Define custom functions for the templates
	funcMap := template.FuncMap{
		"default": func(defaultVal, val interface{}) interface{} {
			if val == nil || val == "" {
				return defaultVal
			}
			return val
		},
		"join":         strings.Join,
		"hasPrefix":    strings.HasPrefix,
		"hasSuffix":    strings.HasSuffix,
		"contains":     strings.Contains,
		"lower":        strings.ToLower,
		"upper":        strings.ToUpper,
		"findFirstKey": FindFirstKey,
		// Add more custom functions as needed
	}

	// Process each template and collect results
	tmpl, err := template.New("template").Funcs(funcMap).Parse(templateString)
	if err != nil {
		fmt.Printf("Error parsing template '%s': %v\n", templateString, err)
		return nil
	}

	// Execute the template
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		fmt.Printf("Error executing template: %v\n", err)
	}

	fmt.Printf("Template execution result: %s\n", buf.String()) // Log template result

	//unmarshal this string into a map[string]interface{}
	var result TemplateMetadata
	if err := json.Unmarshal(buf.Bytes(), &result); err != nil {
		fmt.Printf("Error unmarshalling template result: %v\n", err)
	} else {
		// Handle cases where transaction_id exists in metadata
		if result.Metadata != nil {
			if transactionID, ok := result.Metadata["transaction_id"].(string); ok && transactionID != "" {
				result.InsurerTransactionID = &transactionID
				// Keep transaction_id in metadata as well for the sales service
				fmt.Printf("Set InsurerTransactionID from metadata: %s\n", transactionID)
			} else {
				fmt.Println("No valid transaction_id found in metadata")
			}
		}

		// Handle signal field - if empty, default to "true" for payment callbacks to maintain functionality
		// This ensures that even when no transaction ID is present, the callback still triggers the workflow
		if result.Signal == "" {
			result.Signal = "true"
			fmt.Println("Signal was empty, defaulting to 'true' to ensure workflow processing")
		}

		// Ensure robust callback processing even without transaction ID
		// For payment callbacks without transaction ID, the workflow will fetch the latest payment record
		fmt.Printf("Final template metadata: %+v\n", result) // Log final result
	}

	return &result
}

// TATA Utility function to find the first key in a map[string]interface{}
func FindFirstKey(m map[string]interface{}) string {
	for key, _ := range m {
		return key
	}
	return ""
}
