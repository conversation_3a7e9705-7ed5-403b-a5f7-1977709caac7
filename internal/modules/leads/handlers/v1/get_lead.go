package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/leads/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	leads "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/leads"
	"go.uber.org/zap"
)

func (h *Handler) GetLead(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(leads.Leads_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := leads.NewLeadsClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	LeadID := ctx.GinCtx.Query("lead_id")

	LeadsTable, err := client.GetLeadDetails(grpcCtx.(context.Context), &leads.GetLeadDetailsRequest{
		LeadId: LeadID,
	})
	if err != nil {
		ctx.Logger.Error("Error getting leads table", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Transform the response to match the DTO structure
	leadResponse := &dto.GetLeadResponse{
		Success: LeadsTable.Success,
		Lead: dto.LeadDef{
			ID:      LeadsTable.Lead.Id,
			VisitID: LeadsTable.Lead.VisitId,
			Name:    LeadsTable.Lead.Name,
			Email:   LeadsTable.Lead.Email,
			Phone:   LeadsTable.Lead.Phone,
			Source:  LeadsTable.Lead.Source,
		},
		Quotes: make([]dto.Quote, len(LeadsTable.Quotes)),
	}

	// Transform quotes
	for i, quote := range LeadsTable.Quotes {
		purchaseIntents := make([]dto.PurchaseIntent, len(quote.PurchaseIntents))
		for j, intent := range quote.PurchaseIntents {
			purchaseIntents[j] = dto.PurchaseIntent{
				PurchaseIntentID: intent.PurchaseIntentId,
				VariantName:      intent.VariantName,
				ProductName:      intent.ProductName,
				SumInsured:       intent.SumInsured,
				NextAction:       intent.NextAction.String(),
			}
		}

		leadResponse.Quotes[i] = dto.Quote{
			ID:                   quote.Id,
			MemberCombination:    quote.MemberCombination,
			DateOfRecommendation: quote.DateOfRecommendation,
			SumInsured:           quote.SumInsured,
			Vertical:             quote.Vertical,
			PlanType:             quote.PlanType.String(),
			PurchaseIntents:      purchaseIntents,
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: leadResponse,
	}, nil
}
