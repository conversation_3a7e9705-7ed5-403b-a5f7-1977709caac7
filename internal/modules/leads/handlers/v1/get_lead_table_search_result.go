package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	leads "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/leads"
	"go.uber.org/zap"
)

func (h *Handler) GetLeadTableSearchResult(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	searchQuery := ctx.GetQuery("search_query")

	conn, err := h.GrpcClients.Get(leads.Leads_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting leads client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := leads.NewLeadsClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Create the request for gRPC service
	grpcReq := &leads.GetLeadsTableSearchResultsRequest{
		SearchQuery: searchQuery,
	}

	// Call the gRPC service
	res, err := client.GetLeadsTableSearchResults(grpcCtx.(context.Context), grpcReq)

	if err != nil {
		ctx.Logger.Error("Error sending search results", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	ctx.Logger.Info("Service name", zap.String("name", leads.Leads_ServiceDesc.ServiceName))

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
