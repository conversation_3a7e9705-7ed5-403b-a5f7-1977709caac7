package v1

import (
	"context"
	"net/http"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/leads/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-gateway-svc/internal/utility"
	leads "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/leads"
	"go.uber.org/zap"
)

func (h *Handler) GetLeadsTable(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(leads.Leads_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := leads.NewLeadsClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Check For Limit and conver to int32
	limit := ctx.GinCtx.Query("limit")
	if limit == "" {
		limit = "10"
	}

	parsedLimit, err := strconv.ParseInt(limit, 10, 32)
	if err != nil {
		ctx.Logger.Error("Error converting limit to int32", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	limitInt := int32(parsedLimit)

	startDate := ctx.GinCtx.Query("start_date")
	endDate := ctx.GinCtx.Query("end_date")
	lastLeadId := ctx.GinCtx.Query("last_lead_id")
	firstLeadId := ctx.GinCtx.Query("first_lead_id")
	source := ctx.GinCtx.Query("source")

	LeadsTable, err := client.GetLeadsTable(grpcCtx.(context.Context), &leads.GetLeadsTableRequest{
		Limit:       &limitInt,
		StartDate:   &startDate,
		EndDate:     &endDate,
		LastLeadId:  &lastLeadId,
		FirstLeadId: &firstLeadId,
		Source:      &source,
	})
	if err != nil {
		ctx.Logger.Error("Error getting leads table", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	partnerIds := make([]string, 0)
	for _, lead := range LeadsTable.Leads {
		if lead.PartnerId != "" {
			partnerIds = append(partnerIds, lead.PartnerId)
		}
	}

	role := ctx.GinCtx.GetString("role")
	enterpriseType := ctx.GinCtx.GetString("enterprise_type")

	partnerMap, err := utility.GetPartnerEnterpriseName(
		grpcCtx.(context.Context),
		h.GrpcClients,
		ctx.Logger,
		role,
		enterpriseType,
		partnerIds,
	)
	if err != nil {
		ctx.Logger.Error("Error getting partner enterprise names", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	MapLeads := make([]dto.Lead, 0)
	for _, lead := range LeadsTable.Leads {
		leadMap := dto.Lead{
			ID:     *lead.Id,
			Name:   lead.Name,
			Email:  lead.Email,
			Phone:  lead.Phone,
			Source: lead.Source,
		}

		if partnerInfo, exists := partnerMap[lead.PartnerId]; exists {
			leadMap.PartnerName = partnerInfo.PartnerName
			leadMap.EnterpriseName = partnerInfo.EnterpriseName
		}
		MapLeads = append(MapLeads, leadMap)
	}

	res := dto.LeadsTableResponse{
		Success:     true,
		Leads:       MapLeads,
		LastLeadID:  LeadsTable.LastLeadId,
		StartLeadID: LeadsTable.StartLeadId,
		HasPrevious: LeadsTable.HasPrevious,
		HasNext:     LeadsTable.HasNext,
		TotalCount:  int64(LeadsTable.Count),
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
