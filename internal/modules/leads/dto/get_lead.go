package dto

// // }

type GetLeadRequest struct {
	LeadID string `form:"lead_id"`
}

type GetLeadResponse struct {
	Success bool    `json:"success"`
	Lead    LeadDef `json:"lead"`
	Quotes  []Quote `json:"quotes"`
}

type LeadDef struct {
	ID      *string `json:"id,omitempty"`
	VisitID *string `json:"visit_id,omitempty"`
	Name    string  `json:"name"`
	Email   string  `json:"email"`
	Phone   string  `json:"phone"`
	Source  string  `json:"source"`
}

type Quote struct {
	ID                   string           `json:"id"`
	MemberCombination    string           `json:"member_combination"`
	DateOfRecommendation string           `json:"date_of_recommendation"`
	SumInsured           string           `json:"sum_insured"`
	Vertical             string           `json:"vertical"`
	PlanType             string           `json:"plan_type"`
	PurchaseIntents      []PurchaseIntent `json:"purchase_intents"`
}

type PurchaseIntent struct {
	PurchaseIntentID string `json:"purchase_intent_id"`
	VariantName      string `json:"variant_name"`
	ProductName      string `json:"product_name"`
	SumInsured       int64  `json:"sum_insured"`
	NextAction       string `json:"next_action"`
}
