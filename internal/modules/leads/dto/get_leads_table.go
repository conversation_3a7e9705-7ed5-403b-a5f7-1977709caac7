package dto

// // CreateClientRequest represents the request to create a new client
// type GetProposalTableRequest struct {
// 	EnterpriseID string `json:"enterprise_id" validate:"required"`
// }

// // CreateClientResponse represents the response after creating a new client
// // type CreateClientResponse struct {
// // 	EnterpriseID string `json:"enterprise_id"`
// // 	ClientID     string `json:"client_id"`
// // 	ClientSecret string `json:"client_secret"` // Plain text secret, only returned once
// // 	IsEnabled    bool   `json:"is_enabled"`
// // }

type GetLeadsTableRequest struct {
	Limit       int    `form:"limit"`
	StartDate   string `form:"start_date"`
	EndDate     string `form:"end_date"`
	LastLeadID  string `form:"last_lead_id"`
	FirstLeadID string `form:"first_lead_id"`
	Source      string `form:"source"`
}

type Lead struct {
	Name           string `json:"name"`
	Email          string `json:"email"`
	Phone          string `json:"phone"`
	Source         string `json:"source"`
	ID             string `json:"id"`
	PartnerName    string `json:"partner_name"`
	EnterpriseName string `json:"enterprise_name"`
}

type LeadsTableResponse struct {
	Success     bool   `json:"success"`
	Leads       []Lead `json:"leads"`
	LastLeadID  string `json:"last_lead_id"`
	StartLeadID string `json:"start_lead_id"`
	HasPrevious bool   `json:"has_previous"`
	HasNext     bool   `json:"has_next"`
	TotalCount  int64  `json:"total_count"`
}
