package term_proposal

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_proposal/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_proposal/handlers/v1"
)

type ProposalApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &ProposalApp{}
}

// SetAppName returns the name of the application
func (app *ProposalApp) SetAppName() string {
	return "term_proposal"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *ProposalApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	apiRouter := appContext.Router[constant.HTTP_API]

	requireAuth := false
	apiRouter.RegisterRoute(ctx, appName, "GET", "/",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetTermProposalTable,
				QueryStruct:       &dto.GetTermProposalTableRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"proposal.get-proposal-table",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/search",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetTermProposalTableSearchResult,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"proposal.get-proposal-table",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/:proposal_intent_id/lifecycle",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetTermProposalLifecycle,
				ParamStruct:       &dto.GetTermProposalLifecycleRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"enterprise_id",
						"sub",
					}, "gateway_portal", []string{
						"proposal.get-proposal-lifecycle",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/summary",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetTermProposalPublicData,
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				QueryStruct:       &dto.GetTermProposalPublicDataRequest{},
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id", "submission_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"proposal.get-summary",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/token/:purchase_intent_id/:submission_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h1.GetPublicTokenHandler,
				ParamStruct:       &dto.GetPublicTokenRequest{},
				RequireAuth:       &requireAuth,
				RequireValidation: true,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"proposal.get-public-token",
					}),
				},
			}),
		},
	)
}
