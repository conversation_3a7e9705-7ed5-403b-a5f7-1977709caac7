package v1

import (
	"context"
	"net/http"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_proposal/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	term_proposal "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/term_proposal"
	"go.uber.org/zap"
)

func (h *Handler) GetTermProposalTable(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(term_proposal.TermProposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting term proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := term_proposal.NewTermProposalClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Check For Limit and convert to int32
	limit := ctx.GinCtx.Query("limit")
	if limit == "" {
		limit = "10"
	}

	parsedLimit, err := strconv.ParseInt(limit, 10, 32)
	if err != nil {
		ctx.Logger.Error("Error converting limit to int32", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	limitInt := int32(parsedLimit)

	startDate := ctx.GinCtx.Query("start_date")
	endDate := ctx.GinCtx.Query("end_date")
	lastProposalId := ctx.GinCtx.Query("last_proposal_id")
	status := ctx.GinCtx.Query("status")
	firstProposalId := ctx.GinCtx.Query("first_proposal_id")

	res, err := client.GetProposalTable(grpcCtx.(context.Context), &term_proposal.GetProposalTableRequest{
		Limit:           &limitInt,
		StartDate:       &startDate,
		EndDate:         &endDate,
		LastProposalId:  &lastProposalId,
		StatusFilter:    &status,
		FirstProposalId: &firstProposalId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting term proposal table", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	termProposals := make([]dto.TermProposalTableData, len(res.GetProposals()))
	for i, proposal := range res.GetProposals() {
		termProposals[i] = dto.TermProposalTableData{
			ProposalIntentID: proposal.GetProposalIntentId(),
			ProposalID:       proposal.GetProposalId(),
			Name:             proposal.GetName(),
			Phone:            proposal.GetPhone(),
			Email:            proposal.GetEmail(),
			InsurerName:      proposal.GetInsurerName(),
			ProductName:      proposal.GetProductName(),
			VariantName:      proposal.GetVariantName(),
			Tenure:           proposal.GetTenure(),
			NetPremium:       proposal.GetNetPremium(),
			TotalPremium:     proposal.GetTotalPremium(),
			Status:           proposal.GetStatus(),
			SubmissionID:     proposal.GetSubmissionId(),
			CreatedAt:        proposal.GetCreatedAt(),
			UpdatedAt:        proposal.GetUpdatedAt(),
			StatusCreatedAt:  proposal.GetStatusCreatedAt(),
			ReferenceID:      proposal.GetReferenceId(),
			PurchaseIntentID: proposal.GetPurchaseIntentId(),
			PartnerName:      "", // Partner name field may not exist in term proposals protobuf
			EnterpriseName:   "", // Enterprise name field may not exist in term proposals protobuf
		}

		// Handle optional insurer proposal ID
		if proposal.GetInsurerProposalId() != "" {
			termProposals[i].InsurerProposalID = proposal.InsurerProposalId
		}
	}

	responseData := &dto.GetTermProposalTableResponse{
		Status:          "success",
		Message:         "Term proposals retrieved successfully",
		Proposals:       termProposals,
		CreatedAt:       res.GetCreatedAt(),
		LastProposalID:  res.GetLastProposalId(),
		FirstProposalID: res.GetFirstProposalId(),
		HasNext:         res.GetHasNext(),
		HasPrevious:     res.GetHasPrevious(),
		Count:           res.GetCount(),
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: responseData,
	}, nil
}
