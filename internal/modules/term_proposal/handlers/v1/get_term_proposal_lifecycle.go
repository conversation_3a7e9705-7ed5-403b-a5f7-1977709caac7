package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	term_proposal "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/term_proposal"
	"go.uber.org/zap"
)

func (h *Handler) GetTermProposalLifecycle(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(term_proposal.TermProposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting term proposal client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := term_proposal.NewTermProposalClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	proposalIntentId := ctx.GinCtx.Param("proposal_intent_id")
	submissionId := ctx.GetQuery("submission_id")
	res, err := client.GetProposalLifecycle(grpcCtx.(context.Context), &term_proposal.GetProposalLifecycleRequest{
		ProposalIntentId: &proposalIntentId,
		SubmissionId:     &submissionId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting term proposal lifecycle", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Convert the response to a map structure for easier manipulation
	responseMap := map[string]interface{}{
		"proposal_intent_id": res.GetProposalIntentId(),
		"name":               res.GetName(),
		"phone":              res.GetPhone(),
		"company_name":       res.GetCompanyName(),
		"product_name":       res.GetProductName(),
		"variant_name":       res.GetVariantName(),
		"sum_insured":        res.GetSumInsured(),
		"total_premium":      res.GetTotalPremium(),
		"total_gst":          res.GetTotalGst(),
		"total_amount":       res.GetTotalAmount(),
		"submission_id":      res.GetSubmissionId(),
		"purchase_intent_id": res.GetPurchaseIntentId(),
		"proposal_status":    res.GetProposalStatus().String(), // Convert enum to string
		"insurer_logo":       res.GetLogoUrl(),
		"rejection_reason":   res.GetRejectionReason(),
		"insurer_id":         res.GetInsurerId(),
		"product_id":         res.GetProductId(),
		"proposal_id":        res.GetProposalId(),
		"partner_id":         res.GetPartnerId(),
	}

	// Process the info data structure with application and proposal steps
	if info := res.GetInfo(); info != nil {
		lifecycleInfo := map[string]interface{}{}

		// Process application step if present
		if app := info.GetApplication(); app != nil {
			appStep := map[string]interface{}{}
			if stepInfo := app.GetStepInfo(); stepInfo != nil {
				appStep["step_info"] = map[string]interface{}{
					"status":     stepInfo.GetStatus().String(),
					"created_at": stepInfo.GetCreatedAt(),
				}
				if nextStep := stepInfo.GetNextStep(); nextStep != "" {
					appStep["step_info"].(map[string]interface{})["next_step"] = nextStep
				}
			}
			if meta := app.GetMetadata(); meta != nil {
				appStep["metadata"] = map[string]interface{}{
					"latest_proposal_id": meta.GetLatestProposalId(),
				}
			}
			lifecycleInfo["application"] = appStep
		}

		// Process proposal step if present
		if prop := info.GetProposal(); prop != nil {
			propStep := map[string]interface{}{}
			if stepInfo := prop.GetStepInfo(); stepInfo != nil {
				propStep["step_info"] = map[string]interface{}{
					"status":     stepInfo.GetStatus().String(),
					"created_at": stepInfo.GetCreatedAt(),
				}
				if nextStep := stepInfo.GetNextStep(); nextStep != "" {
					propStep["step_info"].(map[string]interface{})["next_step"] = nextStep
				}
			}
			if meta := prop.GetMetadata(); meta != nil {
				metadataMap := map[string]interface{}{}
				if insurerProposalId := meta.GetInsurerProposalId(); insurerProposalId != "" {
					metadataMap["insurer_proposal_id"] = insurerProposalId
				}
				if len(metadataMap) > 0 {
					propStep["metadata"] = metadataMap
				}
			}
			lifecycleInfo["proposal"] = propStep
		}

		responseMap["info"] = lifecycleInfo
	} else {
		responseMap["info"] = map[string]interface{}{
			"message": "No lifecycle info available",
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: responseMap,
	}, nil
}
