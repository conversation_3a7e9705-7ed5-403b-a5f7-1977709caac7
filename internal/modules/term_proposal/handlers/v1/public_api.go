package v1

import (
	"context"
	"net/http"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/proposal/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-gateway-svc/internal/utility"
	catalog_metadata "github.com/oneassure-tech/oa-protos/go/oa-catalog/v0/metadata"
	"github.com/oneassure-tech/oa-protos/go/oa-forms/v0/forms"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"
	term_proposal "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/term_proposal"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

func (h *<PERSON>ler) GetTermProposalPublicData(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}
	decodedToken := make(map[string]interface{})
	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	for _, claim := range []string{"enterprise_id", "partner_id", "purchase_intent_id", "submission_id"} {
		claimValue := md.Get(claim)

		if len(claimValue) == 0 {
			if claim == "partner_id" {
				subClaim := md.Get("sub")
				if len(subClaim) != 0 {
					decodedToken[claim] = subClaim[0]
					continue
				}
			}
			parsedClaim := ctx.GinCtx.Request.URL.Query().Get(claim)
			if parsedClaim != "" {
				decodedToken[claim] = parsedClaim
				continue
			}
			ctx.Logger.Error("Missing required claim", zap.String("claim", claim))
			return nil, response.NewInternalServerError()
		}
		decodedToken[claim] = claimValue[0]
	}

	// Call The Get Proposal Endpoint on the sales to get the proposal data
	proposalData := h.GetTermProposalData(ctx, decodedToken)

	if proposalData == nil {
		// Get Purchase Intent Data
		purchaseIntentData := h.GetTermPurchaseIntentData(ctx, decodedToken)
		if purchaseIntentData == nil {
			return nil, response.NewInternalServerError()
		}
		// Get The Form Config With Form Responses
		formSubmissionId := decodedToken["submission_id"].(string)
		if formSubmissionId == "" {
			formSubmissionId = purchaseIntentData.SubmissionId
		}
		formResponse := h.GetTermFormConfigData(ctx, formSubmissionId, decodedToken)
		if formResponse == nil {
			return nil, response.NewInternalServerError()
		}

		FormResponseData := make([]dto.ResponseData, 0)
		for _, data := range formResponse.GetResponseData() {
			FormResponseData = append(FormResponseData, dto.ResponseData{
				SectionName:  data.GetSectionName(),
				ResponseData: data.GetResponseData().AsMap(),
			})
		}

		// Extract rider data from purchase intent
		riderData := make([]dto.RiderData, 0)
		if purchaseIntentData.Riders != nil {
			for _, rider := range purchaseIntentData.Riders {
				if rider.IsSelected || rider.IsMandatory {
					riderData = append(riderData, dto.RiderData{
						Name:     rider.Name,
						Sequence: int(rider.Sequence),
					})
				}
			}
		}
		consentDocumentURL := h.GetTermConsentDocumentURL(ctx, decodedToken, purchaseIntentData.ProductId)

		res := &dto.GetPublicDataResponse{
			ApplicationForm: &dto.ApplicationForm{
				SubmissionID: formResponse.SubmissionId,
				FormSchema:   formResponse.FormSchema.AsMap(),
				ResponseData: FormResponseData,
				Status:       formResponse.Status.String(),
			},
			Summary: &dto.Summary{
				CustomerName:       purchaseIntentData.CustomerName,
				CustomerPhone:      purchaseIntentData.CustomerPhone,
				CompanyName:        purchaseIntentData.VariantStaticDetails.InsurerName,
				ProductName:        purchaseIntentData.VariantStaticDetails.VariantName,
				VariantName:        purchaseIntentData.VariantStaticDetails.VariantName,
				InsurerLogo:        purchaseIntentData.VariantStaticDetails.InsurerLogo,
				SumInsured:         strconv.FormatInt(purchaseIntentData.SumInsured, 10),
				BasePremium:        strconv.FormatFloat(utility.RoundFloat64(float64(purchaseIntentData.BasePremium), 2), 'f', -1, 64),
				TotalGST:           strconv.FormatFloat(utility.RoundFloat64(float64(purchaseIntentData.TotalGst), 2), 'f', -1, 64),
				TotalAmount:        strconv.FormatFloat(utility.RoundFloat64(float64(purchaseIntentData.TotalPremium), 2), 'f', -1, 64),
				ConsentDocumentURL: consentDocumentURL,
				// Tenure:             strconv.FormatInt(int64(purchaseIntentData.Tenure), 10),
			},
			Riders: riderData,
		}

		return &response.SuccessResponse{
			Status:  http.StatusOK,
			Payload: res,
		}, nil
	}

	// Get The Form Config With Form Responses
	formResponse := h.GetTermFormConfigData(ctx, proposalData.SubmissionID, decodedToken)
	if formResponse == nil {
		return nil, response.NewInternalServerError()
	}

	responseData := make([]dto.ResponseData, 0)
	for _, data := range formResponse.GetResponseData() {
		responseData = append(responseData, dto.ResponseData{
			SectionName:  data.GetSectionName(),
			ResponseData: data.GetResponseData().AsMap(),
		})
	}

	// Extract rider data from proposal
	riderData := make([]dto.RiderData, 0)
	if proposalData.RiderData != nil {
		for _, rider := range proposalData.RiderData {
			riderData = append(riderData, dto.RiderData{
				Name:     rider.Name,
				Sequence: int(rider.Sequence),
			})
		}
	}

	res := &dto.GetPublicDataResponse{
		ApplicationForm: &dto.ApplicationForm{
			FormSchema:   formResponse.FormSchema.AsMap(),
			ResponseData: responseData,
			Status:       formResponse.Status.String(),
			SubmissionID: proposalData.SubmissionID,
		},
		Summary: &dto.Summary{
			CustomerName:  proposalData.Name,
			CustomerPhone: proposalData.Phone,
			CompanyName:   proposalData.CompanyName,
			ProductName:   proposalData.ProductName,
			VariantName:   proposalData.VariantName,
			InsurerLogo:   proposalData.InsurerLogo,
			SumInsured:    proposalData.SumInsured,
			BasePremium:   proposalData.TotalPremium,
			TotalGST:      proposalData.TotalGST,
			TotalAmount:   proposalData.TotalAmount,
			Tenure:        proposalData.Tenure,
		},
		Lifecycle: proposalData.Info,
		Riders:    riderData,
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}

func (h *Handler) GetTermPurchaseIntentData(ctx *handler.HandlerContext, decodedToken map[string]interface{}) *term_recommendation.GetPurchaseIntentResponse {

	conn, err := h.GrpcClients.Get(term_recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil
	}

	client := term_recommendation.NewTermRecommendationClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil
	}

	res, err := client.GetPurchaseIntent(grpcCtx.(context.Context), &term_recommendation.GetPurchaseIntentRequest{
		PurchaseIntentId: decodedToken["purchase_intent_id"].(string),
	})
	if err != nil {
		ctx.Logger.Error("Error getting purchase intent", zap.Error(err))
		return nil
	}

	return res
}

func (h *Handler) GetTermFormConfigData(ctx *handler.HandlerContext, SubmissionId string, decodedToken map[string]interface{}) *forms.FetchFormResponseResponse {

	conn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil
	}

	client := forms.NewFormsServiceClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil
	}

	formResponse, err := client.FetchFormResponse(grpcCtx.(context.Context), &forms.FetchFormResponseRequest{
		SubmissionId:      SubmissionId,
		IncludeFormSchema: true,
	})
	if err != nil {
		ctx.Logger.Error("Error getting form config", zap.Error(err))
		return nil
	}

	return formResponse
}

func (h *Handler) GetTermConsentDocumentURL(ctx *handler.HandlerContext, decodedToken map[string]interface{}, productId string) string {

	conn, err := h.GrpcClients.Get(catalog_metadata.MetadataService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting catalog client", zap.Error(err))
		return ""
	}

	client := catalog_metadata.NewMetadataServiceClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return ""
	}

	metadataRes, err := client.GetMetadata(grpcCtx.(context.Context), &catalog_metadata.GetMetadataRequest{
		Keys:      []string{"common"},
		ProductId: &productId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting consent document url", zap.Error(err))
		return ""
	}
	if len(metadataRes.Metadata) > 0 {
		if commonMetadata := metadataRes.Metadata[0].Value.Fields["consent-document"]; commonMetadata != nil {
			return commonMetadata.GetStringValue()
		}
	}
	return ""
}

func (h *Handler) GetTermProposalData(ctx *handler.HandlerContext, decodedToken map[string]interface{}) *dto.ProposalData {
	conn, err := h.GrpcClients.Get(term_proposal.TermProposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil
	}

	client := term_proposal.NewTermProposalClient(conn)

	submissionId := decodedToken["submission_id"].(string)
	purchaseIntentId := decodedToken["purchase_intent_id"].(string)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil
	}

	proposalRes, err := client.GetProposal(grpcCtx.(context.Context), &term_proposal.GetProposalRequest{
		PurchaseIntentId: purchaseIntentId,
		SubmissionId:     &submissionId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting proposal", zap.Error(err))
		return nil
	}

	if proposalRes.SubmissionId != decodedToken["submission_id"].(string) {
		return nil
	}

	riderData := make([]dto.RiderData, 0)
	if proposalRes.RiderData != nil {
		for index, rider := range proposalRes.RiderData.AsSlice() {
			riderMap, ok := rider.(map[string]interface{})
			if !ok {
				continue
			}
			riderName, ok := riderMap["name"].(string)
			if !ok {
				continue
			}
			sequence, _ := riderMap["sequence"].(int32)
			riderData = append(riderData, dto.RiderData{
				Name:     riderName,
				Sequence: int(sequence) | index,
			})
		}
	}

	res, err := client.GetProposalLifecycle(grpcCtx.(context.Context), &term_proposal.GetProposalLifecycleRequest{
		ProposalIntentId: &proposalRes.ProposalIntentId,
		PurchaseIntentId: &purchaseIntentId,
		SubmissionId:     &submissionId,
	})
	if err != nil {
		ctx.Logger.Error("Error getting proposal lifecycle", zap.Error(err))
		return nil
	}
	// Convert the response to a structured type for safer handling
	proposalData := &dto.ProposalData{
		ProposalIntentID: res.GetProposalIntentId(),
		Name:             res.GetName(),
		Phone:            res.GetPhone(),
		CompanyName:      res.GetCompanyName(),
		ProductName:      res.GetProductName(),
		VariantName:      res.GetVariantName(),
		SumInsured:       res.GetSumInsured(),
		TotalPremium:     res.GetTotalAmount(),
		TotalGST:         res.GetTotalGst(),
		TotalAmount:      res.GetTotalPremium(),
		SubmissionID:     res.GetSubmissionId(),
		PurchaseIntentID: res.GetPurchaseIntentId(),
		ProposalStatus:   res.GetProposalStatus().String(),
		InsurerLogo:      res.GetLogoUrl(),
		RejectionReason:  res.GetRejectionReason(),
		RiderData:        riderData,
		Tenure:           res.GetTenure(),
	}

	// Process the info data structure with all the steps
	if info := res.GetInfo(); info != nil {
		lifecycleInfo := &dto.LifecycleInfo{
			CustomerConsent:  proposalRes.GetCustomerConsent(),
			RejectionReason:  proposalRes.GetRejectionReason(),
			ProposalIntentID: proposalRes.GetProposalIntentId(),
			PurchaseIntentID: purchaseIntentId,
			InsurerID:        res.GetInsurerId(),
			ProductID:        res.GetProductId(),
			ProposalID:       res.GetProposalId(),
		}

		// Process application step if present
		if app := info.GetApplication(); app != nil {
			appStep := &dto.ApplicationStep{}
			if stepInfo := app.GetStepInfo(); stepInfo != nil {
				appStep.StepInfo = &dto.StepInfo{
					Status:    stepInfo.GetStatus().String(),
					CreatedAt: stepInfo.GetCreatedAt(),
				}
				if nextStep := stepInfo.GetNextStep(); nextStep != "" {
					appStep.StepInfo.NextStep = nextStep
				}
			}
			if meta := app.GetMetadata(); meta != nil {
				appStep.Metadata.LatestProposalID = meta.GetLatestProposalId()
			}
			lifecycleInfo.Application = appStep
		}

		// Process proposal step if present
		if prop := info.GetProposal(); prop != nil {
			propStep := &dto.ProposalStep{}
			if stepInfo := prop.GetStepInfo(); stepInfo != nil {
				propStep.StepInfo = &dto.StepInfo{
					Status:    stepInfo.GetStatus().String(),
					CreatedAt: stepInfo.GetCreatedAt(),
				}
				if nextStep := stepInfo.GetNextStep(); nextStep != "" {
					propStep.StepInfo.NextStep = nextStep
				}
			}
			if meta := prop.GetMetadata(); meta != nil {
				if insurerProposalId := meta.GetInsurerProposalId(); insurerProposalId != "" {
					propStep.Metadata.InsurerProposalID = insurerProposalId
				}
				if redirectUrl := meta.GetRedirectUrl(); redirectUrl != "" {
					propStep.Metadata.RedirectURL = redirectUrl
				}
			}
			lifecycleInfo.Proposal = propStep
		}

		proposalData.Info = lifecycleInfo
	}

	return proposalData
}
