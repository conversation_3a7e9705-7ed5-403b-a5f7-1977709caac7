package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	term_proposal "github.com/oneassure-tech/oa-protos/go/oa-sales/v0/term_proposal"
	"go.uber.org/zap"
)

func (h *Handler) GetTermProposalTableSearchResult(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	searchQuery := ctx.GetQuery("search_query")

	conn, err := h.GrpcClients.Get(term_proposal.TermProposal_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting proposals client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := term_proposal.NewTermProposalClient(conn)

	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	// Create the request for gRPC service
	grpcReq := &term_proposal.GetProposalTableSearchResultsRequest{
		SearchQuery: searchQuery,
	}

	// Call the gRPC service
	res, err := client.GetProposalTableSearchResults(grpcCtx.(context.Context), grpcReq)

	if err != nil {
		ctx.Logger.Error("Error sending search results", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	ctx.Logger.Info("Service name", zap.String("name", term_proposal.TermProposal_ServiceDesc.ServiceName))

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
