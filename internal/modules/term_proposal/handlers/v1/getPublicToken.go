package v1

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/term_proposal/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"github.com/oneassure-tech/oa-protos/go/oa-rapidshort/v0/rapidshort"
	term_recommendation "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/term_recommendation"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// GetPublicTokenHandler is an adapter that conforms to CustomHandler type
func (h *Handler) GetPublicTokenHandler(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	submissionId := ctx.GetParam("submission_id")
	purchaseIntentId := ctx.GetParam("purchase_intent_id")
	if submissionId == "" || purchaseIntentId == "" {
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Type:   "invalid_request",
				Title:  "Invalid Request",
				Detail: "Missing proposal ID",
			},
		}
	}
	return h.GetPublicToken(ctx, &dto.GetPublicTokenRequest{
		SubmissionId:     submissionId,
		PurchaseIntentId: purchaseIntentId,
	})
}

func (h *Handler) GetPublicToken(ctx *handler.HandlerContext, request *dto.GetPublicTokenRequest) (*response.SuccessResponse, *response.ErrorResponse) {
	grpcCtx, ok := ctx.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		ctx.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}
	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	if !ok {
		ctx.Logger.Error("Failed to extract metadata from context")
		return nil, response.NewInternalServerError()
	}

	// Connect to authentication service
	authConn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting authentication client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	termRecommendationConn, err := h.GrpcClients.Get(term_recommendation.TermRecommendation_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting term recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	termRecommendationClient := term_recommendation.NewTermRecommendationClient(termRecommendationConn)

	// Get the enterprise id from the metadata
	rapidshortConn, err := h.GrpcClients.Get(rapidshort.RapidShortService_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting rapidshort client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Create authentication client
	authClient := auth.NewAuthClient(authConn)

	partnerId := md.Get("sub")
	if len(partnerId) == 0 {
		partnerId = md.Get("partner_id")
	}

	// Create token request with appropriate client credentials
	customClaims := map[string]string{
		"purchase_intent_id": request.PurchaseIntentId,
		"submission_id":      request.SubmissionId,
		"enterprise_id":      md.Get("enterprise_id")[0],
		"sub":                partnerId[0],
	}

	recommendationResponse, err := termRecommendationClient.GetPurchaseIntent(grpcCtx.(context.Context), &term_recommendation.GetPurchaseIntentRequest{
		PurchaseIntentId: request.PurchaseIntentId,
	})

	if err != nil {
		ctx.Logger.Error("Error getting term recommendation", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	//  := recommendationResponse.GettenureTenure()
	fmt.Println(recommendationResponse, "abcd")

	// expiry := time.Now().Add(time.Duration(tenure) * time.Hour * 24 * 365)
	expiry := time.Now().Add(time.Duration(10) * time.Hour * 24 * 365)

	// Call authentication service to create JWT token
	tokenResponse, err := authClient.GenerateJWTToken(grpcCtx.(context.Context), &auth.GenerateJWTTokenRequest{
		Audience:     "gateway_public",
		CustomClaims: customClaims,
		ExpiresAt:    timestamppb.New(expiry),
	})
	if err != nil {
		ctx.Logger.Error("Error creating JWT token", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	rapidshortClient := rapidshort.NewRapidShortServiceClient(rapidshortConn)

	fmt.Println(ctx.GinCtx.Request.Header.Get("Origin"))

	res, err := rapidshortClient.CreateShortUrl(ctx.GinCtx, &rapidshort.CreateShortUrlRequest{
		LongUrl: fmt.Sprintf(
			"%s/customer/term/summary/%s",
			ctx.GinCtx.Request.Header.Get("Origin"),
			tokenResponse.GetAccessToken(),
		),
		RedirectionCode: "302",
	})
	if err != nil {
		ctx.Logger.Error("Error getting short url", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetPublicTokenResponse{
			Token:    tokenResponse.GetAccessToken(),
			ShortUrl: fmt.Sprintf("%s/%s", "https://oasr.in", res.GetShortCode()),
		},
	}, nil
}
