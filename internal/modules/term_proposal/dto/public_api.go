package dto

type GetTermProposalPublicDataRequest struct {
	SubmissionId     string `form:"submission_id"`
	PurchaseIntentId string `form:"purchase_intent_id"`
}

type Summary struct {
	CustomerName       string `json:"customer_name"`
	CustomerPhone      string `json:"customer_phone"`
	CompanyName        string `json:"company_name"`
	ProductName        string `json:"product_name"`
	VariantName        string `json:"variant_name"`
	InsurerLogo        string `json:"insurer_logo"`
	SumInsured         string `json:"sum_insured"`
	BasePremium        string `json:"base_premium"`
	TotalGST           string `json:"total_gst"`
	TotalAmount        string `json:"total_amount"`
	ConsentDocumentURL string `json:"consent_document_url"`
	Tenure             string `json:"tenure"`
}
