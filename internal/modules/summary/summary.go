package summary

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/summary/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/summary/handlers/v1"
)

type SummaryApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &SummaryApp{}
}

// SetAppName returns the name of the application
func (app *SummaryApp) SetAppName() string {
	return "summary"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *SummaryApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	// Register HTTP routes
	apiRouter := appContext.Router[constant.HTTP_API]
	requireAuth := false

	apiRouter.RegisterRoute(ctx, appName, "POST", "/health",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.GeneratePurchaseIntent,
				ReqBodyStruct: &dto.GeneratePurchaseIntentRequest{},
				RequireAuth:   &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "enterprise_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"summary.purchase_intent.create",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/health",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler: h1.GetPurchaseIntent,
				// ReqBodyStruct: &dto.GetPurchaseIntentRequest{},
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"summary.purchase_intent.get",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "PATCH", "/health",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.UpdatePurchaseIntent,
				ReqBodyStruct: &dto.UpdatePurchaseIntentRequest{},
				RequireAuth:   &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "purchase_intent_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"summary.purchase_intent.update",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/health/token/:purchase_intent_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.GetPurchaseIntentSummaryToken,
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "enterprise_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"summary.purchase_intent.get",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/term/token/:purchase_intent_id",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.GetPurchaseIntentSummaryToken,
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{
						"sub", "enterprise_id",
					}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{
						"sub",
					}, "gateway_portal", []string{
						"summary.purchase_intent.get",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/consent",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.CreateConsent,
				QueryStruct: &dto.CreateConsentRequestParams{},
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{"submission_id", "enterprise_id", "sub"}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{"sub"}, "gateway_portal", []string{
						"summary.consent.create",
					}),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/consent/verify",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.VerifyConsent,
				ReqBodyStruct: &dto.VerifyConsentRequestBody{},
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{"purchase_intent_id", "enterprise_id", "sub"}, "gateway_public"),
					middleware.CookieAuthMiddleware([]string{"sub"}, "gateway_portal", []string{
						"summary.consent.verify",
					}),
				},
			}),
		},
	)

}
