package v1

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/summary/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"github.com/oneassure-tech/oa-protos/go/oa-forms/v0/forms"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (h *Handler) CreateConsent(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var submissionId string

	// Try to get from query params first
	submissionId = c.GetQuery("submission_id")
	grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		c.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}
	md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	// If not in query, try to get from metadata
	if submissionId == "" {
		if ok {
			submissionIds := md.Get("submission_id")
			if len(submissionIds) > 0 {
				submissionId = submissionIds[0]
			}
		}
	}

	if submissionId == "" {
		c.Logger.Error("Submission ID not found in query params or metadata")
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusBadRequest,
			},
			Problem: &response.Problem{
				Type:   "INVALID_INPUT",
				Title:  "Invalid Input",
				Detail: "Purchase intent ID is required",
			},
		}
	}

	authConn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	authClient := auth.NewAuthClient(authConn)

	formsConn, err := h.GrpcClients.Get(forms.FormsService_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting forms client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	formsClient := forms.NewFormsServiceClient(formsConn)

	formResponse, err := formsClient.FetchFormResponse(
		grpcCtx.(context.Context),
		&forms.FetchFormResponseRequest{
			SubmissionId: submissionId,
		},
	)

	if err != nil {
		c.Logger.Error("Error getting form response", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	var proposerPhone string
	for _, obj := range formResponse.ResponseData {
		if obj.SectionName == "proposer" {
			var proposer map[string]interface{}
			jsonBytes, err := json.Marshal(obj.ResponseData)
			if err != nil {
				c.Logger.Error("Error marshalling proposer", zap.Error(err))
				return nil, response.NewInternalServerError()
			}
			err = json.Unmarshal(jsonBytes, &proposer)
			if err != nil {
				c.Logger.Error("Error unmarshalling proposer", zap.Error(err))
				return nil, response.NewInternalServerError()
			}
			proposerPhone = proposer["phone-number"].(string)
		}
	}

	initiateHeadlessOtpResponse, err := authClient.InitiateHeadlessOtp(
		grpcCtx.(context.Context),
		&auth.InitiateHeadlessOtpRequest{
			Purpose:    "proposal_verification",
			Identifier: proposerPhone,
			Transport:  []auth.Transport{auth.Transport_SMS},
			Entity:     auth.Entity_CUSTOMER,
			ExpiresAt:  timestamppb.New(time.Now().Add(time.Minute * 11)),
		},
	)

	if err != nil {
		c.Logger.Error("Error initiating headless otp", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.CreateConsentRequestResponse{
			MfaToken: initiateHeadlessOtpResponse.GetOtpToken(),
		},
	}, nil
}

func (h *Handler) VerifyConsent(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	body := &dto.VerifyConsentRequestBody{}

	err := c.ExtractHttpRequest(body)
	if err != nil {
		c.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		c.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	authConn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	authClient := auth.NewAuthClient(authConn)

	verifyHeadlessOtpResponse, err := authClient.VerifyHeadlessOtp(
		grpcCtx.(context.Context),
		&auth.VerifyHeadlessOtpRequest{
			OtpToken:  body.MfaToken,
			Code:      body.Code,
			Purpose:   "proposal_verification",
			Transport: auth.Transport_SMS,
			Entity:    auth.Entity_CUSTOMER,
		},
	)

	if err != nil {
		c.Logger.Error("Error verifying headless otp", zap.Error(err))
		return nil, grpc.FromGrpcError(grpc.DownstreamProtocolGRPC, err)
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.VerifyConsentRequestResponse{
			IsVerified: verifyHeadlessOtpResponse.IsSuccess,
		},
	}, nil
}
