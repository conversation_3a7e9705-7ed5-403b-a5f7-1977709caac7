package v1

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"encoding/json"

	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/summary/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/summary/helpers"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"github.com/oneassure-tech/oa-protos/go/oa-rapidshort/v0/rapidshort"
	recommendationPb "github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (h *Handler) GeneratePurchaseIntent(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.GeneratePurchaseIntentRequest
	err := c.ExtractHttpRequest(&req)
	if err != nil {
		c.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(recommendationPb.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting recommendation client", zap.Error(err))

		return nil, response.NewInternalServerError()
	}
	grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		c.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	client := recommendationPb.NewRecommendationClient(conn)

	deductible := float32(req.Deductible)
	copay := float32(req.Copay)

	createPurchaseIntentRequest := &recommendationPb.CreatePurchaseIntentRequest{
		// LeadId: req.LeadID,
		// VisitId: req.VisitID, // TODO: Add this field
		// UserId: req.UserID,
		VariantId:        req.VariantID,
		SumInsured:       req.SumInsured,
		SubmissionId:     req.SubmissionID,
		QuoteId:          req.QuoteID,
		RecommendationId: req.RecommendationID,
		Deductible:       &deductible,
		Copay:            &copay,
		Tenure:           req.Tenure,
		ProductId:        req.ProductID,
	}

	createPurchaseIntentResponse, err := client.CreatePurchaseIntent(grpcCtx.(context.Context), createPurchaseIntentRequest)
	if err != nil {
		c.Logger.Error("Error creating purchase intent", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	resp := dto.GeneratePurchaseIntentResponse{
		PurchaseIntentID: createPurchaseIntentResponse.GetPurchaseIntentId(),
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func (h *Handler) GetPurchaseIntent(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(recommendationPb.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		c.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	purchaseIntentIds := c.GinCtx.GetString("purchase_intent_id")
	purchaseIntentId := ""
	if purchaseIntentIds == "" {
		purchaseIntentId = c.GetQuery("purchase_intent_id")
	} else {
		purchaseIntentId = purchaseIntentIds
	}

	if purchaseIntentId == "" {
		c.Logger.Error("Purchase intent id is required")
		return nil, response.NewInternalServerError()
	}

	client := recommendationPb.NewRecommendationClient(conn)

	getPurchaseIntentRequest := &recommendationPb.GetPurchaseIntentRequest{
		PurchaseIntentId: purchaseIntentId,
	}

	getPurchaseIntentResponse, err := client.GetPurchaseIntent(grpcCtx.(context.Context), getPurchaseIntentRequest)
	if err != nil {
		c.Logger.Error("Error getting purchase intent", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	fmt.Println("1243214141")

	resp := helpers.ConvertProtoToDTO(getPurchaseIntentResponse)
	resp.Filters = helpers.ConvertFilters(getPurchaseIntentResponse.GetFilters())

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func (h *Handler) UpdatePurchaseIntent(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.UpdatePurchaseIntentRequest
	err := c.ExtractHttpRequest(&req)
	if err != nil {
		c.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(recommendationPb.Recommendation_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting recommendation client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	if !ok {
		c.Logger.Error("gRPC metadata context not found in gin context")
		return nil, response.NewInternalServerError()
	}

	purchaseIntentIds := c.GinCtx.GetString("purchase_intent_id")
	purchaseIntentId := ""
	if purchaseIntentIds == "" {
		purchaseIntentId = c.GetQuery("purchase_intent_id")
	} else {
		purchaseIntentId = purchaseIntentIds
	}

	client := recommendationPb.NewRecommendationClient(conn)

	// Convert string update type to UpdateType enum
	updateType, err := helpers.StringToUpdateType(req.UpdateType)
	if err != nil {
		c.Logger.Error("Invalid update type", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Convert JSON value to protobuf struct value
	var structValue *structpb.Value
	if len(req.Values) > 0 {
		var jsonValue interface{}
		if err := json.Unmarshal(req.Values, &jsonValue); err != nil {
			c.Logger.Error("Error unmarshaling values", zap.Error(err))
			return nil, response.NewInternalServerError()
		}

		structValue, err = structpb.NewValue(jsonValue)
		if err != nil {
			c.Logger.Error("Error converting to protobuf value", zap.Error(err))
			return nil, response.NewInternalServerError()
		}
	}

	updatePurchaseIntentRequest := &recommendationPb.UpdatePurchaseIntentRequest{
		PurchaseIntentId: purchaseIntentId,
		Type:             updateType,
		Values:           structValue,
	}

	updatePurchaseIntentResponse, err := client.UpdatePurchaseIntent(grpcCtx.(context.Context), updatePurchaseIntentRequest)
	if err != nil {
		c.Logger.Error("Insurer failed to respond", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	resp := dto.UpdatePurchaseIntentResponse{}

	if updatePurchaseIntentResponse.GetSuccess() {
		resp.Message = "Premium updated successfully"
		resp.Success = true
	} else {
		if updatePurchaseIntentResponse.Message != nil {
			resp.Message = *updatePurchaseIntentResponse.Message
			resp.Success = false
		} else {
			c.Logger.Error("Error updating purchase intent", zap.Error(err))
			resp.Message = "Insurer failed to respond"
			resp.Success = false
			return nil, response.NewInternalServerError()
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: resp,
	}, nil
}

func (h *Handler) GetPurchaseIntentSummaryToken(c *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	PurchaseIntentID := c.GetParam("purchase_intent_id")
	baseUrl := c.GetQuery("base_url")
	conn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting authentication client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}
	// Get the enterprise id from the metadata
	rapidshortConn, err := h.GrpcClients.Get(rapidshort.RapidShortService_ServiceDesc.ServiceName)
	if err != nil {
		c.Logger.Error("Error getting rapidshort client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Create authentication client
	authClient := auth.NewAuthClient(conn)
	rapidshortClient := rapidshort.NewRapidShortServiceClient(rapidshortConn)

	// grpcCtx, ok := c.GinCtx.Get(constant.GrpcMetadataCtxKey)
	// if !ok {
	// 	c.Logger.Error("gRPC metadata context not found in gin context")
	// 	return nil, response.NewInternalServerError()
	// }
	// md, ok := metadata.FromOutgoingContext(grpcCtx.(context.Context))
	// if !ok {
	// 	c.Logger.Error("Failed to extract metadata from context")
	// 	return nil, response.NewInternalServerError()
	// }
	enterpriseId := c.GinCtx.GetString("enterprise_id")
	partnerId := c.GinCtx.GetString("partner_id")

	customClaims := map[string]string{
		"purchase_intent_id": PurchaseIntentID,
		"sub":                partnerId,
	}
	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", enterpriseId)

	// Call authentication service to create JWT token
	tokenResponse, err := authClient.GenerateJWTToken(grpcCtx, &auth.GenerateJWTTokenRequest{
		Audience:     "gateway_public",
		CustomClaims: customClaims,
		ExpiresAt:    timestamppb.New(time.Now().Add(time.Hour * 24 * 30)),
	})
	if err != nil {
		c.Logger.Error("Error creating JWT token", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	res, err := rapidshortClient.CreateShortUrl(c.GinCtx, &rapidshort.CreateShortUrlRequest{
		LongUrl: fmt.Sprintf(
			"%s/%s/%s",
			c.GinCtx.Request.Header.Get("Origin"),
			baseUrl,
			tokenResponse.GetAccessToken(),
		),
		RedirectionCode: "302",
	})
	if err != nil {
		c.Logger.Error("Error creating short url", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: &dto.GetPurchaseIntentSummaryTokenResponse{
			Token:    tokenResponse.GetAccessToken(),
			ShortUrl: fmt.Sprintf("%s/%s", "https://oasr.in", res.GetShortCode()),
		},
	}, nil
}
