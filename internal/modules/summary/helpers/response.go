package helpers

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/summary/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/utility"
	"github.com/oneassure-tech/oa-protos/go/oa-recommendation/v0/recommendation"
)

// ConvertProtoToDTO converts the proto GetRecommendationResponse to DTO GetRecommendationResponse
func ConvertProtoToDTO(proto *recommendation.GetPurchaseIntentResponse) *dto.GetPurchaseIntentResponse {
	// Create the response DTO

	resp := dto.GetPurchaseIntentResponse{
		PurchaseIntentID:    proto.GetPurchaseIntentId(),
		VariantID:           proto.GetVariantId(),
		ParentVariantID:     proto.GetParentVariantId(),
		SubmissionID:        proto.GetSubmissionId(),
		ProductID:           proto.GetProductId(),
		BasePremium:         utility.RoundFloat64(float64(proto.GetBasePremium()), 2),
		Deductible:          utility.RoundFloat64(float64(proto.GetDeductible()), 2),
		Tenure:              proto.GetTenure(),
		Riders:              []dto.Rider{},
		MemberDetails:       map[string]dto.MemberList{},
		PlanType:            proto.GetPlanType().String(),
		PreExistingDiseases: proto.GetPreExistingDiseases(),
		CustomerName:        proto.GetCustomerName(),
		CustomerPhone:       proto.GetCustomerPhone(),
		CustomerEmail:       proto.GetCustomerEmail(),
		Features:            []dto.Feature{},
		Exclusions:          []dto.Exclusion{},
		Locked:              proto.GetLocked(),
		TotalGST:            utility.RoundFloat64(float64(proto.GetTotalGst()), 2),
		LeadID:              proto.GetLeadId(),
		InsurerID:           proto.GetInsurerId(),
		QuoteID:             proto.GetQuoteId(),
		VariantStaticDetails: dto.VariantStaticDetails{
			VariantName:          proto.GetVariantStaticDetails().GetVariantName(),
			InsurerName:          proto.GetVariantStaticDetails().GetInsurerName(),
			InsurerLogo:          proto.GetVariantStaticDetails().GetInsurerLogo(),
			NetworkHospitalCount: proto.GetVariantStaticDetails().GetNetworkHospitalCount(),
			ClaimSettlementRatio: proto.GetVariantStaticDetails().GetClaimSettlementRatio(),
			PolicyWordingUrl:     proto.GetVariantStaticDetails().GetPolicyWordingUrl(),
			PolicyBrochureUrl:    proto.GetVariantStaticDetails().GetPolicyBrochureUrl(),
			NetworkHospitalUrl:   proto.GetVariantStaticDetails().GetNetworkHospitalUrl(),
		},
		TotalPremium: utility.RoundFloat64(float64(proto.GetTotalPremium()), 2),
		IsOffline:    proto.GetIsOffline(),
	}

	if proto.GetDeductible() != 0 {
		//here the deductible and sum_insured shouls be like sum_insured,deductible
		resp.SumInsured = strconv.FormatInt(proto.GetSumInsured(), 10) + "," + strconv.FormatFloat(float64(proto.GetDeductible()), 'f', -1, 64)
	} else {
		resp.SumInsured = strconv.FormatInt(proto.GetSumInsured(), 10)
	}

	for _, rider := range proto.GetRiders() {
		responses, err := json.Marshal(rider.GetResponses().AsMap())
		if err != nil {
			fmt.Println("Error marshalling rider responses", err)
			continue
		}
		formConfigBytes, err := json.Marshal(rider.FormConfig)
		if err != nil {
			fmt.Println("Error marshalling rider static form config", err)
			continue
		}
		formConfig := json.RawMessage(formConfigBytes)
		resp.Riders = append(resp.Riders, dto.Rider{
			ID:           rider.GetId(),
			Name:         rider.GetName(),
			Description:  rider.GetDescription(),
			IsSelected:   rider.GetIsSelected(),
			IsMandatory:  rider.GetIsMandatory(),
			Responses:    json.RawMessage(responses),
			RiderPremium: utility.RoundFloat64(float64(rider.GetRiderPremium()), 2),
			Sequence:     rider.GetSequence(),
			FormConfig:   formConfig,
		})
	}

	for relationship, member := range proto.GetMemberDetails() {
		memberList := dto.MemberList{
			Members: []dto.InsuredMember{},
		}

		for _, member := range member.GetMembers() {
			memberList.Members = append(memberList.Members, dto.InsuredMember{
				Age: int(member.GetAge()),
			})
		}

		resp.MemberDetails[relationship] = memberList
	}

	for _, feature := range proto.GetFeatures() {
		resp.Features = append(resp.Features, dto.Feature{
			Name:           feature.GetName(),
			Description:    feature.GetDescription(),
			ListedFeatures: feature.GetListedFeatures(),
		})
	}

	for _, exclusion := range proto.GetExclusions() {
		resp.Exclusions = append(resp.Exclusions, dto.Exclusion{
			Exclusion: exclusion.GetExclusion(),
		})
	}

	return &resp
}

// StringToUpdateType converts a string to recommendation.UpdateType
func StringToUpdateType(updateType string) (recommendation.UpdateType, error) {
	switch updateType {
	case "RIDER":
		return recommendation.UpdateType_RIDER, nil
	case "SUM_INSURED":
		return recommendation.UpdateType_SUM_INSURED, nil
	case "TENURE":
		return recommendation.UpdateType_TENURE, nil
	case "DEDUCTIBLE":
		return recommendation.UpdateType_DEDUCTIBLE, nil
	case "CO_PAY":
		return recommendation.UpdateType_CO_PAY, nil
	default:
		return 0, fmt.Errorf("invalid update type: %s", updateType)
	}
}

func ConvertFilters(protoFilters []*recommendation.PurchaseIntentFilter) []dto.Filter {
	dtoFilters := make([]dto.Filter, 0, len(protoFilters))

	for _, protoFilter := range protoFilters {
		dtoFilter := dto.Filter{
			Tag:    protoFilter.GetTag(),
			Name:   protoFilter.GetName(),
			Values: json.RawMessage{}, // Default empty JSON
		}

		// Convert the proto struct to JSON if it exists
		if values := protoFilter.GetValues(); values != nil {
			if data, err := json.Marshal(values); err == nil {
				dtoFilter.Values = json.RawMessage(data)
			}
		}

		dtoFilters = append(dtoFilters, dtoFilter)
	}

	return dtoFilters
}
