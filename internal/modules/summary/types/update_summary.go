package types

// UpdateType represents different types of updates available
type UpdateType int32

const (
	// R<PERSON><PERSON> represents the rider update type
	RIDER UpdateType = 0
	// SUM_INSURED represents the sum insured update type
	SUM_INSURED UpdateType = 1
	// TENURE represents the tenure update type
	TENURE UpdateType = 2
	// DEDUCTIBLE represents the deductible update type
	DEDUCTIBLE UpdateType = 3
	// CO_PAY represents the co pay update type
	CO_PAY UpdateType = 4
)

// String returns the string representation of the UpdateType
func (u UpdateType) String() string {
	switch u {
	case RIDER:
		return "RIDER"
	case SUM_INSURED:
		return "SUM_INSURED"
	case TENURE:
		return "TENURE"
	case DEDUCTIBLE:
		return "DEDUCTIBLE"
	case CO_PAY:
		return "CO_PAY"
	default:
		return "UNKNOWN"
	}
}

// IsValid checks if the UpdateType is valid
func (u UpdateType) IsValid() bool {
	switch u {
	case RIDER, SUM_INSURED, TENURE, DEDUCTIBLE, CO_PAY:
		return true
	default:
		return false
	}
}

// FromString converts a string to UpdateType
func UpdateTypeFromString(s string) (UpdateType, bool) {
	switch s {
	case "RIDER":
		return RIDER, true
	case "SUM_INSURED":
		return SUM_INSURED, true
	case "TENURE":
		return TENURE, true
	case "DEDUCTIBLE":
		return DEDUCTIBLE, true
	case "CO_PAY":
		return CO_PAY, true
	default:
		return 0, false
	}
}

// ToProto converts the UpdateType to its proto representation
// Note: This assumes you have imported the proto generated code
func (u UpdateType) ToProto() int32 {
	return int32(u)
}

// FromProto creates a UpdateType from a proto int32 value
func UpdateTypeFromProto(protoValue int32) (UpdateType, bool) {
	updateType := UpdateType(protoValue)
	return updateType, updateType.IsValid()
}
