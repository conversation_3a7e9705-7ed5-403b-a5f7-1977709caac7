package dto

import (
	"encoding/json"
)

type GeneratePurchaseIntentRequest struct {
	VariantID        string  `json:"variant_id" validate:"required"`
	SumInsured       float64 `json:"sum_insured" validate:"required"`
	SubmissionID     string  `json:"submission_id"`
	QuoteID          string  `json:"quote_id" validate:"required"`
	RecommendationID string  `json:"recommendation_id"`
	Deductible       float64 `json:"deductible"`
	Copay            float64 `json:"copay"`
	Tenure           int32   `json:"tenure" validate:"required"`
	ProductID        string  `json:"product_id" validate:"required"`
}

type GeneratePurchaseIntentResponse struct {
	PurchaseIntentID string `json:"purchase_intent_id"`
}

type Rider struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Responses    json.RawMessage `json:"responses"`
	FormConfig   json.RawMessage `json:"form_config"`
	IsSelected   bool            `json:"is_selected"`
	IsMandatory  bool            `json:"is_mandatory"`
	RiderPremium float64         `json:"rider_premium"`
	Sequence     int32           `json:"sequence"`
}

type MemberList struct {
	Members []InsuredMember `json:"members"`
}

type InsuredMember struct {
	Age int `json:"age"`
}

type Feature struct {
	Name           string   `json:"name"`
	Description    string   `json:"description"`
	ListedFeatures []string `json:"listed_features"`
}

type Exclusion struct {
	Exclusion string `json:"exclusion"`
}

type GetPurchaseIntentResponse struct {
	PurchaseIntentID     string                `json:"purchase_intent_id"`
	VariantID            string                `json:"variant_id"`
	ParentVariantID      string                `json:"parent_variant_id"`
	SumInsured           string                `json:"sum_insured"`
	SubmissionID         string                `json:"submission_id"`
	ProductID            string                `json:"product_id"`
	BasePremium          float64               `json:"base_premium"`
	Deductible           float64               `json:"deductible"`
	Tenure               int32                 `json:"tenure"`
	Riders               []Rider               `json:"riders"`
	MemberDetails        map[string]MemberList `json:"member_details"`
	PlanType             string                `json:"plan_type"`
	PreExistingDiseases  []string              `json:"pre_existing_diseases"`
	CustomerName         string                `json:"customer_name"`
	CustomerPhone        string                `json:"customer_phone"`
	CustomerEmail        string                `json:"customer_email"`
	Features             []Feature             `json:"features"`
	Exclusions           []Exclusion           `json:"exclusions"`
	Locked               bool                  `json:"locked"`
	TotalGST             float64               `json:"total_gst"`
	LeadID               string                `json:"lead_id"`
	InsurerID            string                `json:"insurer_id"`
	QuoteID              string                `json:"quote_id"`
	VariantStaticDetails VariantStaticDetails  `json:"variant_static_details"`
	TotalPremium         float64               `json:"total_premium"`
	Filters              []Filter              `json:"filters"`
	IsOffline            bool                  `json:"is_offline"`
}

type Filter struct {
	Tag    string          `json:"tag"`
	Name   string          `json:"name"`
	Values json.RawMessage `json:"values"`
}

type VariantStaticDetails struct {
	VariantName          string `json:"variant_name"`
	InsurerName          string `json:"insurer_name"`
	InsurerLogo          string `json:"insurer_logo"`
	NetworkHospitalCount int32  `json:"network_hospital_count"`
	ClaimSettlementRatio int32  `json:"claim_settlement_ratio"`
	PolicyWordingUrl     string `json:"policy_wording_url"`
	PolicyBrochureUrl    string `json:"policy_brochure_url"`
	NetworkHospitalUrl   string `json:"network_hospital_url"`
}

type UpdatePurchaseIntentRequest struct {
	Values     json.RawMessage `json:"values"`
	UpdateType string          `json:"update_type"`
}

type UpdatePurchaseIntentResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

type UpdateTermPurchaseIntentRequest struct {
	PurchaseIntentID string          `json:"purchase_intent_id"`
	Values           json.RawMessage `json:"values"`
	UpdateType       string          `json:"update_type"`
}

type UpdateTermPurchaseIntentResponse struct {
	Message string `json:"message"`
}

type GetPurchaseIntentSummaryTokenResponse struct {
	Token    string `json:"token"`
	ShortUrl string `json:"short_url"`
}
