package dto

type CreateConsentRequestParams struct {
	PurchaseIntentID string `form:"purchase_intent_id" validate:"required"`
}

type CreateConsentRequestResponse struct {
	MfaToken string `json:"mfa_token"`
}

type VerifyConsentRequestBody struct {
	MfaToken string `json:"mfa_token" validate:"required"`
	Code     string `json:"code" validate:"required"`
}

type VerifyConsentRequestResponse struct {
	IsVerified bool `json:"is_verified"`
}
