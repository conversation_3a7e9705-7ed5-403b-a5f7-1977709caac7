package site

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/app"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/middleware"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/site/dto"
	v1 "github.com/oneassure-tech/oa-gateway-svc/internal/modules/site/handlers/v1"
)

type SiteApp struct {
	app.MustEmbedApp
}

// New creates a new instance of the embedded application
func New() app.AppIface {
	return &SiteApp{}
}

// SetAppName returns the name of the application
func (app *SiteApp) SetAppName() string {
	return "site"
}

// Initialize sets up the embedded module with routes, services, and handlers
func (app *SiteApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your controller/handler
	h1 := &v1.Handler{
		GrpcClients: appContext.GrpcClients,
	}

	// Register HTTP routes
	apiRouter := appContext.Router[constant.HTTP_API]
	requireAuth := false

	apiRouter.RegisterRoute(ctx, appName, "POST", "/initiate",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.InitiateMfaCode,
				RequireAuth:   &requireAuth,
				ReqBodyStruct: &dto.InitiateMfaCodeRequest{},
			}),
		},
	)
	apiRouter.RegisterRoute(ctx, appName, "POST", "/verify",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.VerifyMfaCode,
				RequireAuth:   &requireAuth,
				ReqBodyStruct: &dto.VerifyMfaCodeRequest{},
			}),
		},
	)
	apiRouter.RegisterRoute(ctx, appName, "GET", "/upload-link",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.GetDocumentPresignedUrl,
				RequireAuth: &requireAuth,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/get-policies",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.GetPoliciesByPhone,
				RequireAuth: &requireAuth,
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{"isVerified", "access", "phone_number"}, "gateway_site"),
				},
			}),
		},
	)
	apiRouter.RegisterRoute(ctx, appName, "POST", "/trigger-policy-workflow",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.TriggerPolicyWorkflow,
				RequireAuth:   &requireAuth,
				ReqBodyStruct: &dto.TriggerPolicyWorkflowRequest{},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/poll-workflow",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.PollWorkflow,
				RequireAuth:   &requireAuth,
				ReqBodyStruct: &dto.WorkflowStatusPolicy{},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/query",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.Query,
				RequireAuth:   &requireAuth,
				ReqBodyStruct: &dto.Query{},
				// AuthMiddleware: []func(c *gin.Context) bool{
				// 	middleware.BearerAuthMiddleware([]string{"isVerified", "access"}, "gateway_site"),
				// },
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/link",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.LinkPolicyToCustomer,
				RequireAuth:   &requireAuth,
				ReqBodyStruct: &dto.LinkPolicyToCustomerRequest{},
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{"isVerified", "access", "phone_number"}, "gateway_site"),
				},
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "GET", "/catalog",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:     h1.Catalog,
				RequireAuth: &requireAuth,
			}),
		},
	)

	apiRouter.RegisterRoute(ctx, appName, "POST", "/chat-lead",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.ChatLead,
				RequireAuth:   &requireAuth,
				ReqBodyStruct: &dto.ChatLeadRequest{},
				AuthMiddleware: []func(c *gin.Context) bool{
					middleware.BearerAuthMiddleware([]string{"isVerified", "access", "phone_number"}, "gateway_site"),
				},
			}),
		},
	)
	apiRouter.RegisterRoute(ctx, appName, "POST", "/report-failure",
		map[uint8]*handler.OptionStruct{
			1: h1.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:       h1.ReportFailure,
				RequireAuth:   &requireAuth,
				ReqBodyStruct: &dto.ReportFailure{},
			}),
		},
	)
}
