package v1

import (
	"context"
	"net/http"
	"strings"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/site/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

func (h *Handler) PollWorkflow(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.WorkflowStatusPolicy
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", "18TzlaIOrzcGFHY7") // TODO: Add enterprise id
	grpcCtx = metadata.AppendToOutgoingContext(grpcCtx, "partner_id", "18WRepUqi737YExp")

	pollWorkflowRequest := &policies.GetPolicyForCustomerFacingRequest{
		PolicyId: req.PolicyId,
	}

	res, err := client.GetPolicyForCustomerFacing(grpcCtx, pollWorkflowRequest)

	if err != nil {
		// Check if it's a gRPC status error
		if st, ok := status.FromError(err); ok {
			// Check if error code is 14 (Unavailable) and message contains "Policy is being processed"
			if st.Code() == codes.Unavailable && strings.Contains(st.Message(), "Policy is being processed") {
				ctx.Logger.Info("Policy is being processed, returning 202 status", zap.String("policy_id", req.PolicyId))
				return &response.SuccessResponse{
					Status: http.StatusAccepted,
					Payload: map[string]interface{}{
						"status":  "processing",
						"message": "Policy is being processed",
					},
				}, nil
			}
		}

		// For other errors, log and return appropriate error response
		ctx.Logger.Error("Error getting policy for customer facing", zap.Error(err), zap.String("policy_id", req.PolicyId))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status: http.StatusOK,
		Payload: map[string]interface{}{
			"policy": res,
		},
	}, nil
}
