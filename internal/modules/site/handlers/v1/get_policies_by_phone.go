package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

func (h *<PERSON>ler) GetPoliciesByPhone(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	phoneNumber, ok := ctx.GinCtx.Get("phone_number")
	if !ok {
		ctx.Logger.Error("Phone number not found in context")
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting policies client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	// Create new gRPC context with the metadata
	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", "18TzlaIOrzcGFHY7") // TODO: Add enterprise id
	grpcCtx = metadata.AppendToOutgoingContext(grpcCtx, "partner_id", "18WRepUqi737YExp")

	getPoliciesByPhoneRequest := &policies.GetPoliciesByPhoneNumberRequest{
		PhoneNumber: phoneNumber.(string),
	}

	res, err := client.GetPoliciesByPhoneNumber(grpcCtx, getPoliciesByPhoneRequest)

	if err != nil {
		ctx.Logger.Error("Error getting policies by phone number", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
