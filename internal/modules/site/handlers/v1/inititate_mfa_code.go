package v1

import (
	"context"
	"net/http"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/site/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (h *Handler) InitiateMfaCode(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.InitiateMfaCodeRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(auth.Auth_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting auth client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := auth.NewAuthClient(conn)

	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", "18TzlaIOrzcGFHY7") // TODO: Add enterprise id
	grpcCtx = metadata.AppendToOutgoingContext(grpcCtx, "partner_id", "18WRepUqi737YExp")

	initiateMfaCodeRequest := &auth.InitiateHeadlessOtpRequest{
		Identifier: req.PhoneNumber,
		Entity:     auth.Entity_CUSTOMER,
		Purpose:    "chat_verification",
		Transport:  []auth.Transport{auth.Transport_SMS},
		ExpiresAt:  timestamppb.New(time.Now().Add(time.Minute * 10)),
	}

	res, err := client.InitiateHeadlessOtp(grpcCtx, initiateMfaCodeRequest)

	if err != nil {
		ctx.Logger.Error("Error initiating mfa code", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
