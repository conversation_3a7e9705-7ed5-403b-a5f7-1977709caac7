package v1

import (
	"fmt"
	"net/http"

	"github.com/go-resty/resty/v2"
	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

func (h *Handler) Catalog(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	// Call the query service
	url := fmt.Sprintf("http://%s:%s/api/v1/catalog/health-products", config.GetConfig().RAG.Url, config.GetConfig().RAG.Port)

	// Create a new resty client
	client := resty.New()

	var ragResponse map[string]interface{}
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetResult(&ragResponse).
		Get(url)

	if err != nil {
		ctx.Logger.Error("Error calling RAG service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	if resp.StatusCode() != http.StatusOK {
		ctx.Logger.Error("RAG service returned non-OK status", zap.Int("status", resp.StatusCode()))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: ragResponse,
	}, nil
}
