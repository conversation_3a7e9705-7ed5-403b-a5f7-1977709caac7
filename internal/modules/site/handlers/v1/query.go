package v1

import (
	"fmt"
	"net/http"

	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/site/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

func (h *Handler) Query(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.Query
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	if req.SessionId == nil {
		sessionId := uuid.New().String()
		req.SessionId = &sessionId
	}

	// Call the query service
	queryURL := fmt.Sprintf("http://%s:%s/api/v1/policy-agent/query", config.GetConfig().RAG.Url, config.GetConfig().RAG.Port)

	queryReq := dto.Query{
		Query:     req.Query,
		SessionId: req.SessionId,
		VariantId: req.VariantId,
		AgentType: req.AgentType,
		PolicyId:  req.PolicyId,
		Scope:     req.Scope,
	}

	// Create a new resty client
	client := resty.New()

	var ragResponse map[string]interface{}
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(queryReq).
		SetResult(&ragResponse).
		Post(queryURL)

	if err != nil {
		fmt.Println("Error calling RAG service", err)
		return nil, response.NewInternalServerError()
	}

	if resp.StatusCode() != http.StatusOK {
		ctx.Logger.Error("RAG service returned non-OK status", zap.Int("status", resp.StatusCode()))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: ragResponse,
	}, nil
}
