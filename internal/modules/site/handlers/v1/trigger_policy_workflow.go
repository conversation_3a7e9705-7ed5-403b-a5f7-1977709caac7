package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/site/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

func (h *Handler) TriggerPolicyWorkflow(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.TriggerPolicyWorkflowRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting sales client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", "18TzlaIOrzcGFHY7") // TODO: Add enterprise id
	grpcCtx = metadata.AppendToOutgoingContext(grpcCtx, "partner_id", "18WRepUqi737YExp")

	triggerPolicyWorkflowRequest := &policies.InitiateCustomerFacingPolicyWorkflowRequest{
		FileUuid: req.FileUuid,
	}

	res, err := client.InitiateCustomerFacingPolicyWorkflow(grpcCtx, triggerPolicyWorkflowRequest)

	if err != nil {
		ctx.Logger.Error("Error initiating workflow", zap.Error(err))
		return nil, &response.ErrorResponse{
			Status: &response.Status{
				HttpStatus: http.StatusUnauthorized,
			},
			Problem: &response.Problem{
				Type:   "Not Initiated",
				Title:  "Not Initiated",
				Detail: "Workflow Not Initiated",
			},
		}
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
