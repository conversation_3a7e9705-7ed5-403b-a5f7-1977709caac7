package v1

import (
	"context"
	"net/http"

	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-protos/go/oa-sales/v0/policies"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

func (h *<PERSON>ler) GetDocumentPresignedUrl(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {

	conn, err := h.GrpcClients.Get(policies.Policies_ServiceDesc.ServiceName)
	if err != nil {
		ctx.Logger.Error("Error getting policies client", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	client := policies.NewPoliciesClient(conn)

	// Create new gRPC context with the metadata
	grpcCtx := metadata.AppendToOutgoingContext(context.Background(), "enterprise_id", "18TzlaIOrzcGFHY7") // TODO: Add enterprise id
	grpcCtx = metadata.AppendToOutgoingContext(grpcCtx, "partner_id", "18WRepUqi737YExp")

	// Create the request for gRPC service
	grpcReq := &policies.GetDocumentPresignedUrlRequest{}

	// Call the gRPC service
	res, err := client.GetDocumentPresignedUrl(grpcCtx, grpcReq)

	if err != nil {
		ctx.Logger.Error("Error sending document", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	ctx.Logger.Info("Service name", zap.String("name", policies.Policies_ServiceDesc.ServiceName))

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: res,
	}, nil
}
