package v1

import (
	"net/http"

	"github.com/go-resty/resty/v2"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/site/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

func (h *Handler) ReportFailure(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.ReportFailure
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	// Call the query service
	queryURL := "https://n8n.corp.oneassure.in/webhook/273d7157-0f16-4f1a-9d8d-33e7bbc86aac"

	queryReq := map[string]interface{}{
		"file_url": req.FileId,
	}

	// Create a new resty client
	client := resty.New()

	var ragResponse map[string]interface{}
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(queryReq).
		SetResult(&ragResponse).
		Post(queryURL)

	if err != nil {
		ctx.Logger.Error("Error calling report failure service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	if resp.StatusCode() != http.StatusOK {
		ctx.Logger.Error("Report failure service returned non-OK status", zap.Int("status", resp.StatusCode()))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: "success",
	}, nil
}
