package v1

import (
	"net/http"

	"github.com/go-resty/resty/v2"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/modules/site/dto"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"go.uber.org/zap"
)

func (h *Handler) ChatLead(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	var req dto.ChatLeadRequest
	err := ctx.ExtractHttpRequest(&req)
	if err != nil {
		ctx.Logger.Error("Error extracting http request", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	phoneNumber, ok := ctx.GinCtx.Get("phone_number")
	if !ok {
		ctx.Logger.Error("Phone number not found in context")
		return nil, response.NewInternalServerError()
	}

	// Call the query service
	queryURL := "https://n8n.corp.oneassure.in/webhook/be6e647c-c6b4-43e4-962c-67bc22d2a083"

	queryReq := map[string]interface{}{
		"phone_number": phoneNumber,
		"insurer_name": req.InsurerName,
		"product_name": req.ProductName,
		"session_id":   req.SessionId,
	}

	// Create a new resty client
	client := resty.New()

	var ragResponse map[string]interface{}
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(queryReq).
		SetResult(&ragResponse).
		Post(queryURL)

	if err != nil {
		ctx.Logger.Error("Error calling chat lead service", zap.Error(err))
		return nil, response.NewInternalServerError()
	}

	if resp.StatusCode() != http.StatusOK {
		ctx.Logger.Error("Chat lead service returned non-OK status", zap.Int("status", resp.StatusCode()))
		return nil, response.NewInternalServerError()
	}

	return &response.SuccessResponse{
		Status:  http.StatusOK,
		Payload: ragResponse,
	}, nil
}
