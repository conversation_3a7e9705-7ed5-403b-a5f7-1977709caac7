package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
)

// HandlerContext holds request-specific context information
type HandlerContext struct {
	Logger  *logger.Logger
	GinCtx  *gin.Context
	GrpcCtx context.Context
	Req     interface{} // Generic request field for both HTTP and gRPC
}

// ExtractGrpcRequest extracts and type asserts the gRPC request into the provided interface
func (c *HandlerContext) ExtractGrpcRequest(out interface{}) error {
	if c.Req == nil {
		return fmt.Errorf("no gRPC request found in context")
	}

	// Get reflect.Value of output parameter
	outVal := reflect.ValueOf(out)
	if outVal.Kind() != reflect.Ptr {
		return fmt.Errorf("output parameter must be a pointer")
	}

	// Try direct type assertion if types match
	if reflect.TypeOf(c.Req) == outVal.Type().Elem() {
		outVal.Elem().Set(reflect.ValueOf(c.Req))
		return nil
	}

	// If direct assignment fails, try JSON marshaling and unmarshaling
	jsonBytes, err := json.Marshal(c.Req)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	if err := json.Unmarshal(jsonBytes, out); err != nil {
		return fmt.Errorf("failed to unmarshal request into target type: %v", err)
	}

	return nil
}

// ExtractHttpRequest extracts and type asserts the HTTP request body into the provided struct
func (c *HandlerContext) ExtractHttpRequest(out interface{}) error {
	if c.Req == nil {
		return fmt.Errorf("no HTTP request body found in context")
	}

	// Get reflect.Value of output parameter
	outVal := reflect.ValueOf(out)
	if outVal.Kind() != reflect.Ptr {
		return fmt.Errorf("output parameter must be a pointer")
	}

	// Try direct type assertion if types match
	if reflect.TypeOf(c.Req) == outVal.Type().Elem() {
		outVal.Elem().Set(reflect.ValueOf(c.Req))
		return nil
	}

	// If direct assignment fails, try JSON marshaling and unmarshaling
	jsonBytes, err := json.Marshal(c.Req)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %v", err)
	}

	if err := json.Unmarshal(jsonBytes, out); err != nil {
		return fmt.Errorf("failed to unmarshal request body into target type: %v", err)
	}

	return nil
}

func (c *HandlerContext) GetParam(key string) string {
	return c.GinCtx.Param(key)
}

func (c *HandlerContext) GetQuery(key string) string {
	return c.GinCtx.Query(key)
}
