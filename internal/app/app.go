package app

import (
	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/http_router"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
)

// "github.com/oneassure-tech/oa-gateway-svc/internalrouter"

type AppContext struct {
	Router      map[string]http_router.RouterIface
	GrpcRouter  map[string]grpc.RouterIface
	Config      config.Config
	Logger      *logger.Logger
	GrpcClients *grpc.GrpcClientManager
	// Tracer
}

type AppContextInputParams struct {
	Router     map[string]http_router.RouterIface
	GrpcRouter map[string]grpc.RouterIface
}

func NewAppContext(params *AppContextInputParams) *AppContext {
	return &AppContext{
		Router:     params.Router,
		GrpcRouter: params.GrpcRouter,
	}
}
