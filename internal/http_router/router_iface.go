package http_router

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
)

type RouterIface interface {
	GetEngine() *gin.Engine
	GetGlobalRouteGroup() *gin.RouterGroup
	Use(ctx context.Context, middleware ...gin.HandlerFunc)
	RegisterRoute(ctx context.Context, moduleName string, method string, route string, handlerMap map[uint8]*handler.OptionStruct)
}
