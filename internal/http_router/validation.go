package http_router

import (
	"fmt"
	"net/http"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
)

// ValidationError represents a structured validation error
type ValidationError struct {
	Field              string `json:"field"`               // Field that failed validation
	InvariantCondition string `json:"invariant_condition"` // Validation tag that failed
	CurrentValue       string `json:"current_value"`       // Value that was validated
	Message            string `json:"message"`             // Human-readable error message
}

// ValidationErrors is a collection of ValidationError
type ValidationErrors []ValidationError

// Error implements the error interface for ValidationErrors
func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return ""
	}

	var errMsgs []string
	for _, err := range ve {
		errMsgs = append(errMsgs, fmt.Sprintf("Validation failed for '%s': %s", err.Field, err.Message))
	}
	return strings.Join(errMsgs, "; ")
}

type ValidationTarget int

type ValidationConfig struct {
	// Target specifies what part of the request to validate
	Target ValidationTarget
	// Model is the struct type to validate against (should have validator tags)
	Model interface{}
}

const (
	// ValidateBody indicates that the request body should be validated
	ValidateBody ValidationTarget = iota
	// ValidateQuery indicates that the query parameters should be validated
	ValidateQuery
	// ValidateParams indicates that the URL parameters should be validated
	ValidateParams
)

// Validate performs validation on the request according to the handler options
// It handles validation for request body, query parameters, and URL parameters
func Validate(c *gin.Context, h *handler.OptionStruct, logger *logger.Logger) *response.HTTPError {
	var targets []ValidationConfig

	// Prepare the validation configs
	if h.RequireValidation {
		if h.ReqBodyStruct != nil {
			targets = append(targets, ValidationConfig{
				Target: ValidateBody,
				Model:  h.ReqBodyStruct,
			})
		}

		if h.QueryStruct != nil {
			targets = append(targets, ValidationConfig{
				Target: ValidateQuery,
				Model:  h.QueryStruct,
			})
		}

		if h.ParamStruct != nil {
			targets = append(targets, ValidationConfig{
				Target: ValidateParams,
				Model:  h.ParamStruct,
			})
		}
	}

	// Perform the validation one by one
	for _, t := range targets {
		// Create a fresh model for validation
		freshModel := createFreshModel(t.Model)

		// Bind request data to model based on target type
		if err := bindRequestToModel(c, t.Target, freshModel, logger); err != nil {
			return err
		}

		// Update t.Model with the fresh instance
		t.Model = freshModel

		// Validate the model
		if err := validateModel(c, t.Model, logger); err != nil {
			return err
		}
	}

	return nil
}

// createFreshModel creates a new instance of the provided model type
// This ensures we always validate with fresh instances to avoid caching issues
func createFreshModel(modelTemplate interface{}) interface{} {
	modelType := reflect.TypeOf(modelTemplate).Elem()
	return reflect.New(modelType).Interface()
}

// bindRequestToModel binds request data to the model based on validation target type
// It handles different binding sources: JSON body, query parameters, and URL parameters
func bindRequestToModel(c *gin.Context, target ValidationTarget, model interface{}, logger *logger.Logger) *response.HTTPError {
	var err error

	switch target {
	case ValidateBody:
		// Set a reasonable max body size
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 1<<20) // 1MB limit
		err = c.ShouldBindJSON(model)
	case ValidateQuery:
		err = c.ShouldBindQuery(model)
	case ValidateParams:
		err = c.ShouldBindUri(model)
	}

	if err != nil {
		return response.GenericHttpBadRequestError(c)
	}

	return nil
}

// validateModel validates the model using the validator package
// It processes validation errors and returns them in a structured format
func validateModel(c *gin.Context, model interface{}, logger *logger.Logger) *response.HTTPError {
	// Create a new validator instance for each validation
	v := validator.New()
	v.SetTagName("validate") // Ensure we're using the correct tag name

	if err := v.Struct(model); err != nil {
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			// Convert validation errors to a more readable format
			errorDetails := make([]*ValidationError, 0)

			for _, e := range validationErrors {
				// Process each validation error and convert to user-friendly format
				validationError := processValidationError(e, model, logger)
				errorDetails = append(errorDetails, validationError)
			}

			return &response.HTTPError{
				Status: http.StatusBadRequest,
				Problem: &response.HttpProblem{
					Type:           "validation_error",
					Title:          "Validation Error",
					Detail:         "Request validation failed",
					Instance:       c.Request.URL.Path,
					AdditionalInfo: errorDetails,
				},
			}
		}
	}

	return nil
}

// processValidationError transforms a validator.FieldError into our ValidationError type
// It handles the conversion of field names to their JSON/form/URI tag equivalents
func processValidationError(e validator.FieldError, model interface{}, logger *logger.Logger) *ValidationError {
	// Get the field path for nested structs - e.Namespace() gives the full path
	fieldPath := e.Namespace()

	// Split the namespace by dots to get the path components
	parts := strings.Split(fieldPath, ".")

	// Build the JSON path for the field
	fieldName := buildJSONPath(parts, model, e, logger)

	// Get the current value, handling nil pointers
	currentValue := extractActualValue(e)

	// Create the validation error
	validationError := &ValidationError{
		Field:              fieldName,
		InvariantCondition: e.Tag(),
		CurrentValue:       currentValue,
		Message:            getErrorMessage(e),
	}

	return validationError
}

// buildJSONPath constructs the JSON path for a field based on its tags and position in the struct hierarchy
// It handles both nested fields and simple (non-nested) fields
func buildJSONPath(parts []string, model interface{}, e validator.FieldError, logger *logger.Logger) string {
	// Handle the simple case (non-nested fields)
	if len(parts) <= 1 {
		// Remove debug logging
		// fmt.Println("simple------------", parts)
		return handleSimpleField(model, e, logger)
	}

	// Handle nested fields
	// Remove debug logging
	// fmt.Println("nested------------", parts)
	return handleNestedField(parts, model, logger)
}

// findField searches for a field in a struct, including in embedded types
// It handles both direct field access and fields in embedded structs
//
// Parameters:
// - structType: The reflect.Type of the struct to search in
// - fieldName: The name of the field to find
// - logger: Logger for diagnostic information
//
// Returns:
// - reflect.StructField: The found field
// - bool: Whether the field was found
// - string: The embedded field prefix to remove from path (if any)
func findField(structType reflect.Type, fieldName string, logger *logger.Logger) (reflect.StructField, bool, string) {
	// First try to find the field directly in the struct
	field, found := structType.FieldByName(fieldName)
	if found {
		return field, true, ""
	}

	// Look for the field in embedded types
	for i := 0; i < structType.NumField(); i++ {
		f := structType.Field(i)

		// Skip non-anonymous (non-embedded) fields
		if !f.Anonymous {
			continue
		}

		// This is an embedded field - get its type
		embedType := f.Type

		// Extract the embedded type name for pointer fields
		var embeddedName string
		if embedType.Kind() == reflect.Ptr {
			// For pointer types, get the base type name in lowercase
			// This will be used to remove it from field paths
			embeddedName = strings.ToLower(embedType.Elem().Name())
			embedType = embedType.Elem()
		}

		// Try to find the field in this embedded type
		if embedField, embedFound := embedType.FieldByName(fieldName); embedFound {
			return embedField, true, embeddedName
		}
	}

	return reflect.StructField{}, false, ""
}

// getTagName extracts the appropriate tag name (json, form, or uri) from a struct field
// If no tags are found, it returns the lowercase field name
//
// Parameters:
// - field: The struct field to extract tags from
// - defaultName: The default name to use if no tags are found
//
// Returns: The tag name or lowercase default name
func getTagName(field reflect.StructField, defaultName string) string {
	// Extract the tag name, checking json, form, and uri tags in that order
	if jsonTag := field.Tag.Get("json"); jsonTag != "" {
		// Take the part before any comma (e.g., "user_id" from "user_id,omitempty")
		parts := strings.Split(jsonTag, ",")
		if parts[0] != "" {
			return parts[0]
		}
	}

	if formTag := field.Tag.Get("form"); formTag != "" {
		parts := strings.Split(formTag, ",")
		if parts[0] != "" {
			return parts[0]
		}
	}

	if uriTag := field.Tag.Get("uri"); uriTag != "" {
		parts := strings.Split(uriTag, ",")
		if parts[0] != "" {
			return parts[0]
		}
	}

	// If no tag was found, return lowercase field name
	return strings.ToLower(defaultName)
}

// getNextType gets the type for the next iteration when traversing nested structs
// It handles pointers, arrays, and slices, returning the underlying type
//
// Parameters:
// - field: The struct field to get the type from
// - logger: Logger for diagnostic information
//
// Returns: The reflect.Type to use for the next iteration
func getNextType(field reflect.StructField, logger *logger.Logger) reflect.Type {
	// Start with the field's type
	fieldType := field.Type

	// Handle pointer types by dereferencing them
	// This handles cases like *User where we need the underlying User type
	if fieldType.Kind() == reflect.Ptr {
		fieldType = fieldType.Elem()
	}

	// Handle array and slice types by getting the element type
	// This handles cases like []User or [5]User where we need the User type
	if fieldType.Kind() == reflect.Array || fieldType.Kind() == reflect.Slice {
		elementType := fieldType.Elem()

		// If the array elements are pointers, dereference them too
		if elementType.Kind() == reflect.Ptr {
			elementType = elementType.Elem()
		}

		fieldType = elementType
	}

	return fieldType
}

// extractActualValue extracts the actual value for a validation error
// For oneOf errors, it extracts the actual value instead of the parameter list
// It handles nil values by returning an empty string
func extractActualValue(e validator.FieldError) string {
	// Try to extract the actual value
	val := e.Value()

	if val == nil {
		// Return empty string for nil values instead of "<nil>"
		return ""
	}

	// Handle different validation tags
	switch e.Tag() {
	case "oneof":
		// For oneof, show the actual value that failed validation
		return fmt.Sprintf("%v", val)
	default:
		// For other validation types, show the actual value
		// unless it's the parameter list for the validation rule
		if e.Param() != "" && !strings.Contains(e.Param(), ",") {
			return e.Param()
		}
		return fmt.Sprintf("%v", val)
	}
}

// getErrorMessage returns a human-readable error message for a validation error
func getErrorMessage(e validator.FieldError) string {
	switch e.Tag() {
	case "required":
		return "This field is required"
	case "email":
		return "Invalid email format"
	case "min":
		return fmt.Sprintf("Value must be at least %s", e.Param())
	case "max":
		return fmt.Sprintf("Value must be at most %s", e.Param())
	case "len":
		return fmt.Sprintf("Value must be exactly %s characters long", e.Param())
	case "oneof":
		return fmt.Sprintf("Value must be one of: %s", e.Param())
	case "numeric":
		return "Value must be numeric"
	case "alphanum":
		return "Value must be alphanumeric"
	case "datetime":
		return fmt.Sprintf("Invalid datetime format, expected: %s", e.Param())
	default:
		return fmt.Sprintf("Failed validation on %s", e.Tag())
	}
}

// handleSimpleField processes a non-nested field (direct field on a struct)
// It checks for the field in the main struct and in embedded types,
// and uses the appropriate tag (json/form/uri) for the field name
func handleSimpleField(model interface{}, e validator.FieldError, logger *logger.Logger) string {
	// Start with the original field name as fallback
	fieldName := e.Field()

	// Get the model type (dereference if it's a pointer)
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	// Try to find the field in the struct or its embedded types
	structField, found, _ := findField(modelType, e.StructField(), logger)

	if found {
		// Get the field name from tags (json, form, uri)
		fieldName = getTagName(structField, fieldName)
	} else {
		// If the field wasn't found, use the lowercase name
		fieldName = strings.ToLower(fieldName)
	}

	return fieldName
}

// shouldSkipEmbeddedType checks if the next part of the path is an embedded type reference
// that should be skipped in the validation path.
//
// Parameters:
// - structType: The struct type to check for embedded fields
// - parts: The array of path parts
// - currentIndex: The current index in the parts array
// - embeddedPrefix: Optional embedded prefix if already known (may be empty)
// - logger: Logger for diagnostic information
//
// Returns:
// - bool: True if the next path part should be skipped
// - int: The new index to use (incremented if a part was skipped)
func shouldSkipEmbeddedType(structType reflect.Type, parts []string, currentIndex int, embeddedPrefix string, logger *logger.Logger) (bool, int) {
	newIndex := currentIndex

	// If we're at the last part, nothing to skip
	if currentIndex >= len(parts)-1 {
		return false, newIndex
	}

	nextField := parts[currentIndex+1]

	// If embedded prefix is already known (from findField), check if next part matches it
	if embeddedPrefix != "" {
		if strings.HasPrefix(strings.ToLower(nextField), embeddedPrefix+".") ||
			strings.EqualFold(nextField, embeddedPrefix) {
			return true, currentIndex + 1
		}
		return false, currentIndex
	}

	// If no embedded prefix is provided, scan struct for embedded types
	for j := 0; j < structType.NumField(); j++ {
		f := structType.Field(j)

		// Only check anonymous (embedded) fields
		if !f.Anonymous {
			continue
		}

		// Get embedded type
		embedType := f.Type
		embeddedName := ""

		// Handle pointer embedded types
		if embedType.Kind() == reflect.Ptr {
			embeddedName = strings.ToLower(embedType.Elem().Name())
		} else if embedType.Kind() == reflect.Struct {
			embeddedName = strings.ToLower(embedType.Name())
		}

		// If next part matches embedded type pattern, skip it
		if embeddedName != "" {
			if strings.HasPrefix(strings.ToLower(nextField), embeddedName+".") ||
				strings.EqualFold(nextField, embeddedName) {
				return true, currentIndex + 1
			}
		}
	}

	return false, currentIndex
}

// handleNestedField processes a nested field path (fields inside structs inside structs)
// It traverses the struct hierarchy, following the path components, and
// builds a JSON path using the appropriate tags at each level
func handleNestedField(parts []string, model interface{}, logger *logger.Logger) string {
	// Build a new path using the field names extracted from JSON tags
	jsonPath := []string{}

	// Get the root struct type and make sure we're working with a struct, not a pointer
	currentType := reflect.TypeOf(model)
	if currentType.Kind() == reflect.Ptr {
		currentType = currentType.Elem()
	}

	// Remove debug logging
	// fmt.Println("NestedField", parts)
	// fmt.Println("currentType------------", currentType)

	// Skip the first part which is the struct name itself
	for i := 1; i < len(parts); i++ {

		fieldName := parts[i]
		// Remove debug logging
		// fmt.Println("i------------", fieldName)

		// Special handling for array indices - preserve the names but with proper JSON field names
		if strings.Contains(fieldName, "[") && strings.Contains(fieldName, "]") {
			// Extract the field name and array index
			bracketIndex := strings.Index(fieldName, "[")
			baseFieldName := fieldName[:bracketIndex]
			arrayIndex := fieldName[bracketIndex:]

			// Try to find the corresponding struct field for the array
			field, found, _ := findField(currentType, baseFieldName, logger)

			// If found, use the JSON tag name + array index, otherwise use lowercase
			if found {
				// Extract tag name for the array field
				tagName := getTagName(field, baseFieldName)
				jsonPath = append(jsonPath, tagName+arrayIndex)

				// Remove debug logging
				// fmt.Println("jsonPath Now------------", jsonPath)

				// Update current type for array elements
				fieldType := getNextType(field, logger)
				// Remove debug logging
				// fmt.Println("fieldType------------", fieldType)

				// Check if we have an embedded type in the array element
				if fieldType.Kind() == reflect.Struct && i+1 < len(parts) {
					shouldSkip, newIndex := shouldSkipEmbeddedType(fieldType, parts, i, "", logger)
					if shouldSkip {
						i = newIndex
					}
				}

				// Update current type for next iteration
				if fieldType.Kind() == reflect.Struct {
					currentType = fieldType
				}
			} else {
				// Fall back to lowercase if field not found
				jsonPath = append(jsonPath, strings.ToLower(baseFieldName)+arrayIndex)
			}
			continue
		}

		// Find the field in the current struct (including embedded fields)
		field, found, embeddedPrefix := findField(currentType, fieldName, logger)

		// If field wasn't found, use lowercase name and continue
		if !found {
			jsonPath = append(jsonPath, strings.ToLower(fieldName))
			continue
		}

		// Get tag name (json, form, or uri) or use lowercase field name
		tagName := getTagName(field, fieldName)

		// If we found this field in an embedded type, check if the next path part
		// has the embedded type name as a prefix (e.g., "commonmember.firstname")
		if embeddedPrefix != "" && i < len(parts)-1 {
			shouldSkip, newIndex := shouldSkipEmbeddedType(currentType, parts, i, embeddedPrefix, logger)
			if shouldSkip {
				// Update current type for next iteration
				fieldType := getNextType(field, logger)
				if fieldType.Kind() == reflect.Struct {
					currentType = fieldType
				}
				i = newIndex
				continue
			}
		}

		jsonPath = append(jsonPath, tagName)

		// Only update the type for the next iteration if this isn't the last field
		if i < len(parts)-1 {
			// Get the next type in the path, handling pointers, arrays, and slices
			fieldType := getNextType(field, logger)

			// Only continue if we found a struct type to traverse
			if fieldType.Kind() != reflect.Struct {
				break
			}

			// Update the current type for the next iteration
			currentType = fieldType
		}
	}

	// Join the path components with dots
	result := strings.Join(jsonPath, ".")
	return result
}
