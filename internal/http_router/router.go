package http_router

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"github.com/oneassure-tech/oa-gateway-svc/internal/handler"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/response"
	"github.com/oneassure-tech/oa-gateway-svc/internal/telemetry"
	oaerrors "github.com/oneassure-tech/oa-utilities/go/oa-errors"
	"go.uber.org/zap"
)

type Router struct {
	ginRouter        *gin.Engine
	GlobalRouteGroup *gin.RouterGroup
	routeGroupMap    map[string]*gin.RouterGroup
	minVersion       uint8
	maxVersion       uint8
	metricService    *telemetry.MetricService
}

type RouterInputParams struct {
	MaxVersion uint8
	MinVersion uint8
	CorsConfig *cors.Config
}

func (r *Router) requestTimingMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Start timer - this will capture timing for both successful and failed requests,
		// even when the request is aborted with ctx.Abort() or ctx.AbortWithStatusJSON(),
		// because deferred functions in already-executed middleware still run
		startTime := time.Now()

		// Process request
		ctx.Next()

		// Calculate duration - this runs even if the request was aborted
		duration := time.Since(startTime)

		// Get the response status - will be 200 if not set
		status := ctx.Writer.Status()

		// Record metric for both successful and failed requests
		r.metricService.RecordRequestLatency(
			context.Background(),
			ctx.Request.Method,
			ctx.Request.URL.Path,
			duration.Milliseconds(),
			status,
		)
	}
}

func NewHttpRouter(params *RouterInputParams) RouterIface {
	// Enable Release Mode if Environment is not Prod
	if config.GetConfig().Environment != config.Dev {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Apply CORS middleware right after router initialization
	if params.CorsConfig != nil {
		router.Use(cors.New(*params.CorsConfig))
	}

	r := &Router{
		ginRouter:        router,
		GlobalRouteGroup: router.Group("/:version"),
		minVersion:       params.MinVersion,
		maxVersion:       params.MaxVersion,
		routeGroupMap:    map[string]*gin.RouterGroup{},
		metricService:    telemetry.NewMetricService(),
	}

	// Add timing middleware first to capture total request time
	r.GlobalRouteGroup.Use(r.requestTimingMiddleware())
	r.GlobalRouteGroup.Use(r.contextInitializer)

	return r
}

func (r *Router) GetGlobalRouteGroup() *gin.RouterGroup {
	return r.GlobalRouteGroup
}

func (r *Router) createRouteGroup(group string) {
	if _, ok := r.routeGroupMap[group]; !ok {
		r.routeGroupMap[group] = r.GlobalRouteGroup.Group(fmt.Sprintf("/%s", group))
	}
}

func (r *Router) RegisterRoute(ctx context.Context, moduleName string, method string, route string, handlerMap map[uint8]*handler.OptionStruct) {
	// Extract the group name from context
	group := ctx.Value(constant.ApiGroup).(string)

	// Create or get the route group
	r.createRouteGroup(group)
	routeGroup := r.routeGroupMap[group]

	// Create a scope middleware that will run after contextInitializer
	injectLoggerMiddlware := func(ctx *gin.Context) {
		// Get base logger
		baseLogger := logger.GetLogger()

		reqCtx := ExtractRequestContext(ctx)

		logger := baseLogger.GetScopedLogger(moduleName).With(
			zap.String("type", constant.HTTPRequest),
			zap.String("http_path", ctx.Request.URL.Path),
			zap.String("http_method", ctx.Request.Method),
			zap.String("http_client_ip", ctx.ClientIP()),
		)

		// Add query string parameters if present
		if len(ctx.Request.URL.RawQuery) > 0 {
			logger = logger.With(
				zap.String("http_query_string", ctx.Request.URL.RawQuery),
			)
		}

		// Add non-sensitive headers
		headers := make(map[string]string)
		for k, v := range ctx.Request.Header {
			// Skip sensitive headers
			if !strings.EqualFold(k, constant.Authorization) && !strings.EqualFold(k, constant.Cookie) {
				headers[k] = v[0] // Take first value if multiple exists
			}
		}
		if len(headers) > 0 {
			logger = logger.With(
				zap.Any("http_headers", headers),
			)
		}

		// Add request body for non-GET requests
		if ctx.Request.Method != "GET" && ctx.Request.Header.Get("Content-Type") == "application/json" {
			var jsonBody map[string]interface{}
			ExtractHttpRequestBody(ctx, &jsonBody)
			if len(jsonBody) > 0 {
				// Remove sensitive fields
				delete(jsonBody, constant.Password)
				delete(jsonBody, constant.Token)
				delete(jsonBody, constant.Secret)

				logger = logger.With(
					zap.Any("http_body", jsonBody),
				)
			}
		}

		// Update the logger in the request context
		reqCtx.Logger = logger

		ctx.Next()
	}

	// Add the scope middleware to this route group
	routeGroup.Use(injectLoggerMiddlware)

	// Register the route with the handler
	routeGroup.Handle(method, route, r.makeHandler(handlerMap))
}

func (r *Router) GetEngine() *gin.Engine {
	return r.ginRouter
}

func (r *Router) makeHandler(handlerMap map[uint8]*handler.OptionStruct) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Get router context with logger first
		routerCtx := ExtractRequestContext(ctx)
		logger := routerCtx.Logger

		// Step 1: Parse and validate API version
		v, err := strconv.ParseUint(strings.Split(ctx.Param("version"), "v")[1], 10, 8)
		if err != nil {
			logger.Error("Failed to parse version", zap.Error(err))
			r.respondWithError(ctx, response.GenericHttpInternalServerError(ctx))
			return
		}

		apiVersion := uint8(v)

		// Check if version is within allowed range
		if apiVersion > r.maxVersion {
			r.respondWithError(ctx, response.GenericHttpNotFoundError(ctx))
			return
		}

		var handlerFn handler.CustomHandler
		curr := r.maxVersion
		foundHandler := false
		var h *handler.OptionStruct

		for curr >= r.minVersion {
			if (curr == apiVersion) || (curr < apiVersion) {
				if _, ok := handlerMap[curr]; !ok {
					curr--
					continue
				}
				h = handlerMap[curr]
				handlerFn = h.Handler
				foundHandler = true
				break
			}
			curr--
		}

		if !foundHandler {
			logger.Error("No handler found for version",
				zap.Uint8("version", apiVersion),
			)
			r.respondWithError(ctx, response.GenericHttpNotFoundError(ctx))
			return
		}

		if h.AuthMiddleware != nil {
			for idx, middleware := range h.AuthMiddleware {
				fmt.Println("middleware failed", len(h.AuthMiddleware), idx)
				if middleware(ctx) {
					break
				} else if idx == len(h.AuthMiddleware)-1 {
					fmt.Println("All middlewares failed", len(h.AuthMiddleware), idx)
					response.SendError(ctx, &response.HTTPError{
						Status: http.StatusUnauthorized,
						Problem: &response.HttpProblem{
							Type:     oaerrors.ErrUnauthorized.GetCode(),
							Title:    oaerrors.ErrUnauthorized.GetMessage(),
							Instance: ctx.Request.URL.Path,
						},
					})
					return
				}
			}
		}

		// if h.RequireAuth {
		// 	if !r.jwtAuthMiddleware(ctx) {
		// 		return
		// 	}
		// }

		vError := Validate(ctx, h, logger)
		if vError != nil {
			r.respondWithError(ctx, vError)
			return
		}

		s, e := handlerFn(routerCtx)
		r.handleResponse(ctx, routerCtx, s, e)
	}
}

func (r *Router) respondWithError(ctx *gin.Context, httpErr *response.HTTPError) {
	routerCtx := ExtractRequestContext(ctx)

	routerCtx.Logger.Info(fmt.Sprintf("❌ HTTP Request Failed [%s %s] (%d)",
		ctx.Request.Method, ctx.Request.URL.Path, httpErr.Status),
		zap.Int("status", int(httpErr.Status)),
	)

	ctx.AbortWithStatusJSON(int(httpErr.Status), httpErr.Problem)
	r.metricService.IncrementHTTPError(context.Background(), ctx.Request.Method, ctx.Request.URL.Path)
}

// handleResponse centralizes all response handling including logging and metrics
func (r *Router) handleResponse(ctx *gin.Context, handlerCtx *handler.HandlerContext, success *response.SuccessResponse, e *response.ErrorResponse) {
	// Get logger from context
	logger := handlerCtx.Logger

	if e != nil {
		// Transform error to HTTP response
		HTTPError := e.TransformToHTTPError(ctx)

		logger.Info(fmt.Sprintf("❌ HTTP Request Failed [%s %s] (%d)",
			ctx.Request.Method, ctx.Request.URL.Path, HTTPError.Status),
			zap.Int("status", int(HTTPError.Status)),
		)

		// Send error response
		ctx.Header("Content-Type", "application/problem+json")
		ctx.AbortWithStatusJSON(int(HTTPError.Status), HTTPError.Problem)

		// Increment error counter
		r.metricService.IncrementHTTPError(context.Background(), ctx.Request.Method, ctx.Request.URL.Path)
		return
	}

	// Transform success response to HTTP response
	HTTPSuccess := success.TransformToHTTPSuccess(ctx)

	logger.Info(fmt.Sprintf("✅ HTTP Request Succeeded [%s %s] (%d)",
		ctx.Request.Method, ctx.Request.URL.Path, HTTPSuccess.Status),
		zap.Int("status", int(HTTPSuccess.Status)),
	)

	// Send success response
	ctx.JSON(int(HTTPSuccess.Status), HTTPSuccess)
	r.metricService.IncrementHTTPSuccess(context.Background(), ctx.Request.Method, ctx.Request.URL.Path)
}

func (r *Router) Use(ctx context.Context, middleware ...gin.HandlerFunc) {
	group := ctx.Value(constant.ApiGroup).(string)
	r.createRouteGroup(group)
	r.routeGroupMap[group].Use(middleware...)
}

func (r *Router) contextInitializer(ctx *gin.Context) {
	// Initialize basic handler context without scoped logger
	handlerCtx := &handler.HandlerContext{
		GinCtx: ctx,
		Logger: logger.GetLogger().GetBaseLogger(), // Use base logger initially
	}

	// If it's a POST/PUT/PATCH request, extract the body
	if ctx.Request.Method != "GET" {
		var jsonBody map[string]interface{}
		fmt.Println("Content-Type:", ctx.Request.Header.Get("Content-Type"))
		if ctx.Request.Header.Get("Content-Type") == "application/json" {
			ExtractHttpRequestBody(ctx, &jsonBody)
		} else if ctx.Request.Header.Get("Content-Type") == "application/x-www-form-urlencoded" {
			ExtractFormData(ctx, &jsonBody)
		}
		handlerCtx.Req = jsonBody
	}

	ctx.Set(constant.RouterContext, handlerCtx)
	ctx.Next()
}

// sendDirectError sends an error response without any context or metrics
func sendDirectError(ctx *gin.Context, e *response.HTTPError) {
	// Use scoped logger for initialization
	initLogger := logger.GetLogger().GetScopedLogger("direct-error")
	initLogger.Info(fmt.Sprintf("❌ HTTP Request Failed [%s %s] (%d)",
		ctx.Request.Method, ctx.Request.URL.Path, e.Status),
		zap.Int("status", int(e.Status)),
		zap.String("error_type", e.Problem.Type),
		zap.String("error_title", e.Problem.Title),
		zap.String("error_detail", e.Problem.Detail),
	)

	// Set content type and send error response
	ctx.Header("Content-Type", "application/problem+json")
	ctx.AbortWithStatusJSON(int(e.Status), e.Problem)
}

func ExtractRequestContext(ctx *gin.Context) *handler.HandlerContext {
	logger := logger.GetLogger().GetScopedLogger("request-context-extractor")

	handlerCtxInterface, exists := ctx.Get(constant.RouterContext)
	if !exists {
		// Create a default context with basic logging for initialization phase
		defaultCtx := &handler.HandlerContext{
			GinCtx: ctx,
			Logger: logger,
		}
		return defaultCtx
	}

	handlerCtx, ok := handlerCtxInterface.(*handler.HandlerContext)
	if !ok {
		// Send error response
		sendDirectError(ctx, response.GenericHttpInternalServerError(ctx))
		return nil
	}

	return handlerCtx
}

func ExtractHttpRequestBody(ctx *gin.Context, c interface{}) error {
	logger := ExtractRequestContext(ctx).Logger
	reqBodyBytes, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		logger.Error("Failed to read request body",
			zap.Error(err),
		)
		SendError(ctx, response.GenericHttpInternalServerError(ctx))
		return err
	}

	// Restore the body for subsequent reads
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(reqBodyBytes))

	if err := json.Unmarshal(reqBodyBytes, &c); err != nil {
		logger.Error("Failed to unmarshal request body",
			zap.Error(err),
		)
		SendError(ctx, response.GenericHttpInternalServerError(ctx))
		return err
	}

	return nil
}

func ExtractFormData(ctx *gin.Context, c interface{}) error {
	logger := ExtractRequestContext(ctx).Logger
	formData := make(map[string]interface{})

	if err := ctx.Request.ParseForm(); err != nil {
		logger.Error("Failed to parse form data",
			zap.Error(err),
		)
		SendError(ctx, response.GenericHttpInternalServerError(ctx))
		return err
	}

	for key, values := range ctx.Request.PostForm {
		if len(values) > 0 {
			formData[key] = values[0]
		}
	}
	c = formData

	return nil
}

// SendError is a helper function for full error handling with logging and metrics
func SendError(ctx *gin.Context, e *response.HTTPError) {
	// Get handler context if available
	routerCtx := ExtractRequestContext(ctx)
	if routerCtx == nil {
		// If no context available, fall back to direct error
		sendDirectError(ctx, e)
		return
	}

	m := telemetry.NewMetricService()

	routerCtx.Logger.Info(fmt.Sprintf("❌ HTTP Request Failed [%s %s] (%d)",
		ctx.Request.Method, ctx.Request.URL.Path, e.Status),
		zap.Int16("status", e.Status),
	)

	// Send error response
	ctx.Header("Content-Type", "application/problem+json")
	ctx.AbortWithStatusJSON(int(e.Status), e.Problem)

	// Increment error counter
	m.IncrementHTTPError(context.Background(), ctx.Request.Method, ctx.Request.URL.Path)
}
