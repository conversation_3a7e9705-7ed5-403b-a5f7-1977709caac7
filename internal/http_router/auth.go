package http_router

// // JWTAuthMiddleware validates JWT tokens, extracts enterprise ID and other claims,
// // and sets up gRPC metadata for forwarding to backend services
// func (r *Router) jwtAuthMiddleware(c *gin.Context) bool {

// 	hCtx := ExtractRequestContext(c)
// 	// Get token from Authorization header
// 	authHeader := c.<PERSON>("Authorization")
// 	if authHeader == "" {
// 		hCtx.Logger.Info("Missing Authorization header")
// 		SendError(c, &response.HTTPError{
// 			Status: http.StatusUnauthorized,
// 			Problem: &response.HttpProblem{
// 				Type:     oaerrors.ErrUnauthorized.GetCode(),
// 				Title:    oaerrors.ErrUnauthorized.GetMessage(),
// 				Instance: c.Request.URL.Path,
// 			},
// 		})
// 		return false
// 	}

// 	// Check if the header has the Bearer prefix
// 	if !strings.HasPrefix(authHeader, "Bearer ") {
// 		hCtx.Logger.Info("Missing Bearer prefix in Authorization header")
// 		SendError(c, &response.HTTPError{
// 			Status: http.StatusUnauthorized,
// 			Problem: &response.HttpProblem{
// 				Type:     oaerrors.ErrUnauthorized.GetCode(),
// 				Title:    oaerrors.ErrUnauthorized.GetMessage(),
// 				Instance: c.Request.URL.Path,
// 			},
// 		})
// 		return false
// 	}

// 	// Get the token string by removing the "Bearer " prefix
// 	tokenString := strings.TrimPrefix(authHeader, "Bearer ")

// 	cfg := &client.ClientConfig{
// 		ServiceURL: config.GetConfig().JWT.PublicKeyURL,
// 	}
// 	conn, err := client.NewGrpcClient(cfg)
// 	if err != nil {
// 		SendError(c, &response.HTTPError{
// 			Status: http.StatusUnauthorized,
// 			Problem: &response.HttpProblem{
// 				Type:     oaerrors.ErrUnauthorized.GetCode(),
// 				Title:    oaerrors.ErrUnauthorized.GetMessage(),
// 				Instance: c.Request.URL.Path,
// 			},
// 		})
// 		return false
// 	}
// 	publicKeySet, err := jwt.GetPublicKeysFromAuthService(conn.GetConnection())
// 	if err != nil {
// 		SendError(c, &response.HTTPError{
// 			Status: http.StatusUnauthorized,
// 			Problem: &response.HttpProblem{
// 				Type:     oaerrors.ErrUnauthorized.GetCode(),
// 				Title:    oaerrors.ErrUnauthorized.GetMessage(),
// 				Instance: c.Request.URL.Path,
// 			},
// 		})
// 		return false
// 	}
// 	hCtx.Logger.Info("Public key set retrieved from auth service")

// 	// Create JWT config
// 	jwtConfig := &jwt.JWTConfig{
// 		TokenString:  tokenString,
// 		PublicKeySet: publicKeySet,
// 	}

// 	// Parse and validate token using JWKS from auth service
// 	claims, oaerror := jwt.ParseToken(tokenString, jwtConfig, hCtx.Logger)
// 	if oaerror != nil {
// 		SendError(c, &response.HTTPError{
// 			Status: http.StatusUnauthorized,
// 			Problem: &response.HttpProblem{
// 				Type:     oaerror.GetCode(),
// 				Title:    oaerror.GetMessage(),
// 				Instance: c.Request.URL.Path,
// 			},
// 		})
// 		return false
// 	}

// 	// Store enterprise ID in Gin context
// 	c.Set(constant.EnterpriseIDKey, claims.EnterpriseID)
// 	c.Set(constant.ScopesKey, claims.Scopes)

// 	// Create gRPC metadata with the claims
// 	md := metadata.New(map[string]string{
// 		constant.EnterpriseIDKey: claims.EnterpriseID,
// 	})

// 	// Create a context with this metadata
// 	ctx := metadata.NewOutgoingContext(c.Request.Context(), md)

// 	// Store the gRPC metadata context for later use in handlers
// 	c.Set(constant.GrpcMetadataCtxKey, ctx)

// 	return true
// }

// func (r *Router)
