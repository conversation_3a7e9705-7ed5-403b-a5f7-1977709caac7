package telemetry

// import (
// 	"context"

// 	"go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploghttp"
// 	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
// 	sdklog "go.opentelemetry.io/otel/sdk/log"
// 	"go.opentelemetry.io/otel/sdk/resource"
// 	"go.opentelemetry.io/otel/sdk/trace"
// 	sdktrace "go.opentelemetry.io/otel/sdk/trace"
// 	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
// )

// func newExporter(ctx context.Context) (trace.SpanExporter, error) {
// 	return otlptracegrpc.New(
// 		ctx,
// 		otlptracegrpc.WithInsecure(),
// 		otlptracegrpc.WithEndpoint("localhost:4317"),
// 	)
// }

// func NewTraceProvider(ctx context.Context) *sdktrace.TracerProvider {

// 	exp, err := newExporter(ctx)

// 	if err != nil {
// 		panic(err)
// 	}

// 	r := resource.NewWithAttributes(
// 		semconv.SchemaURL,
// 		semconv.ServiceNameKey.String("boilerplate-svc"),
// 		semconv.ServiceVersionKey.String("0.0.1"),
// 	)

// 	return sdktrace.NewTracerProvider(
// 		sdktrace.WithBatcher(exp),
// 		sdktrace.WithResource(r),
// 	)
// }

// func newLogExporter(ctx context.Context) (sdklog.Exporter, error) {
// 	return otlploghttp.New(
// 		ctx,
// 		otlploghttp.WithInsecure(),
// 		otlploghttp.WithEndpoint("localhost:4318"),
// 	)
// }

// func newMeterProvider(res *resource.Resource) (*metric.MeterProvider, error) {
// 	metricExporter, err := return prometheus.New()
// 	// new promethe
// 	// metricExporter, err :=
// 	// if err != nil {
// 	// 	return nil, err
// 	// }

// 	metric.NewMeterProvider()

// 	// meterProvider := metric.NewMeterProvider(
// 	// 	metric.WithResource(res),
// 	// 	metric.WithReader(metric.NewPeriodicReader(metricExporter,
// 	// 		// Default is 1m. Set to 3s for demonstrative purposes.
// 	// 		metric.WithInterval(3*time.Second))),
// 	// )
// 	// return meterProvider, nil
// }

// func NewLogProvider(ctx context.Context) *sdklog.LoggerProvider {

// 	exp, err := newLogExporter(ctx)

// 	if err != nil {
// 		panic(err)
// 	}

// 	r := resource.NewWithAttributes(
// 		semconv.SchemaURL,
// 		semconv.ServiceNameKey.String("boilerplate-svc"),
// 		semconv.ServiceVersionKey.String("0.0.1"),
// 	)

// 	return sdklog.NewLoggerProvider(
// 		sdklog.WithProcessor(sdklog.NewBatchProcessor(exp)),
// 		sdklog.WithResource(r),
// 	)
// }
