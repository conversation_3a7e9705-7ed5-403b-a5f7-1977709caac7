package telemetry

import (
	"context"
	stdlog "log"

	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploghttp"
	"go.opentelemetry.io/otel/exporters/stdout/stdoutlog"

	"go.opentelemetry.io/otel/sdk/log"
	"go.opentelemetry.io/otel/sdk/resource"
)

func NewOtlpLogExporter(ctx context.Context) (log.Exporter, error) {
	return otlploghttp.New(
		ctx,
		otlploghttp.WithInsecure(),
		otlploghttp.WithEndpoint(config.GetConfig().OtlpCollectorHttp),
	)
}

func NewStdOutLogExporter() (*stdoutlog.Exporter, error) {
	return stdoutlog.New()
}

func NewLoggerProvider(res *resource.Resource) (*log.LoggerProvider, error) {
	var exporter log.Exporter

	// exporter, err := NewStdOutLogExporter()
	exporter, err := NewOtlpLogExporter(context.Background())
	if err != nil {
		stdlog.Fatalf("Failed to initialize stdout log exporter : %v", err)
		panic(err)
	}

	loggerProvider := log.NewLoggerProvider(
		log.WithResource(res),
		log.WithProcessor(
			log.NewBatchProcessor(exporter),
		),
	)

	return loggerProvider, nil
}
