package telemetry

import (
	"context"
	"time"

	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/exporters/stdout/stdoutmetric"
	otelmetric "go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
)

var (
	fixedAttributes []attribute.KeyValue
	// HTTP metrics
	httpSuccessCounter        otelmetric.Int64Counter
	httpErrorCounter          otelmetric.Int64Counter
	httpRequestLatencyCounter otelmetric.Int64Histogram
	// gRPC metrics
	grpcSuccessCounter otelmetric.Int64Counter
	grpcErrorCounter   otelmetric.Int64Counter
)

// initMetrics initializes all metrics with the global meter provider
func initMetrics() error {
	meter := otel.GetMeterProvider().Meter(config.GetConfig().SvcName)
	var err error

	// Initialize HTTP metrics
	httpSuccessCounter, err = meter.Int64Counter(
		"http_success_response_count",
		otelmetric.WithDescription("Count of successful HTTP Requests"),
		otelmetric.WithUnit("count"),
	)
	if err != nil {
		return err
	}

	httpErrorCounter, err = meter.Int64Counter(
		"http_error_response_count",
		otelmetric.WithDescription("Count of erroneous HTTP Requests"),
		otelmetric.WithUnit("count"),
	)
	if err != nil {
		return err
	}

	// Initialize gRPC metrics
	grpcSuccessCounter, err = meter.Int64Counter(
		"grpc_success_response_count",
		otelmetric.WithDescription("Count of successful gRPC Requests"),
		otelmetric.WithUnit("count"),
	)
	if err != nil {
		return err
	}

	grpcErrorCounter, err = meter.Int64Counter(
		"grpc_error_response_count",
		otelmetric.WithDescription("Count of failed gRPC Requests"),
		otelmetric.WithUnit("count"),
	)
	if err != nil {
		return err
	}

	httpRequestLatencyCounter, err = meter.Int64Histogram(
		"http_request_latency",
		otelmetric.WithDescription("Latency of HTTP Requests"),
		otelmetric.WithUnit("ms"),
	)
	if err != nil {
		return err
	}

	return nil
}

// MetricService provides methods to increment various metrics
type MetricService struct{}

// NewMetricService creates a new metric service
func NewMetricService() *MetricService {
	return &MetricService{}
}

// Helper to combine fixed and dynamic labels
func (m *MetricService) combineLabels(method, path string, additionalLabels ...attribute.KeyValue) []attribute.KeyValue {
	labels := append(fixedAttributes,
		attribute.String("method", method),
		attribute.String("path", path),
	)
	return append(labels, additionalLabels...)
}

// IncrementHTTPSuccess increments the HTTP success counter
func (m *MetricService) IncrementHTTPSuccess(ctx context.Context, method, path string) {
	httpSuccessCounter.Add(ctx, 1, otelmetric.WithAttributes(
		m.combineLabels(method, path)...,
	))
}

// IncrementHTTPError increments the HTTP error counter
func (m *MetricService) IncrementHTTPError(ctx context.Context, method, path string) {
	httpErrorCounter.Add(ctx, 1, otelmetric.WithAttributes(
		m.combineLabels(method, path)...,
	))
}

// IncrementGRPCSuccess increments the gRPC success counter
func (m *MetricService) IncrementGRPCSuccess(ctx context.Context, method, path string) {
	grpcSuccessCounter.Add(ctx, 1, otelmetric.WithAttributes(
		m.combineLabels(method, path)...,
	))
}

// IncrementGRPCError increments the gRPC error counter
func (m *MetricService) IncrementGRPCError(ctx context.Context, method, path string) {
	grpcErrorCounter.Add(ctx, 1, otelmetric.WithAttributes(
		m.combineLabels(method, path)...,
	))
}

// RecordRequestLatency records the request latency
func (m *MetricService) RecordRequestLatency(ctx context.Context, method, path string, duration int64, status int) {
	statusLabel := "success"
	if status >= 400 {
		statusLabel = "error"
	}

	httpRequestLatencyCounter.Record(ctx, duration, otelmetric.WithAttributes(
		m.combineLabels(method, path, attribute.String("status", statusLabel))...,
	))
}

func NewPromMetricExporter() (metric.Reader, error) {
	return prometheus.New()
}

func NewStdOutMetricExporter() (metric.Exporter, error) {
	return stdoutmetric.New()
}

func NewMeterProvider(res *resource.Resource) (*metric.MeterProvider, error) {
	var reader metric.Reader
	cfg := config.GetConfig()

	if config.GetConfig().Environment == config.Dev {
		r, err := NewStdOutMetricExporter()
		if err != nil {
			// Add a log line here
			panic(err)
		}

		reader = metric.NewPeriodicReader(
			r,
			metric.WithInterval(10*time.Minute),
		)
	} else {
		r, err := NewPromMetricExporter()
		if err != nil {
			// Add a log line here
			panic(err)
		}

		reader = r
	}

	// Set the fixed attributes of the meter provider
	fixedAttributes = []attribute.KeyValue{
		attribute.String("service_name", cfg.SvcName),
		attribute.String("env", cfg.Environment),
	}

	meterProvider := metric.NewMeterProvider(
		metric.WithResource(res),
		metric.WithReader(reader),
	)

	// Initialize metrics after setting up the provider
	if err := initMetrics(); err != nil {
		return nil, err
	}

	return meterProvider, nil
}

type MetricMeter struct {
	Meter otelmetric.Meter
}

// NewMetricMeter creates a new metric meter with the given name
func NewMetricMeter(name string) *MetricMeter {
	return &MetricMeter{
		Meter: otel.Meter(name),
	}
}

// Counter methods
func (m *MetricMeter) CreateInt64Counter(name, description, unit string) (otelmetric.Int64Counter, error) {
	return m.Meter.Int64Counter(
		name,
		otelmetric.WithDescription(description),
		otelmetric.WithUnit(unit),
	)
}

func (m *MetricMeter) CreateFloat64Counter(name, description, unit string) (otelmetric.Float64Counter, error) {
	return m.Meter.Float64Counter(
		name,
		otelmetric.WithDescription(description),
		otelmetric.WithUnit(unit),
	)
}

// Histogram methods
func (m *MetricMeter) CreateInt64Histogram(name, description, unit string) (otelmetric.Int64Histogram, error) {
	return m.Meter.Int64Histogram(
		name,
		otelmetric.WithDescription(description),
		otelmetric.WithUnit(unit),
	)
}

func (m *MetricMeter) CreateFloat64Histogram(name, description, unit string) (otelmetric.Float64Histogram, error) {
	return m.Meter.Float64Histogram(
		name,
		otelmetric.WithDescription(description),
		otelmetric.WithUnit(unit),
	)
}

// UpDownCounter methods
func (m *MetricMeter) CreateInt64UpDownCounter(name, description, unit string) (otelmetric.Int64UpDownCounter, error) {
	return m.Meter.Int64UpDownCounter(
		name,
		otelmetric.WithDescription(description),
		otelmetric.WithUnit(unit),
	)
}

func (m *MetricMeter) CreateFloat64UpDownCounter(name, description, unit string) (otelmetric.Float64UpDownCounter, error) {
	return m.Meter.Float64UpDownCounter(
		name,
		otelmetric.WithDescription(description),
		otelmetric.WithUnit(unit),
	)
}

// Add methods for different metric types
func (m *MetricMeter) AddInt64Counter(counter otelmetric.Int64Counter, ctx context.Context, value int64, attributes ...attribute.KeyValue) {
	allAttributes := append(fixedAttributes, attributes...)
	counter.Add(ctx, value, otelmetric.WithAttributes(allAttributes...))
}

func (m *MetricMeter) AddFloat64Counter(counter otelmetric.Float64Counter, ctx context.Context, value float64, attributes ...attribute.KeyValue) {
	allAttributes := append(fixedAttributes, attributes...)
	counter.Add(ctx, value, otelmetric.WithAttributes(allAttributes...))
}

func (m *MetricMeter) RecordInt64Histogram(histogram otelmetric.Int64Histogram, ctx context.Context, value int64, attributes ...attribute.KeyValue) {
	allAttributes := append(fixedAttributes, attributes...)
	histogram.Record(ctx, value, otelmetric.WithAttributes(allAttributes...))
}

func (m *MetricMeter) RecordFloat64Histogram(histogram otelmetric.Float64Histogram, ctx context.Context, value float64, attributes ...attribute.KeyValue) {
	allAttributes := append(fixedAttributes, attributes...)
	histogram.Record(ctx, value, otelmetric.WithAttributes(allAttributes...))
}

func (m *MetricMeter) AddInt64UpDownCounter(counter otelmetric.Int64UpDownCounter, ctx context.Context, value int64, attributes ...attribute.KeyValue) {
	allAttributes := append(fixedAttributes, attributes...)
	counter.Add(ctx, value, otelmetric.WithAttributes(allAttributes...))
}

func (m *MetricMeter) AddFloat64UpDownCounter(counter otelmetric.Float64UpDownCounter, ctx context.Context, value float64, attributes ...attribute.KeyValue) {
	allAttributes := append(fixedAttributes, attributes...)
	counter.Add(ctx, value, otelmetric.WithAttributes(allAttributes...))
}
