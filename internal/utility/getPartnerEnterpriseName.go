package utility

import (
	"context"
	"slices"

	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-protos/go/oa-enterprise/v0/enterprise"
	"go.uber.org/zap"
)

// PartnerEnterpriseInfo holds partner and enterprise name information
type PartnerEnterpriseInfo struct {
	PartnerName    string
	EnterpriseName string
}

// GetPartnerEnterpriseName retrieves partner and enterprise names based on role and partner IDs
func GetPartnerEnterpriseName(
	ctx context.Context,
	grpcClients *grpc.GrpcClientManager,
	logger *logger.Logger,
	role string,
	enterpriseType string,
	partnerIds []string,
) (map[string]PartnerEnterpriseInfo, error) {

	// Determine if we should show partner and enterprise names based on role
	shouldShowPartnerName := false
	shouldShowEnterpriseName := false

	if slices.Contains([]string{"admin", "super_admin", "operations_admin", "resource_manager"}, role) {
		shouldShowPartnerName = true
		if enterpriseType == "LE" {
			shouldShowEnterpriseName = true
		}
	}

	partnerMap := make(map[string]PartnerEnterpriseInfo)

	// Return empty map if we don't need to show partner names
	if !shouldShowPartnerName || len(partnerIds) == 0 {
		return partnerMap, nil
	}

	// Deduplicate partner IDs
	uniquePartnerIds := make([]string, 0)
	for _, partnerId := range partnerIds {
		// Only add partner ID if it's not empty and not already in the slice
		if partnerId != "" && !slices.Contains(uniquePartnerIds, partnerId) {
			uniquePartnerIds = append(uniquePartnerIds, partnerId)
		}
	}

	// Return empty map if no valid partner IDs found
	if len(uniquePartnerIds) == 0 {
		return partnerMap, nil
	}

	// Get enterprise gRPC client
	conn, err := grpcClients.Get(enterprise.Enterprise_ServiceDesc.ServiceName)
	if err != nil {
		logger.Error("Error getting enterprise client", zap.Error(err))
		return nil, err
	}

	client := enterprise.NewEnterpriseClient(conn)

	// Make gRPC call to get partner information
	partnerRes, err := client.GetPartnerByIds(ctx, &enterprise.GetPartnerByIdsRequest{
		PartnerIds: uniquePartnerIds,
	})
	if err != nil {
		logger.Error("Error getting partners", zap.Error(err))
		return nil, err
	}

	// Build the partner map
	for _, partner := range partnerRes.Partners {
		info := PartnerEnterpriseInfo{
			PartnerName: partner.FirstName + " " + partner.LastName,
		}

		// Add enterprise name if we should show it
		if shouldShowEnterpriseName && partner.EnterpriseDetails != nil {
			info.EnterpriseName = partner.EnterpriseDetails.BrandName
		}

		partnerMap[partner.Id] = info
	}

	return partnerMap, nil
}
