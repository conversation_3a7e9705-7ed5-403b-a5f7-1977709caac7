package utility

import (
	"context"
	"slices"

	"github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-protos/go/oa-enterprise/v0/enterprise"
	"go.uber.org/zap"
)

// PartnerEnterpriseInfoV1 holds partner and enterprise name information
type PartnerEnterpriseInfoV1 struct {
	PartnerName    string
	EnterpriseName string
}

// GetPartnerEnterpriseNameV1 retrieves partner and enterprise names based on role and partner IDs
func GetPartnerEnterpriseNameV1(
	ctx context.Context,
	grpcClients *grpc.GrpcClientManager,
	logger *logger.Logger,
	role string,
	enterpriseType string,
	partnerEnterpriseIds []struct {
		PartnerId    string
		EnterpriseId string
	},
) (map[string]PartnerEnterpriseInfoV1, error) {

	// Determine if we should show partner and enterprise names based on role
	shouldShowPartnerName := false
	shouldShowEnterpriseName := false

	if slices.Contains([]string{"admin", "super_admin", "operations_admin", "resource_manager"}, role) {
		shouldShowPartnerName = true
		if enterpriseType == "LE" {
			shouldShowEnterpriseName = true
		}
	}

	partnerMap := make(map[string]PartnerEnterpriseInfoV1)

	// Return empty map if we don't need to show partner names
	if !shouldShowPartnerName || len(partnerEnterpriseIds) == 0 {
		return partnerMap, nil
	}

	// Deduplicate partner IDs
	uniquePartnerIds := make([]struct {
		PartnerId    string
		EnterpriseId string
	}, 0)
	for _, partnerEnterpriseId := range partnerEnterpriseIds {
		// Only add partner ID if it's not empty and not already in the slice
		if partnerEnterpriseId.PartnerId != "" && !slices.Contains(uniquePartnerIds, partnerEnterpriseId) {
			uniquePartnerIds = append(uniquePartnerIds, partnerEnterpriseId)
		}
	}

	// Return empty map if no valid partner IDs found
	if len(uniquePartnerIds) == 0 {
		return partnerMap, nil
	}

	// Get enterprise gRPC client
	conn, err := grpcClients.Get(enterprise.Enterprise_ServiceDesc.ServiceName)
	if err != nil {
		logger.Error("Error getting enterprise client", zap.Error(err))
		return nil, err
	}

	client := enterprise.NewEnterpriseClient(conn)

	filteredPartnerIds := make([]string, 0)
	for _, partnerId := range uniquePartnerIds {
		if partnerId.PartnerId != "9999999999999999" {
			filteredPartnerIds = append(filteredPartnerIds, partnerId.PartnerId)
		}
	}

	// Make gRPC call to get partner information
	partnerRes, err := client.GetPartnerByIds(ctx, &enterprise.GetPartnerByIdsRequest{
		PartnerIds: filteredPartnerIds,
	})
	if err != nil {
		logger.Error("Error getting partners", zap.Error(err))
		return nil, err
	}

	// Build the partner map
	for _, partnerInfo := range uniquePartnerIds {
		var matchingPartner *enterprise.PartnerDetails
		for _, p := range partnerRes.Partners {
			if p.Id == partnerInfo.PartnerId {
				matchingPartner = p
				break
			}
		}
		if matchingPartner == nil && partnerInfo.PartnerId != "9999999999999999" {
			continue
		}
		info := PartnerEnterpriseInfoV1{}
		if partnerInfo.PartnerId == "9999999999999999" {
			info = PartnerEnterpriseInfoV1{
				PartnerName: "N/A",
			}
			if shouldShowEnterpriseName {
				enterpriseRes, err := client.GetEnterpriseById(ctx, &enterprise.GetEnterpriseByIdRequest{
					EnterpriseId: partnerInfo.EnterpriseId,
				})
				if err != nil {
					logger.Error("Error getting enterprise", zap.Error(err))
					return nil, err
				}
				info.EnterpriseName = enterpriseRes.BrandName
			}
			partnerMap[partnerInfo.EnterpriseId] = info
		} else {
			info = PartnerEnterpriseInfoV1{
				PartnerName: matchingPartner.FirstName + " " + matchingPartner.LastName,
			}

			// Add enterprise name if we should show it
			if shouldShowEnterpriseName && matchingPartner.EnterpriseDetails != nil {
				info.EnterpriseName = matchingPartner.EnterpriseDetails.BrandName
			}
			partnerMap[partnerInfo.PartnerId] = info
		}

	}

	return partnerMap, nil
}
