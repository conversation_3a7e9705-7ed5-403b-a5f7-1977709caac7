package middleware

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/golang/protobuf/ptypes/empty"

	// oaerrors "github.com/oneassure-tech/oa-errors/go"

	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	client "github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	j "github.com/oneassure-tech/oa-gateway-svc/internal/jwt"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

func BearerAuthMiddleware(claimKeys []string, allowedAudience string) func(c *gin.Context) bool {
	return func(c *gin.Context) bool {

		logger := logger.GetLogger().GetScopedLogger("BearerAuthMiddleware")
		logger.Info("Starting bearer authentication middleware", zap.String("path", c.Request.URL.Path))

		respondUnauthorized := func(c *gin.Context) {
			logger.Info("Responding with unauthorized status")
			// response.SendError(c, &response.HTTPError{
			// 	Status: http.StatusUnauthorized,
			// 	Problem: &response.HttpProblem{
			// 		Type:     oaerrors.ErrUnauthorized.GetCode(),
			// 		Title:    oaerrors.ErrUnauthorized.GetMessage(),
			// 		Instance: c.Request.URL.Path,
			// 	},
			// })
		}

		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logger.Info("Missing Authorization header")
			respondUnauthorized(c)
			return false
		}

		// Check if the header has the Bearer prefix
		if !strings.HasPrefix(authHeader, "Bearer ") {
			logger.Info("Missing Bearer prefix in Authorization header")
			respondUnauthorized(c)
			return false
		}

		// Get the token string by removing the "Bearer " prefix
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")

		cfg := &client.ClientConfig{
			ServiceURL: config.GetConfig().JWT.PublicKeyURL,
		}

		grpcClient, err := client.NewGrpcClient(cfg)
		if err != nil {
			logger.Error("Failed to create gRPC client", zap.Error(err))
			respondUnauthorized(c)
			return false
		}

		// logger.Info("Parsing token insecurely to extract claims")
		// _, accessTokenClaims, err := j.InsecureParseToken(tokenString)
		// if err != nil {
		// 	logger.Error("Failed to insecurely parse token for initial claims", zap.Error(err))
		// 	respondUnauthorized(c)
		// 	return false
		// }
		// logger.Info("Successfully parsed token insecurely")

		// accessTokenId := (*accessTokenClaims)["jti"].(string)
		// logger.Info("Extracted token ID", zap.String("token_id", accessTokenId))

		// Get gRPC connection to auth service
		authClient := auth.NewAuthClient(grpcClient.GetConnection())

		response, err := authClient.GetJWKS(context.Background(), &empty.Empty{})
		if err != nil {
			fmt.Println("Failed to get JWKS from auth service 1243", err)
			// logger.Error("Failed to get JWKS from auth service", zap.Error(err))
			respondUnauthorized(c)
			return false
		}

		jwtParser := j.GetJWTParser(
			jwt.WithAudience(allowedAudience),
		)

		_, err = jwtParser.Parse(tokenString, j.GetPublicKey(response.Keys))
		if err != nil {
			if errors.Is(err, jwt.ErrTokenExpired) {
				logger.Info("Token is expired")
				respondUnauthorized(c)
				return false
			}

			logger.Error("Failed to parse token", zap.Error(err))
			respondUnauthorized(c)
			return false
		}

		_, parsedClaims, err := j.InsecureParseToken(tokenString)
		if err != nil {
			logger.Error("Failed to insecurely parse token", zap.Error(err))
			respondUnauthorized(c)
			return false
		}

		tokenId := (*parsedClaims)["jti"].(string)

		blacklistResponse, err := authClient.VerifyJWTBlacklist(context.Background(), &auth.VerifyJWTBlacklistRequest{
			TokenId: tokenId,
		})
		if err != nil {
			logger.Error("Failed to verify JWT blacklist", zap.Error(err))
			respondUnauthorized(c)
			return false
		}

		if blacklistResponse.IsBlacklisted {
			logger.Info("Token is blacklisted", zap.String("token_id", tokenString))
			respondUnauthorized(c)
			return false
		}

		requiredClaims := []string{constant.EnterpriseIDKey}
		optionalClaims := claimKeys

		extractedClaims, err := extractClaims(parsedClaims, requiredClaims, optionalClaims)
		if err != nil {
			logger.Error("Failed to extract claims", zap.Error(err))
			respondUnauthorized(c)
			return false
		}

		fmt.Println("extractedClaims", extractedClaims)

		// Create gRPC metadata with the claims
		md := metadata.New(extractedClaims)
		md.Append("role", "public")
		md.Append("partner_id", extractedClaims["sub"])
		c.Set("role", "public")
		ctx := metadata.NewOutgoingContext(c.Request.Context(), md)
		c.Set(constant.GrpcMetadataCtxKey, ctx)

		// Create gin context with the claims
		for key, value := range extractedClaims {
			c.Set(key, value)
		}

		logger.Info("Bearer authentication middleware completed successfully")
		c.Next()
		return true
	}
}

func extractClaims(parsedClaims *jwt.MapClaims, requiredClaims []string, optionalClaims []string) (map[string]string, error) {
	logger := logger.GetLogger().GetScopedLogger("extractClaims")
	logger.Info("Starting claims extraction",
		zap.Strings("required_claims", requiredClaims),
		zap.Strings("optional_claims", optionalClaims))

	extractedClaims := make(map[string]string)

	// Extract the required claims always
	for _, key := range requiredClaims {
		if value, ok := (*parsedClaims)[key]; ok {
			extractedClaims[key] = value.(string)
		} else {
			logger.Error("Required claim not found", zap.String("claim_key", key))
			return nil, fmt.Errorf("required claim not found in parsed claims: %s", key)
		}
	}

	// Extract the optional claims if present
	for _, key := range optionalClaims {
		if value, ok := (*parsedClaims)[key]; ok {
			extractedClaims[key] = value.(string)
		} else {
			logger.Error("Optional claim not found", zap.String("claim_key", key))
		}
	}

	return extractedClaims, nil
}
