package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func InitMiddlewares(g *gin.RouterGroup, routerType string) {
	switch routerType {
	case constant.HTTP_PUBLIC:
		// CORS is now handled in router.go
	case constant.HTTP_ADMIN:
		// CORS is now handled in router.go
	case constant.HTTP_API:
		// g.Use(JWTAuthMiddleware)
	}

	g.Use(otelgin.Middleware("otel-otlp-go-service"))
}
