package middleware

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/golang/protobuf/ptypes/empty"

	// oaerrors "github.com/oneassure-tech/oa-errors/go"

	"github.com/oneassure-tech/oa-gateway-svc/internal/config"
	"github.com/oneassure-tech/oa-gateway-svc/internal/constant"
	client "github.com/oneassure-tech/oa-gateway-svc/internal/grpc"
	j "github.com/oneassure-tech/oa-gateway-svc/internal/jwt"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-protos/go/oa-authentication/v0/auth"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

func CookieAuthMiddleware(claimKeys []string, allowedAudience string, requiredPermissions []string) func(c *gin.Context) bool {

	return func(c *gin.Context) bool {

		logger := logger.GetLogger().GetScopedLogger("CookieAuthMiddleware")
		logger.Info("Starting cookie authentication middleware", zap.String("path", c.Request.URL.Path))

		respondUnauthorized := func(c *gin.Context) {
			logger.Info("Responding with unauthorized status")
			// response.SendError(c, &response.HTTPError{
			// 	Status: http.StatusUnauthorized,
			// 	Problem: &response.HttpProblem{
			// 		Type:     oaerrors.ErrUnauthorized.GetCode(),
			// 		Title:    oaerrors.ErrUnauthorized.GetMessage(),
			// 		Instance: c.Request.URL.Path,
			// 	},
			// })
		}

		// Get token from Cookie
		token, err := c.Cookie("token")
		if err != nil {
			logger.Error("Failed to get Cookie token", zap.Error(err))
			respondUnauthorized(c)
			return false
		}
		logger.Info("Got Cookie", zap.String("cookie", token))
		if token == "" {
			logger.Info("Missing Cookie token")
			respondUnauthorized(c)
			return false
		}

		tokenParts := strings.Split(token, ",")
		if len(tokenParts) != 2 {
			logger.Info("Invalid Cookie token")
			respondUnauthorized(c)
			return false
		}

		accessToken := tokenParts[0]
		refreshToken := tokenParts[1]

		cfg := &client.ClientConfig{
			ServiceURL: config.GetConfig().JWT.PublicKeyURL,
		}

		grpcClient, err := client.NewGrpcClient(cfg)
		if err != nil {
			logger.Error("Failed to create gRPC client", zap.Error(err))
			respondUnauthorized(c)
			return false
		}

		authClient := auth.NewAuthClient(grpcClient.GetConnection())

		response, err := authClient.GetJWKS(context.Background(), &empty.Empty{})
		if err != nil {
			logger.Error("Failed to get JWKS from auth service", zap.Error(err))
			respondUnauthorized(c)
			return false
		}

		jwtParser := j.GetJWTParser(
			jwt.WithAudience(allowedAudience),
		)

		accessTokenExpired := false
		_, err = jwtParser.Parse(accessToken, j.GetPublicKey(response.Keys))
		if err != nil {
			if errors.Is(err, jwt.ErrTokenExpired) {
				accessTokenExpired = true
			} else {
				logger.Error("Failed to parse token", zap.Error(err))
				respondUnauthorized(c)
				return false
			}
		}

		var claimsToUse *jwt.MapClaims
		role := ""
		enterpriseType := ""

		if !accessTokenExpired {
			// Use the valid access token for claims
			_, parsedClaims, err := j.InsecureParseToken(accessToken)
			if err != nil {
				logger.Error("Failed to insecurely parse token", zap.Error(err))
				respondUnauthorized(c)
				return false
			}
			tokenId := (*parsedClaims)["jti"].(string)
			// role := (*parsedClaims)["role"].(string)

			authorizeResponse, err := authClient.Authorize(context.Background(), &auth.AuthorizeRequest{
				// Role:                role,
				Sub:                 (*parsedClaims)["sub"].(string),
				TokenId:             tokenId,
				RequiredPermissions: requiredPermissions,
			})
			if err != nil {
				logger.Error("Failed to authorize user", zap.Error(err))
				respondUnauthorized(c)
				return false
			}

			if !authorizeResponse.IsAuthorized {
				logger.Info("User does not have the required permissions", zap.String("token_id", tokenId))
				respondUnauthorized(c)

				fmt.Print("Responded with unauthorized.... ")
				return false
			}

			claimsToUse = parsedClaims
			role = authorizeResponse.Role
			enterpriseType = authorizeResponse.EnterpriseType
		} else {
			_, parsedClaimsAccessToken, err := j.InsecureParseToken(accessToken)
			if err != nil {
				logger.Error("Failed to insecurely parse token", zap.Error(err))
				respondUnauthorized(c)
				return false
			}
			tokenId := (*parsedClaimsAccessToken)["jti"].(string)

			authorizeResponse, err := authClient.Authorize(context.Background(), &auth.AuthorizeRequest{
				Sub:                 (*parsedClaimsAccessToken)["sub"].(string),
				TokenId:             tokenId,
				RequiredPermissions: requiredPermissions,
			})
			if err != nil {
				logger.Error("Failed to authorize user", zap.Error(err))
				respondUnauthorized(c)
				return false
			}

			fmt.Println("authorizeResponse", authorizeResponse)

			if !authorizeResponse.IsAuthorized {
				logger.Info("User does not have the required permissions", zap.String("token_id", tokenId))
				respondUnauthorized(c)

				fmt.Print("Responded with unauthorized.... ")
				return false
			}

			_, err = jwtParser.Parse(refreshToken, j.GetPublicKey(response.Keys))
			if err != nil {
				if errors.Is(err, jwt.ErrTokenExpired) {
					logger.Info("Refresh token is expired")
					respondUnauthorized(c)
					return false
				}

				logger.Error("Failed to parse refresh token", zap.Error(err))
				respondUnauthorized(c)
				return false
			}

			// Parse the refresh token to get its claims
			_, parsedClaims, err := j.InsecureParseToken(refreshToken)
			if err != nil {
				logger.Error("Failed to parse refresh token", zap.Error(err))
				respondUnauthorized(c)
				return false
			}

			// Extract the enterprise id from the refresh token
			enterpriseId := (*parsedClaims)["enterprise_id"].(string)

			role = authorizeResponse.Role
			enterpriseType = authorizeResponse.EnterpriseType
			md := metadata.New(map[string]string{
				"enterprise_id": enterpriseId,
			})
			ctx := metadata.NewOutgoingContext(c.Request.Context(), md)

			// try to refresh the access token
			refreshTokenResponse, err := authClient.RefreshJWTToken(ctx, &auth.RefreshJWTTokenRequest{
				RefreshToken: refreshToken,
			})
			if err != nil {
				logger.Error("Failed to refresh access token", zap.Error(err))
				respondUnauthorized(c)
				return false
			}

			// Parse the new access token to get its claims
			newAccessToken := refreshTokenResponse.AccessToken
			_, newAccessTokenClaims, err := j.InsecureParseToken(newAccessToken)
			if err != nil {
				logger.Error("Failed to parse new access token", zap.Error(err))
				respondUnauthorized(c)
				return false
			}

			// Use the new access token claims
			claimsToUse = newAccessTokenClaims

			var domain string
			// Hardcode domain configuration
			if config.GetConfig().Environment == "dev" {
				domain = "localhost"
			} else if config.GetConfig().Environment == "non-prod" {
				domain = "non-prod.oneassure.in"
			} else {
				domain = "portal.oneassure.in"
			}

			var secure bool
			if config.GetConfig().Environment != "dev" {
				secure = true
			}

			maxAge := time.Until(time.Now().Add(time.Hour * 24 * 2)).Seconds()

			c.SetCookie("token", fmt.Sprintf("%s,%s", refreshTokenResponse.AccessToken, refreshTokenResponse.RefreshToken), int(maxAge), "/", domain, secure, true)

		}

		// logger.Info("Parsing token insecurely to extract claims")
		// _, accessTokenClaims, err := j.InsecureParseToken(accessToken)
		// if err != nil {
		// 	logger.Error("Failed to insecurely parse token for initial claims", zap.Error(err))
		// 	respondUnauthorized(c)
		// 	return false
		// }
		// logger.Info("Successfully parsed token insecurely")
		requiredClaims := []string{constant.EnterpriseIDKey}
		optionalClaims := claimKeys

		// Use the appropriate claims based on token state
		extractedClaims, err := extractClaims(claimsToUse, requiredClaims, optionalClaims)
		if err != nil {
			logger.Error("Failed to extract claims", zap.Error(err))
			respondUnauthorized(c)
			return false
		}

		// Extract enterprise_id and partner_id (from sub claim)
		enterpriseId, ok := (*claimsToUse)["enterprise_id"].(string)
		if !ok {
			logger.Error("enterprise_id not found in token claims")
			respondUnauthorized(c)
			return false
		}

		partnerId, ok := (*claimsToUse)["sub"].(string)
		if !ok {
			logger.Error("sub (partner_id) not found in token claims")
			respondUnauthorized(c)
			return false
		}

		logger.Info("Successfully extracted claims",
			zap.String("enterprise_id", enterpriseId),
			zap.String("partner_id", partnerId))

		md := metadata.New(map[string]string{
			"enterprise_id":   enterpriseId,
			"partner_id":      partnerId,
			"role":            role,
			"enterprise_type": enterpriseType,
		})
		md.Set("token", accessToken)
		ctx := metadata.NewOutgoingContext(c.Request.Context(), md)
		c.Set(constant.GrpcMetadataCtxKey, ctx)

		// Create gin context with the claims
		c.Set("enterprise_id", enterpriseId)
		c.Set("partner_id", partnerId)
		c.Set("role", role)
		c.Set("enterprise_type", enterpriseType)
		for key, value := range extractedClaims {
			c.Set(key, value)
		}

		c.Next()
		return true
	}
}
