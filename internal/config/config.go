package config

import "github.com/spf13/viper"

var _ViperConfig Config = &_viperConfig{}

const (
	dbDriver = "postgres"
	Dev      = "dev"
)

func New() Config {
	viper.SetDefault("database.driver", dbDriver)
	return _ViperConfig
}

func GetConfig() Config {
	if _ViperConfig != nil {
		return _ViperConfig
	}

	// Add a log line here
	panic("Config is not initilized")
}

type Config = *_viperConfig

type _viperConfig struct {
	MaxVersion              uint8    `mapstructure:"max_version"`
	MinVersion              uint8    `mapstructure:"min_version"`
	Environment             string   `mapstructure:"environment"`
	HttpPublicPort          uint16   `mapstructure:"http_public_port"`
	HttpAdminPort           uint16   `mapstructure:"http_admin_port"`
	HttpCallbackPort        uint16   `mapstructure:"http_callback_port"`
	HttpRapidshortPort      uint16   `mapstructure:"http_rapidshort_port"`
	HttpRapidshortAdminPort uint16   `mapstructure:"http_rapidshort_admin_port"`
	RapidshortAdminToken    string   `mapstructure:"rapidshort_admin_token"`
	GrpcPort                uint16   `mapstructure:"grpc_port"`
	CorsOrigins             []string `mapstructure:"cors_origins"`
	AdminCorsOrigins        []string `mapstructure:"admin_cors_origins"`
	SvcName                 string   `mapstructure:"service_name"`
	HttpApiPort             uint16   `mapstructure:"http_api_port"`
	OtlpCollectorGrpc       string   `mapstructure:"otlp_collector_grpc"`
	OtlpCollectorHttp       string   `mapstructure:"otlp_collector_http"`
	JWT                     struct {
		PublicKeyURL string `mapstructure:"public_key_url"`
	} `mapstructure:"jwt"`
	Database struct {
		Driver   string `mapstructure:"driver"`
		Host     string `mapstructure:"host"`
		User     string `mapstructure:"user"`
		Password string `mapstructure:"pass"`
		Name     string `mapstructure:"name"`
		Port     string `mapstructure:"port"`
	} `mapstructure:"database"`
	AffinityService struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	} `mapstructure:"affinity_service"`
	AffinitySales struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	} `mapstructure:"affinity_sales"`
	Auth struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	} `mapstructure:"auth"`
	Forms struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	} `mapstructure:"forms"`
	Sales struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	}
	Recommendation struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	}
	CATALOGUE struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	}
	RAG struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	}
	PUBLIC_BASE_URL string `mapstructure:"public_base_url"`
	RAPIDSHORT      struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	}
	Enterprise struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	} `mapstructure:"enterprise"`
	MARKETING struct {
		Url  string `mapstructure:"url"`
		Port string `mapstructure:"port"`
	}
	Storage struct {
		RootPrefix    string `mapstructure:"root_prefix"`
		DefaultBucket string `mapstructure:"default_bucket"`
		Region        string `mapstructure:"region"`
	} `mapstructure:"storage"`
	HASURA struct {
		Url         string `mapstructure:"url"`
		Port        string `mapstructure:"port"`
		AdminSecret string `mapstructure:"admin_secret"`
	} `mapstructure:"hasura"`
}
