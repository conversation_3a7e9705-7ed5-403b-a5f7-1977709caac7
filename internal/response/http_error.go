package response

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-gateway-svc/internal/telemetry"
	oaerrors "github.com/oneassure-tech/oa-utilities/go/oa-errors"
	"go.uber.org/zap"
)

type HTTPError struct {
	Status  int16
	Problem *HttpProblem
}

func (h HTTPError) Error() string {
	return fmt.Sprint("An HTTP Error Occurred with Status Code : ", h.Status)
}

func NewHTTPError(Status int16, Problem *HttpProblem) *HTTPError {
	return &HTTPError{
		Status:  Status,
		Problem: Problem,
	}
}

type HttpProblem struct {
	Type           string      `json:"type"`
	Title          string      `json:"title"`
	Detail         string      `json:"detail,omitempty"`
	Instance       string      `json:"instance"`
	TraceId        *string     `json:"request_id,omitempty"`
	AdditionalInfo interface{} `json:"additional_info,omitempty"`
}

func GenericHttpInternalServerError(ctx *gin.Context) *HTTPError {
	return NewHTTPError(
		http.StatusInternalServerError,
		&HttpProblem{
			Type:     oaerrors.ErrCodeUnexpected.GetCode(),
			Title:    oaerrors.ErrCodeUnexpected.GetMessage(),
			Instance: ctx.Request.URL.Path,
		},
	)
}

func GenericHttpBadRequestError(ctx *gin.Context) *HTTPError {
	return NewHTTPError(
		http.StatusBadRequest,
		&HttpProblem{
			Type:     "E101",
			Title:    "Bad Request",
			Detail:   "The request was malformed or invalid.",
			Instance: ctx.Request.URL.Path,
		},
	)
}

func GenericHttpNotFoundError(ctx *gin.Context) *HTTPError {
	return NewHTTPError(
		http.StatusNotFound,
		&HttpProblem{
			Type:     oaerrors.ErrCodeNotFound.GetCode(),
			Title:    oaerrors.ErrCodeNotFound.GetMessage(),
			Instance: ctx.Request.URL.Path,
		},
	)
}

// SendError is a helper function for full error handling with logging and metrics
func SendError(ctx *gin.Context, e *HTTPError) {
	// Get handler context if available
	// routerCtx := ExtractRequestContext(ctx)
	// if routerCtx == nil {
	// 	// If no context available, fall back to direct error
	// 	sendDirectError(ctx, e)
	// 	return
	// }

	logger := logger.GetLogger().GetScopedLogger("SendError")

	m := telemetry.NewMetricService()

	logger.Info(fmt.Sprintf("❌ HTTP Request Failed [%s %s] (%d)",
		ctx.Request.Method, ctx.Request.URL.Path, e.Status),
		zap.Int16("status", e.Status),
	)

	// Send error response
	ctx.Header("Content-Type", "application/problem+json")
	ctx.AbortWithStatusJSON(int(e.Status), e.Problem)

	// Increment error counter
	m.IncrementHTTPError(context.Background(), ctx.Request.Method, ctx.Request.URL.Path)
}
