package response

// Status represents different status codes for different protocols
type Status struct {
	GrpcStatus       int
	HttpStatus       int
	DownstreamStatus *int32
}

// Problem represents the RFC 7807 problem details
type Problem struct {
	Type   string `json:"type"`
	Title  string `json:"title"`
	Detail string `json:"detail"`
	// Instance       string      `json:"instance"`
	// TraceId        *string     `json:"trace_id,omitempty"`
	AdditionalInfo interface{} `json:"additional_info,omitempty"`
}
