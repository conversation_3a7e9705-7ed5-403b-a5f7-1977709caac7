package response

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-gateway-svc/internal/logger"
	"github.com/oneassure-tech/oa-protos/go/oa-common/v0/common"
	oaerrors "github.com/oneassure-tech/oa-utilities/go/oa-errors"
	"go.uber.org/zap"
	epb "google.golang.org/genproto/googleapis/rpc/errdetails"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
)

type ErrorResponse struct {
	// Status is the transporter layer status code. Example: 400, 500, etc. for HTTP & 1, 2 for GRPC
	Status  *Status
	Problem *Problem
}

func (e *ErrorResponse) TransformToHTTPError(ctx *gin.Context) *HTTPError {
	log := logger.GetLogger().GetScopedLogger("http-error-transformer")

	if e.Status == nil {
		log.Error("Unable to transform HTTP error as e.Status is nil")
		return GenericHttpInternalServerError(ctx)
	}

	// Create a copy of Problem to modify AdditionalInfo if needed
	problem := &HttpProblem{
		Type:     e.Problem.Type,
		Title:    e.Problem.Title,
		Detail:   e.Problem.Detail,
		Instance: ctx.Request.URL.Path,
		// TraceId:  &traceId,
	}

	// Handle AdditionalInfo conversion if it's an Any
	if e.Problem.AdditionalInfo != nil {
		if anyVal, ok := e.Problem.AdditionalInfo.(*anypb.Any); ok {
			// Convert Any back to Go type
			val, err := fromAny(anyVal)
			if err != nil {
				log.Error("failed to convert Any to Go type",
					zap.Error(err),
					zap.Any("value", anyVal))
				// Continue without AdditionalInfo
			} else {
				problem.AdditionalInfo = val
			}
		} else {
			// Not an Any type, use as is
			problem.AdditionalInfo = e.Problem.AdditionalInfo
		}
	}

	if e.Status.DownstreamStatus != nil {
		if *e.Status.DownstreamStatus < 400 || *e.Status.DownstreamStatus > 599 {
			log.Error("Downstream status code is not a valid HTTP error status code", zap.Int32("status", *e.Status.DownstreamStatus))
			return GenericHttpInternalServerError(ctx)
		}

		return &HTTPError{
			Status:  int16(*e.Status.DownstreamStatus),
			Problem: problem,
		}
	}

	if e.Status.HttpStatus < 400 || e.Status.HttpStatus > 599 {
		log.Error("Status code is not a valid HTTP error status code", zap.Int("status", e.Status.HttpStatus))
		return GenericHttpInternalServerError(ctx)
	}

	return &HTTPError{
		Status:  int16(e.Status.HttpStatus),
		Problem: problem,
	}
}

func (e *ErrorResponse) TransformToGrpcError() error {
	log := logger.GetLogger().GetScopedLogger("grpc-error-transformer")

	// Create the gRPC error details
	errDetail := &common.Error{
		Type:   e.Problem.Type,
		Title:  e.Problem.Title,
		Detail: e.Problem.Detail,
	}

	// Handle additional info if present
	if e.Problem.AdditionalInfo != nil {
		// Since AdditionalInfo in Problem is interface{} and in common.Error is *anypb.Any,
		// we need to ensure it's already an *anypb.Any or convert it
		if anyVal, ok := e.Problem.AdditionalInfo.(*anypb.Any); ok {
			errDetail.AdditionalInfo = anyVal
		} else {
			// Convert using existing toAny function
			anyVal, err := toAny(e.Problem.AdditionalInfo)
			if err != nil {
				log.Error("failed to convert additional info to Any",
					zap.Error(err),
					zap.Any("value", e.Problem.AdditionalInfo))
			} else {
				errDetail.AdditionalInfo = anyVal
			}
		}
	}

	// Create status with appropriate code
	var st *status.Status
	if e.Status.DownstreamStatus != nil {
		st = status.New(
			codes.Code(*e.Status.DownstreamStatus),
			e.Problem.Title,
		)
	} else {
		st = status.New(
			codes.Code(e.Status.GrpcStatus),
			e.Problem.Title,
		)
	}

	// Add the error details
	// detail, err := anypb.New(errDetail)
	// if err != nil {
	// 	log.Error("failed to convert error detail to Any", zap.Error(err))
	// 	return st.Err()
	// }
	// detail.TypeUrl = "oa.common.v0.Error" // Override type URL

	// Add the error details using raw proto API to preserve our custom type URL
	// s, err := st.WithDetails(detail)

	stWithDetails, err := st.WithDetails(errDetail)
	if err != nil {
		log.Error("failed to create gRPC error with details", zap.Error(err))
		return st.Err()
	}

	return stWithDetails.Err()
}

// Deprecated: Use NewInternalServerError() instead. This function will be removed in a future version.
// GenericInternalServerCustomError creates a generic internal server error response.
// It returns a 500 Internal Server Error with basic error details.
func GenericInternalServerCustomError() *ErrorResponse {
	return &ErrorResponse{
		Status: &Status{
			HttpStatus: http.StatusInternalServerError,
			GrpcStatus: int(codes.Internal),
		},
		Problem: &Problem{
			Type:  oaerrors.ErrCodeUnexpected.GetCode(),
			Title: oaerrors.ErrCodeUnexpected.GetMessage(),
			// Instance: "about:blank",
		},
	}
}

// NewInternalServerError creates a new ErrorResponse for internal server errors (500)
// with generic error details. This is typically used when we want to hide implementation
// details from the client while still indicating a server-side problem occurred.
func NewInternalServerError() *ErrorResponse {
	return &ErrorResponse{
		Status: &Status{
			HttpStatus: http.StatusInternalServerError,
			GrpcStatus: int(codes.Internal),
		},
		Problem: &Problem{
			Type:  oaerrors.ErrCodeUnexpected.GetCode(),
			Title: oaerrors.ErrCodeUnexpected.GetMessage(),
			// Detail: "An internal error has occurred",
		},
	}
}

// ToAny converts a Go interface{} to *anypb.Any
func toAny(v interface{}) (*anypb.Any, error) {
	// Handle nil case
	if v == nil {
		return nil, nil
	}

	// Check if it's already an Any
	if any, ok := v.(*anypb.Any); ok {
		return any, nil
	}

	// Check if it's a proto.Message
	if msg, ok := v.(proto.Message); ok {
		any, err := anypb.New(msg)
		if err != nil {
			return nil, err
		}
		// Override the type URL if it's our custom error type
		if _, ok := msg.(*common.Error); ok {
			any.TypeUrl = "type.oa.tech/oa.common.v0.Error" // Use your desired type URL
		}
		return any, nil
	}

	return nil, fmt.Errorf("unsupported type: %T", v)
}

// FromAny converts *anypb.Any back to Go types
func fromAny(any *anypb.Any) (interface{}, error) {
	if any == nil {
		return nil, nil
	}

	// Try to unmarshal as error details first
	switch {
	case any.MessageIs(&epb.BadRequest{}):
		v := &epb.BadRequest{}
		if err := any.UnmarshalTo(v); err != nil {
			return nil, fmt.Errorf("failed to unmarshal BadRequest: %w", err)
		}
		return errorDetailToMap(v)

	case any.MessageIs(&epb.PreconditionFailure{}):
		v := &epb.PreconditionFailure{}
		if err := any.UnmarshalTo(v); err != nil {
			return nil, fmt.Errorf("failed to unmarshal PreconditionFailure: %w", err)
		}
		return errorDetailToMap(v)

	case any.MessageIs(&epb.QuotaFailure{}):
		v := &epb.QuotaFailure{}
		if err := any.UnmarshalTo(v); err != nil {
			return nil, fmt.Errorf("failed to unmarshal QuotaFailure: %w", err)
		}
		return errorDetailToMap(v)

	case any.MessageIs(&epb.RetryInfo{}):
		v := &epb.RetryInfo{}
		if err := any.UnmarshalTo(v); err != nil {
			return nil, fmt.Errorf("failed to unmarshal RetryInfo: %w", err)
		}
		return errorDetailToMap(v)

	case any.MessageIs(&epb.DebugInfo{}):
		v := &epb.DebugInfo{}
		if err := any.UnmarshalTo(v); err != nil {
			return nil, fmt.Errorf("failed to unmarshal DebugInfo: %w", err)
		}
		return errorDetailToMap(v)
	}

	return nil, fmt.Errorf("unsupported type: %T", any.Value)
}

// errorDetailToMap converts error detail protos to map[string]interface{}
func errorDetailToMap(detail proto.Message) (map[string]interface{}, error) {
	switch v := detail.(type) {
	case *epb.BadRequest:
		violations := make([]map[string]interface{}, len(v.GetFieldViolations()))
		for i, violation := range v.GetFieldViolations() {
			violations[i] = map[string]interface{}{
				"field":       violation.GetField(),
				"description": violation.GetDescription(),
			}
		}
		return map[string]interface{}{
			"field_violations": violations,
		}, nil

	case *epb.PreconditionFailure:
		violations := make([]map[string]interface{}, len(v.GetViolations()))
		for i, violation := range v.GetViolations() {
			violations[i] = map[string]interface{}{
				"type":        violation.GetType(),
				"subject":     violation.GetSubject(),
				"description": violation.GetDescription(),
			}
		}
		return map[string]interface{}{
			"violations": violations,
		}, nil

	case *epb.QuotaFailure:
		violations := make([]map[string]interface{}, len(v.GetViolations()))
		for i, violation := range v.GetViolations() {
			violations[i] = map[string]interface{}{
				"subject":     violation.GetSubject(),
				"description": violation.GetDescription(),
			}
		}
		return map[string]interface{}{
			"violations": violations,
		}, nil

	case *epb.RetryInfo:
		return map[string]interface{}{
			"retry_delay": v.GetRetryDelay().AsDuration().String(),
		}, nil

	case *epb.DebugInfo:
		return map[string]interface{}{
			"stack_entries": v.GetStackEntries(),
			"detail":        v.GetDetail(),
		}, nil
	}

	return nil, fmt.Errorf("unsupported type: %T", detail)
}
