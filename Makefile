SERVICE_NAME := gateway
MAIN_PACKAGE_PATH := ./cmd
DB_PATH := postgres://user:pass@localhost:5432/$(SERVICE_NAME)

## tidy: tidy the application
.PHONY: tidy
tidy:
	go mod tidy

## build: build the application
.PHONY: build
build:
	go build -tags=viper_bind_struct -o=./bin/${SERVICE_NAME} ${MAIN_PACKAGE_PATH}/${SERVICE_NAME}/*.go

## run: run the  application
.PHONY: run
run: build
	go run -tags=viper_bind_struct ${MAIN_PACKAGE_PATH}/${SERVICE_NAME}/*.go

## serve: run the application with hot reloading
.PHONY: serve
serve:
	export SERVICE_NAME=${SERVICE_NAME} && \
	air -c .air.toml