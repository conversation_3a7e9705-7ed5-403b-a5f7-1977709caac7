#!/bin/bash

set -ex
ENV_NAME=$1
GITHUB_PAT=$2
OVERARCHING_ENV=$3
PUBLIC_URL=$4
PRIVATE_URL=$5
CALLBACK_URL=$6
RAPIDSHORT_URL=$7

# construct GCR_URI based on ENV_NAME
if [ "$ENV_NAME" == "prod" ]; then
    GCR_URI="asia-south1-docker.pkg.dev/oneassure-prod/oneassure"
else
    GCR_URI="asia-south1-docker.pkg.dev/oneassure-non-prod/oneassure-non-prod"
fi

APP_NAME="gateway"

TAG=$APP_NAME-$(git rev-parse --abbrev-ref HEAD)-$(git describe --always)

export GITHUB_PAT=$2

docker build \
    --secret id=GITHUB_PAT \
    --target builder \
    -t oa-$APP_NAME-builder:$TAG \
    . || exit 1

docker build \
    --secret id=GITHUB_PAT \
    -t $GCR_URI/oa-$APP_NAME-$ENV_NAME:$TAG \
    . || exit 1

docker push $GCR_URI/oa-$APP_NAME-$ENV_NAME:$TAG || exit 1

# kubectl create secret generic oa-$client-$env-app-secret -n oa-apps --dry-run=client --from-file=.env.local=.env.local -o yaml | kubectl apply -f -

(
    sed \
        -e "s/{{env_name}}/$ENV_NAME/g" \
        -e "s/{{image_id}}/$TAG/g" \
        -e "s/{{app_name}}/$APP_NAME/g" \
        -e "s/{{overarching_env}}/$OVERARCHING_ENV/g" \
        -e "s/{{public_url}}/$PUBLIC_URL/g" \
        -e "s/{{private_url}}/$PRIVATE_URL/g" \
        -e "s|{{callback_url}}|$CALLBACK_URL|g" \
        -e "s|{{gcr_uri}}|$GCR_URI|g" \
        -e "s|{{rapidshort_url}}|$RAPIDSHORT_URL|g" \
        k8s/manifest.yaml | kubectl apply -f -
) || exit 1

kubectl rollout status deployment/oa-$APP_NAME-deployment-$ENV_NAME -n oa-$ENV_NAME || exit 1

set +ex