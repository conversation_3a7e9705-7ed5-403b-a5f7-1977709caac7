# Stage 1: Build stage
FROM golang:1.23.10-alpine3.21 AS builder

# Set working directory
WORKDIR /app

# Install git
RUN apk add --no-cache git

RUN --mount=type=secret,id=GITHUB_PAT,env=GITHUB_PAT printenv | grep GITHUB_PAT

# Add GitHub authentication during build (token will not be in final image)

RUN --mount=type=secret,id=GITHUB_PAT,env=GITHUB_PAT \
    git config --global url."https://${GITHUB_PAT}:<EMAIL>/".insteadOf "https://github.com/"

# Copy only go mod files first
COPY go.mod go.sum ./

# Download dependencies with GitHub authentication
RUN go mod download

# Copy only necessary directories
COPY cmd ./cmd
COPY internal ./internal

# Build with cache mounting and all optimizations
ENV CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64

RUN --mount=type=cache,target=/root/.cache/go-build \
    go build -tags=viper_bind_struct \
    -ldflags="-w -s" \
    -o bin/gateway \
    ./cmd/gateway/*.go

# Stage 2: Final stage
FROM alpine:3.21

# Add only essential packages
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user for security
RUN adduser -D -H -h /app appuser
USER appuser

WORKDIR /app

# Copy only the binary
COPY --from=builder /app/bin/gateway .

# Expose port and set entrypoint
EXPOSE 8080

ENTRYPOINT ["./gateway"] 