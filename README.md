# OA Gateway Service

## Pre-commit Setup

This project uses pre-commit hooks to ensure code quality and consistency. Follow these steps to set up pre-commit:

### Prerequisites

- Python 3.x
- pip (Python package installer)

### Installation

1. Install pre-commit and Commitizen using pip:

```bash
pip install pre-commit commitizen
```

2. Navigate to the project root directory:

```bash
cd oa-gateway-svc
```

3. Install the pre-commit hooks:

```bash
pre-commit install
pre-commit install --hook-type commit-msg
pre-commit install --hook-type pre-push
```

4. (Optional) Run the hooks against all files:

```bash
pre-commit run --all-files
```

### Usage

Once installed, pre-commit will automatically run the configured hooks before each commit. If any hook fails, the commit will be aborted, allowing you to fix the issues before committing.

To temporarily bypass the hooks (not recommended for regular use):

```bash
git commit -m "Your message" --no-verify
```

### Updating Hooks

To update the pre-commit hooks to the latest versions:

```bash
pre-commit autoupdate
```

## Commit Messages

This project uses [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages, enforced through pre-commit hooks. The pre-commit configuration includes Co<PERSON><PERSON>zen, which will automatically check your commit messages for compliance with the standard format.

The commit message format should be:

```
type(optional scope): description

[optional body]

[optional footer(s)]
```

Common types include:

- feat: A new feature
- fix: A bug fix
- docs: Documentation changes
- style: Code style changes (formatting, etc.)
- refactor: Code changes that neither fix bugs nor add features
- test: Adding or modifying tests
- chore: Changes to the build process or auxiliary tools
