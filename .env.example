# Service Name
SERVICE_NAME=

# Environment
# Possible Values : dev,prod ...
ENVIRONMENT=dev

# OTEL Endpoint
OTLP_COLLECTOR_GRPC=
OTLP_COLLECTOR_HTTP=

# HTTP Public Port
HTTP_PUBLIC_PORT=

# HTTP Admin Port
HTTP_ADMIN_PORT=

# HTTP API Port
HTTP_API_PORT=

# HTTP Rapidshort Port
HTTP_RAPIDSHORT_PORT=

# API Versions
MIN_VERSION=
MAX_VERSION=

# Allow CORS Origins
CORS_ORIGINS=

# Allow Admin CORS Origins
ADMIN_CORS_ORIGINS=

# JWT Public Key URL
JWT_PUBLIC_KEY_URL=

AUTH_URL=
AUTH_PORT=

FORMS_URL=
FORMS_PORT=

SALES_URL=
SALES_PORT=

RECOMMENDATION_URL=
RECOMMENDATION_PORT=

RAPIDSHORT_URL=
RAPIDSHORT_PORT=